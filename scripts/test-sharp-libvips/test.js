const fs = require('node:fs');
const { join: pathJoin, dirname } = require('node:path');
const sharp = require('../../apps/app-malou-api/node_modules/sharp');

const main = async () => {
    // check that libvips is properly configured (with libheif, etc) to decode common picture
    // formats
    const fileNames = ['heif_hevc_10bits.heic', 'heif_hevc_8bits.heic', 'jpeg_8bits.jpg', 'png_8bits_alpha.png'];
    for (const fileName of fileNames) {
        const path = pathJoin(__dirname, fileName);
        const source = fs.readFileSync(path);
        await sharp(source)
            .rotate() // EXIF rotation
            .resize(200)
            .jpeg({ progressive: true, quality: 50 })
            .toFile('/dev/null');
    }
};

main();
