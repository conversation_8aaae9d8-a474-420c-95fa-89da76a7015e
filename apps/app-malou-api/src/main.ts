import 'reflect-metadata';

import ':env';

import ':di';
import * as Sentry from '@sentry/node';
import Agendash from 'agendash';
import chalk from 'chalk';
import connectRedis from 'connect-redis';
import cors from 'cors';
import express, { Application } from 'express';
import basicAuth from 'express-basic-auth';
import session from 'express-session';
import helmet from 'helmet';
import { container } from 'tsyringe';

import { Config } from ':config';
import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { MalouError } from ':helpers/classes/malou-error';
import { checkConfigAndLogValidationErrors } from ':helpers/config-checker';
import { errorHandlerMiddleware, getHttpStatusFromMalouError } from ':helpers/error-handler.middleware';
import { exitProcess } from ':helpers/exit-process';
import { logger } from ':helpers/logger';
import { MemoryManager } from ':helpers/memory-manager/memory-manager';
import { addBodyParserMiddleware } from ':plugins/body-parser.middleware';
import { setupHttpRequestLogger } from ':plugins/http-logger';
import { addMaintenanceHeaderMiddleware } from ':plugins/maintenance-header.middleware';
import { addMetricsMiddleware } from ':plugins/metrics.middleware';
import * as myPassport from ':plugins/passport';
import * as redis from ':plugins/redis-db';
import { initConfig } from ':plugins/sentry';
import { initQueueMessageProducers } from ':queues/queues';

import packageJson from '../package.json';
import { api } from './api';
import { maloupeApi } from './maloupe-api';

checkConfigAndLogValidationErrors();
initQueueMessageProducers();

if (!['test', 'local-tests'].includes(process.env.NODE_ENV)) {
    require('./plugins/db');
}

const RedisStore = connectRedis(session);

export async function initApplication(): Promise<Application> {
    const app: Application = express();

    logger.info(`Starting server with NODE_ENV = ${chalk.bold.cyan(process.env.NODE_ENV)}`);
    logger.info(
        chalk.bold.green(`
    Will send emails (using mailgun's api): ${Config.settings.sendEmail}
    Will scrap pagesjaunes (using residential ips): ${Config.platforms.pagesjaunes.scrapWebsite}
    Will fetch keywords rankings (using gmaps api): ${Config.geolocation.fetchRankings}
    Using facebook app id : ${Config.platforms.facebook.api.appId} (${
        Config.platforms.facebook.api.appId === '1313739148973155' ? 'MalouApp Test' : 'MalouApp'
    })`)
    );

    if (process.env.NODE_ENV === 'production' && process.env.I_AM_A !== 'developer') {
        Sentry.init(initConfig({ serviceName: 'api', app }));
    }

    // RequestHandler creates a separate execution context, so that all
    // transactions/spans/breadcrumbs are isolated across requests
    app.use(
        Sentry.Handlers.requestHandler({
            user: ['_id', 'email', 'name', 'lastname'], // Sentry will look for user in req.user
        })
    );

    // TracingHandler creates a trace for every incoming request
    app.use(Sentry.Handlers.tracingHandler());

    // Helmet
    app.use(helmet());

    // cors
    const allowedOrigins = [process.env.BASE_URL, process.env.V3_BASE_URL ?? '', process.env.MALOUPE_URL ?? ''];
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'local') {
        allowedOrigins.push(...['https://*.cloudfront.net', 'http://localhost:4200', 'http://localhost:4201']);
    }

    app.use(
        cors({
            origin: allowedOrigins,
            credentials: true,
            exposedHeaders: ['X-Maintenance-Status'],
        })
    );
    app.options('*', cors());

    app.use(function (req, res, next) {
        res.setHeader(
            'Content-Security-Policy',
            "default-src 'self'; base-uri 'self'; font-src 'self' https: data:;form-action 'self'; frame-ancestors 'self'; child-src blob:; worker-src blob:; img-src 'self' data: https://heapanalytics.com *.contentsquare.net; object-src 'none'; script-src 'self' https://cdn.us.heap-api.com https://heapanalytics.com 'unsafe-inline' 'unsafe-eval' t.contentsquare.net app.contentsquare.com; script-src-attr 'none'; style-src 'self' https: 'unsafe-inline'; connect-src https://c.us.heap-api.com https://heapanalytics.com *.contentsquare.net; upgrade-insecure-requests"
        );

        next();
    });

    addBodyParserMiddleware(app);
    addMetricsMiddleware(app);
    addMaintenanceHeaderMiddleware(app);

    const asyncLocalStorageService = container.resolve(AsyncLocalStorageService);
    app.use(function (req, res, next) {
        asyncLocalStorageService.updateOrCreateStoreAndRun({}, next);
    });

    setupHttpRequestLogger(app);

    // Passport
    myPassport.initialize(app);

    // Health check
    app.get('/amen', function (req, res) {
        res.json({
            name: packageJson.name,
            version: packageJson.version,
            msg: 'Amen to that boys.',
            ip: req.ip,
            branch: Config.branchName,
        });
    });

    app.get('/', function (req, res) {
        res.json({
            msg: 'Server Up',
        });
    });

    app.use(
        session({
            store: new RedisStore({ client: redis.client }),
            secret: 'frid@y1993',
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: false, // if true only transmit cookie over https
                httpOnly: false, // if true prevent client side JS from reading the cookie
                maxAge: Config.settings.sessionExpireTimeMs, // session max age in miliseconds
            },
            rolling: false,
        })
    );

    app.use(Config.settings.apiRoute.v1, api());
    app.use(Config.settings.apiRoute.maloupe, maloupeApi());

    if (!['test', 'local-tests'].includes(process.env.NODE_ENV)) {
        // supertest starts server in parallel so we cannot listen on port 3000.
        app.use(
            '/dash',
            basicAuth({
                users: {
                    admin: String(process.env.AGENDASH_PASSWORD ?? 'frid@yagain'),
                },
                challenge: true,
            }),
            Agendash(await container.resolve(AgendaSingleton).getInstance())
        );
        // Agendash jobs: localhost:3000/dash
    }

    // This middleware is an Express error handler that should be between the API routes
    // middleware (`app.use(api())`) and our custom error handler middleware.
    // By default Sentry ignores errors that have a field `code` set to an integer lower
    // than 500, but that’s not how MalouError works.
    app.use(
        Sentry.Handlers.errorHandler({
            shouldHandleError: (error: unknown): boolean => !(error instanceof MalouError) || getHttpStatusFromMalouError(error) >= 500,
        })
    );

    // Error handling
    app.use(errorHandlerMiddleware);

    // Optional fallthrough error handler
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    app.use(function onError(err, req, res, next) {
        // The error id is attached to `res.sentry` to be returned
        // and optionally displayed to the user for support.
        res.statusCode = 500;
        res.end(`${res.sentry}\n`);
    });

    process.on('unhandledRejection', (reason, promise) => {
        logger.error(`Unhandled Rejection`, {
            app: 'api',
            reason,
            promise: JSON.stringify(promise),
        });

        Sentry.captureException(reason);

        void exitProcess(1);
    });

    process.on('uncaughtException', (err, origin) => {
        logger.error(`Uncaught Exception`, {
            app: 'api',
            err,
            origin,
        });

        Sentry.captureException(err);

        void exitProcess(1);
    });

    ['SIGINT', 'SIGTERM'].forEach((signal) =>
        process.on(signal, () => {
            logger.info('Process exited on signal', {
                app: 'api',
                signal,
            });

            void exitProcess(0);
        })
    );

    // Run server
    const port = process.env.PORT ?? 3000;
    app.set('port', port);

    if (!['tests', 'local-tests'].includes(process.env.NODE_ENV)) {
        app.listen(port, () => logger.info(`Running on localhost:${port}`));
    }

    container.resolve(MemoryManager).startMonitoring();

    return app;
}
