import { AiInteractionRelatedEntityCollection } from '@malou-io/package-utils';

export type ReviewsSemanticAnalysisOverviewPayload = {
    collectionId?: string;
    collection: AiInteractionRelatedEntityCollection;
    language: string;
    restaurantsData: RestaurantReviewSemanticAnalysisOverviewPayload[];
};

export type RestaurantReviewSemanticAnalysisOverviewPayload = {
    restaurantName: string;
    reviewsSemanticAnalysis: ReviewSemanticAnalysis[];
};

export type ReviewSemanticAnalysis = {
    category: string;
    sentiment: string;
    segment: string;
};

export type ReviewsSemanticAnalysisOverviewResponse = {
    semanticAnalysisResult: string;
    aiInteractionDetails: AIInteractionDetails;
};

export type AIInteractionDetails = {
    promptText: string;
    completionText: string;
    completionTokenCount: number;
    promptTokenCount: number;
    type: string;
    responseTimeInMilliseconds: number;
};
