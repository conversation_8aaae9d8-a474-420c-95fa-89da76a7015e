import { MalouError } from ':helpers/classes/malou-error';

export interface AlertsService {
    sendAlert(
        feature: AlertFeature,
        error: MalouError,
        endpoint: string,
        description: string,
        restaurantName: string
    ): Promise<{ success: boolean }>;
}

export enum AlertFeature {
    AUTO_REPLY_TO_REVIEW = 'AUTO_REPLY_TO_REVIEW',
    FETCH_KEYWORDS_VOLUME_MONTHLY = 'FETCH_KEYWORDS_VOLUME_MONTHLY',
    GENERATE_KEYWORDS = 'GENERATE_KEYWORDS',
    GET_YELP_LOCATION_OVERVIEW_DATA = 'GET_YELP_LOCATION_OVERVIEW_DATA',
    GET_TIKTOK_LOCATION_OVERVIEW_DATA = 'GET_TIKTOK_LOCATION_OVERVIEW_DATA',
    GET_TRIPADVISOR_LOCATION_OVERVIEW_DATA = 'GET_TRIPADVISOR_LOCATION_OVERVIEW_DATA',
    GET_DELIVEROO_DRN_ID = 'GET_DELIVEROO_DRN_ID',
    MEMORY_USAGE = 'MEMORY_USAGE',
    MONTHLY_SAVE_ROI_INSIGHTS = 'MONTHLY_SAVE_ROI_INSIGHTS',
    PUBLISH_TRIPADVISOR_REPLY = 'PUBLISH_TRIPADVISOR_REPLY',
    INITIALIZE_STORY = 'INITIALIZE_STORY',
    PUBLISH_FB_POST = 'PUBLISH_FB_POST',
    PUBLISH_FB_STORY = 'PUBLISH_FB_STORY',
    PUBLISH_IG_POST = 'PUBLISH_IG_POST',
    PUBLISH_IG_STORY = 'PUBLISH_IG_STORY',
    PUBLISH_GMB_POST = 'PUBLISH_GMB_POST',
    PUBLISH_POST = 'PUBLISH_POST',
    RETRY_REPLY_TO_REVIEW = 'RETRY_REPLY_TO_REVIEW',
    ROI_ACTIVATED_RESTAURANTS = 'ROI_ACTIVATED_RESTAURANTS',
    UBEREATS_PROMOTION_AMOUNT = 'UBEREATS_PROMOTION_AMOUNT',
    YEXT_DELETE_LOCATION = 'YEXT_DELETE_LOCATION',
    /** PLEASE KEEP ALPHABETICAL ORDER */
}
