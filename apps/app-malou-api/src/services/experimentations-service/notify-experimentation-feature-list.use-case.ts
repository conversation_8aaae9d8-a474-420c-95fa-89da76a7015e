import { FeatureDefinition } from '@growthbook/growthbook';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class NotifyExperimentationFeatureListUseCase {
    constructor(
        private readonly _experimentationService: ExperimentationService,
        private readonly _slackService: SlackService
    ) {}

    async execute(): Promise<void> {
        const features = await this._experimentationService.listFeatures();
        logger.info('[NotifyExperimentationFeatureListUseCase] Release features', features);

        this._slackService.sendMessage({
            text: this._formatMessageForSlack(features),
            channel: SlackChannel.EXPERIMENTATION_ALERTS,
        });
    }

    private _formatMessageForSlack(features: Record<string, FeatureDefinition<any>>): string {
        const releaseFeatures = Object.keys(features).filter((feature) => feature.startsWith('release-'));

        const growthbookBaseUrl = 'https://experimentation.malou.io:3001/features/';

        const defaultValueTrueList = releaseFeatures.filter((feature) => !!features[feature].defaultValue);
        const defaultValueFalseList = releaseFeatures.filter((feature) => !features[feature].defaultValue);

        const defaultValueTrueListMessage =
            defaultValueTrueList.length > 0
                ? defaultValueTrueList
                      .sort()
                      .map((releaseFeature) => `${releaseFeature} (<${growthbookBaseUrl}/${releaseFeature}|lien>)`)
                      .join('\n')
                : "Y'a rien ici !! :shocked_face_with_exploding_head:";
        const defaultValueFalseListMessage =
            defaultValueFalseList.length > 0
                ? defaultValueFalseList
                      .sort()
                      .map((releaseFeature) => `${releaseFeature} (<${growthbookBaseUrl}/${releaseFeature}|lien>)`)
                      .join('\n')
                : "Y'a rien ici !! :shocked_face_with_exploding_head:";

        const title = '📜 *Liste des features \`release-\` non-archivées*';

        const valueTrueTitle = `*Features avec* \`defaultValue: true\` *sur ${Config.env}*`;
        const valueFalseTitle = `*Features avec* \`defaultValue: false\` *sur ${Config.env}*`;
        const releaseFeatureList = `${valueTrueTitle}\n\n${defaultValueTrueListMessage}\n\n${valueFalseTitle}\n\n${defaultValueFalseListMessage}`;

        const footer =
            defaultValueFalseList.length + defaultValueTrueList.length > 0 ? '*Pensez à faire le ménage !* 🧹' : '*Félicitations !* :tada:';

        const message = `${title}\n\n${releaseFeatureList}\n\n${footer}`;

        return message;
    }
}
