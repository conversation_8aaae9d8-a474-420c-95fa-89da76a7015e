import { randomUUID } from 'node:crypto';
import { Readable } from 'node:stream';
import { inject, singleton } from 'tsyringe';

import { guessMimeTypeFromExtension, MimeType, ProcessingMediaErrorCode } from '@malou-io/package-utils';

import { guessImageMimeType } from ':helpers/guess-image-type';
import { logger } from ':helpers/logger';
import { UploadImageService } from ':modules/media/use-cases/upload-media-v2/services/upload-image.service';
import { UploadVideoService } from ':modules/media/use-cases/upload-media-v2/services/upload-video.service';
import { ProcessingMediasRepository } from ':modules/processing-medias/processing-medias.repository';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { metricsService } from ':services/metrics.service';

const supportedImageMimeTypes = [MimeType.IMAGE_HEIC, MimeType.IMAGE_JPEG, MimeType.IMAGE_PNG];

export const imageGuessedTypesCounter = metricsService.getMeter().createCounter<{
    type: MimeType | undefined;
}>('media.upload.image.guessed_types.count', {
    description: 'Results of guessImageMimeType',
});

const MIME_TYPE_TO_EXTENSION: Record<MimeType.IMAGE_JPEG | MimeType.IMAGE_PNG | MimeType.IMAGE_HEIC, string> = {
    [MimeType.IMAGE_PNG]: 'png',
    [MimeType.IMAGE_JPEG]: 'jpeg',
    [MimeType.IMAGE_HEIC]: 'heic',
};

@singleton()
export class UploadMediaV2UseCase {
    constructor(
        private readonly _uploadImageService: UploadImageService,
        private readonly _uploadVideoService: UploadVideoService,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService,
        private readonly _processingMediasRepository: ProcessingMediasRepository
    ) {}

    async execute(params: {
        /**
         * A user-provided file name. We don’t rely on it to determine the file type
         * (in other words, we don’t check the extension of the file name) but we display
         * it in many places in the UI.
         */
        userFileName: string;
        source: Readable;
        restaurantId: string;
        folderId?: string;
        /** Can make the processing MUCH faster if specified. */
        additionalInfo?: {
            videoWidthInPixels: number;
            videoHeightInPixels: number;
            videoDurationInMilliseconds: number;
        };
    }): Promise<{ processingMediaId: string }> {
        const { userFileName, source: _source, restaurantId, additionalInfo } = params;
        const uuid = randomUUID();
        logger.info('[UploadMediaV2] Start', { userFileName, restaurantId, additionalInfo, uuid });

        const { imageMimeType, readable: source } = await guessImageMimeType(_source);
        // At this point the original `_source` stream should no longer be used directly.
        // We have to use the returned `source` stream instead.
        logger.info('[UploadMediaV2] Guessed imageMimeType', { imageMimeType });
        imageGuessedTypesCounter.add(1, { type: imageMimeType ?? undefined });

        let sourceExtension: string | undefined;
        let sourceMimeType: MimeType | undefined;
        if (imageMimeType) {
            sourceExtension = MIME_TYPE_TO_EXTENSION[imageMimeType];
            sourceMimeType = imageMimeType;
        } else {
            sourceExtension = userFileName.split('.').at(-1);
            if (sourceExtension) {
                sourceMimeType = guessMimeTypeFromExtension(sourceExtension) ?? undefined;
            }
        }

        const sourceS3Key = `media-upload/${uuid}/source${sourceExtension ? `.${sourceExtension}` : ''}`;
        await this._distantStorageService.saveFromReadable(sourceS3Key, source, { contentType: sourceMimeType });

        const processingMedia = await this._processingMediasRepository.createInProgressDocument();

        // We do not await this, the processing status can be poll form ProcessingMedias collection
        this._processMedia({
            restaurantId: params.restaurantId,
            processingMediaId: processingMedia._id.toString(),
            uuid,
            imageMimeType,
            additionalInfo: params.additionalInfo,
            sourceS3Key,
            userFileName: params.userFileName,
        }).catch((error) => logger.info('[UploadMediaV2] Error processing media', { error }));

        return { processingMediaId: processingMedia._id.toString() };
    }

    private async _processMedia(params: {
        processingMediaId: string;
        uuid: string;
        imageMimeType: MimeType.IMAGE_JPEG | MimeType.IMAGE_PNG | MimeType.IMAGE_HEIC | null;
        userFileName: string;
        sourceS3Key: string;
        restaurantId: string;
        folderId?: string;
        additionalInfo?: {
            videoWidthInPixels: number;
            videoHeightInPixels: number;
            videoDurationInMilliseconds: number;
        };
    }): Promise<void> {
        const { processingMediaId, uuid, imageMimeType, userFileName, restaurantId, additionalInfo } = params;
        const source = this._distantStorageService.getReadable(params.sourceS3Key);
        if (imageMimeType && supportedImageMimeTypes.includes(imageMimeType)) {
            logger.info('[UploadMediaV2] Launch image import...');

            const res = await this._uploadImageService.execute({
                source,
                imageMimeType,
                uuid,
                restaurantId,
                folderId: params.folderId,
                userFileName,
            });
            if (res.isErr()) {
                console.log('res.error', res.error);
                await this._processingMediasRepository.setInError(processingMediaId, ProcessingMediaErrorCode.INVALID_FILE);
            } else {
                await this._processingMediasRepository.setInSuccess(processingMediaId, res.value.mediaId);
            }
        } else {
            logger.info('[UploadMediaV2] Launch video import...');

            const res = await this._uploadVideoService.execute({
                source,
                uuid,
                restaurantId,
                folderId: params.folderId,
                userFileName,
                additionalInfo,
            });

            if (res.isErr()) {
                await this._processingMediasRepository.setInError(processingMediaId, ProcessingMediaErrorCode.INVALID_FILE);
            } else {
                await this._processingMediasRepository.setInSuccess(processingMediaId, res.value.mediaId);
            }
        }
    }
}
