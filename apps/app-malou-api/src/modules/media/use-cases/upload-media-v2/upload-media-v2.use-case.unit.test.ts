import { CreateJobRequest, CreateJobResponse, ListJobsRequest, ListJobsResponse } from 'aws-sdk/clients/mediaconvert';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import fs from 'node:fs';
import pathFunctions from 'node:path';
import { Readable } from 'node:stream';
import sharp from 'sharp';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { ProcessingMediaErrorCode, ProcessingMediaStatus, retry, RetryError, TimeInMilliseconds } from '@malou-io/package-utils';

import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { AwsMediaConvert, AwsMediaConvertInterface } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert';
import { listKeysForFrameCapture } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert.service';
import { MediaConvertJobTracking } from ':modules/media/use-cases/upload-media-v2/media-convert-job-tracking';
import { UploadMediaV2UseCase } from ':modules/media/use-cases/upload-media-v2/upload-media-v2.use-case';
import { ProcessingMediasRepository } from ':modules/processing-medias/processing-medias.repository';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';
import { FakeDistantStorageService } from ':services/distant-storage-service/implementations/fake-distant-storage-service';

const repoRootDir = pathFunctions.join(__dirname, '..', '..', '..', '..', '..', '..', '..');
const picturesDir = pathFunctions.join(repoRootDir, 'scripts', 'test-sharp-libvips');
const videosDir = pathFunctions.join(__dirname, '..', '..', 'tests');

class FakeAwsMediaConvert implements AwsMediaConvertInterface {
    public constructor(private readonly _onJobCreation: (req: CreateJobRequest) => void) {}

    async listJobs(_req: ListJobsRequest): Promise<ListJobsResponse> {
        return { Jobs: [] };
    }

    async createJob(req: CreateJobRequest): Promise<CreateJobResponse> {
        this._onJobCreation(req);
        return {};
    }
}

const waitUntilMediaProcessed = async (processingMediaId: string): Promise<Result<Media, ProcessingMediaErrorCode.INVALID_FILE>> => {
    const processingMediasRepository = container.resolve(ProcessingMediasRepository);
    const mediasRepository = container.resolve(MediasRepository);

    const res = await retry(() => processingMediasRepository.findById(processingMediaId), {
        attempts: 60,
        isSuccess: (r) => r?.status === ProcessingMediaStatus.SUCCESS,
        shouldRetrySuccess: (r) => r?.status === ProcessingMediaStatus.IN_PROGRESS,
        backoffStrategy: (attempt) => 2 ** attempt * 50 * TimeInMilliseconds.MILLISECOND,
        minDelayInMs: 100 * TimeInMilliseconds.MILLISECOND,
        maxDelayInMs: TimeInMilliseconds.SECOND,
    });

    if (res.isErr()) {
        assert(res.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS);
        expect(res.error.originalValue?.status).toBe(ProcessingMediaStatus.ERROR);
        return err(ProcessingMediaErrorCode.INVALID_FILE);
    }

    expect(res.value?.mediaId).toBeDefined();
    assert(res.value?.mediaId);
    expect(res.value?.status).toBe(ProcessingMediaStatus.SUCCESS);
    const media = await mediasRepository.findById(res.value.mediaId);
    expect(media).toBeDefined();
    assert(media);
    return ok(media);
};

describe('UploadMediaV2UseCase', () => {
    beforeEach(() => {
        container.reset();
    });

    it('processes and uploads images', async () => {
        const s3 = new FakeDistantStorageService();
        expect(s3.objects.size).toBe(0);

        container.register<DistantStorageService>(AwsS3DistantStorageService, { useValue: s3 });
        const uploadMediaV2UseCase = container.resolve(UploadMediaV2UseCase);

        const sourcePath = pathFunctions.join(picturesDir, 'jpeg_8bits.jpg');

        // the user-provided file extension is incorrect but we don’t rely on it to
        // determine the type of the file.
        const result = await uploadMediaV2UseCase.execute({
            userFileName: 'choux à la crème.zip',
            source: fs.createReadStream(sourcePath),
            restaurantId: newDbId().toString(),
        });

        let media: Media;
        {
            const res = await waitUntilMediaProcessed(result.processingMediaId);
            assert(res.isOk());
            media = res.value;
        }

        expect(s3.objects.size).toBe(5);
        assert(media);
        expect(media.name).toBe('choux à la crème.zip');
        assert(media.storedObjects);

        {
            const source = await fs.promises.readFile(sourcePath);
            expect(source.equals(await s3.getBuffer(media.storedObjects.original.key))).toBe(true);
        }

        {
            const meta = await sharp(await s3.getBuffer(media.storedObjects.thumbnail256Outside.key)).metadata();
            expect(meta.width).toBe(341);
            expect(meta.height).toBe(256);
            expect(meta.format).toBe('jpeg');
        }
    });

    // skipped because this test requires a Redis server, and there is no Redis server on the
    // CI currently.
    it.skip('fails with an invalid file whose first bytes don’t look like a picture', async () => {
        const s3 = new FakeDistantStorageService();
        container.register<DistantStorageService>(AwsS3DistantStorageService, { useValue: s3 });
        const awsMediaConvert = new FakeAwsMediaConvert(async (req) => {
            assert(req.UserMetadata);
            const uuid = req.UserMetadata.malouUploadV2Uuid;
            expect(typeof uuid).toBe('string');
            await MediaConvertJobTracking.publish(uuid, {
                type: 'errored',
                awsCode: 1010,
                awsMessage: 'Input Error',
            });
        });
        container.register<AwsMediaConvertInterface>(AwsMediaConvert, { useValue: awsMediaConvert });
        const uploadMediaV2UseCase = container.resolve(UploadMediaV2UseCase);

        const sourcePath = pathFunctions.join(repoRootDir, 'package.json');
        const result = await uploadMediaV2UseCase.execute({
            userFileName: 'choux à la crème.jpeg',
            source: fs.createReadStream(sourcePath),
            restaurantId: newDbId().toString(),
        });

        const res = await waitUntilMediaProcessed(result.processingMediaId);
        assert(res.isErr());
        expect(res.error).toBe(ProcessingMediaErrorCode.INVALID_FILE);
    });

    it('fails with an invalid file whose first bytes look like a valid picture', async () => {
        const s3 = new FakeDistantStorageService();
        container.register<DistantStorageService>(AwsS3DistantStorageService, { useValue: s3 });
        const uploadMediaV2UseCase = container.resolve(UploadMediaV2UseCase);

        const validJpeg = await fs.promises.readFile(pathFunctions.join(picturesDir, 'jpeg_8bits.jpg'));
        const file = Buffer.concat([validJpeg.subarray(0, 999), Buffer.from('garbage')]);
        const readable = new Readable();
        readable.push(file);
        readable.push(null);

        const { processingMediaId } = await uploadMediaV2UseCase.execute({
            userFileName: 'choux à la crème.jpeg',
            source: readable,
            restaurantId: newDbId().toString(),
        });

        const res = await waitUntilMediaProcessed(processingMediaId);
        assert(res.isErr());
        expect(res.error).toBe(ProcessingMediaErrorCode.INVALID_FILE);
    });

    // skipped because this test requires a Redis server, and there is no Redis server on the
    // CI currently.
    it.skip('works with a video', async () => {
        const s3 = new FakeDistantStorageService();
        const awsMediaConvert = new FakeAwsMediaConvert(async (req) => {
            assert(req.UserMetadata);
            assert(req.Settings.OutputGroups);
            const uuid = req.UserMetadata.malouUploadV2Uuid;
            expect(typeof uuid).toBe('string');

            await MediaConvertJobTracking.publish(uuid, {
                type: 'completed',
                outputGroups: req.Settings.OutputGroups.map((outputGroup) => {
                    assert(outputGroup.Outputs);
                    return {
                        s3keys: outputGroup.Outputs.flatMap((o): string[] => {
                            assert(o.ContainerSettings);
                            if (o.ContainerSettings.Container === 'MP4') {
                                return ['s3key.mp4'];
                            }
                            if (o.ContainerSettings?.Container === 'RAW' && o.VideoDescription?.CodecSettings?.Codec === 'FRAME_CAPTURE') {
                                return [`media-upload/${uuid}/timeline_preview_256.0000004.jpeg`];
                            }
                            return [];
                        }),
                        durationInMs: 1000,
                        width: 1920,
                        height: 1080,
                    };
                }),
            });
        });

        container.register<DistantStorageService>(AwsS3DistantStorageService, { useValue: s3 });
        container.register<AwsMediaConvertInterface>(AwsMediaConvert, { useValue: awsMediaConvert });
        const uploadMediaV2UseCase = container.resolve(UploadMediaV2UseCase);

        const udonVideoStream = fs.createReadStream(pathFunctions.join(videosDir, 'udon_240p.mp4'));
        const restaurantId = newDbId().toString();
        const { processingMediaId } = await uploadMediaV2UseCase.execute({
            userFileName: 'udon_240p.mp4',
            source: udonVideoStream,
            restaurantId,
        });

        const res = await waitUntilMediaProcessed(processingMediaId);
        assert(res.isOk());
        const media = res.value;

        expect(media.urls.original).toMatch(/^public:\/\/media-upload\/[a-f0-9-]+\/normalized\.mp4$/);
        expect(media.original?.publicUrl).toStartWith('public://');
        expect(media.thumbnail256Outside?.publicUrl).toStartWith('public://');
        expect(media.thumbnail1024Outside?.publicUrl).toStartWith('public://');
        expect(media.type).toEqual('video');
        expect(media.format).toEqual('mp4');
        expect(media.name).toEqual('udon_240p.mp4');
        expect(media.restaurantId).toEqual(restaurantId);
        expect(media.timelinePreviewFrames256h?.length).toEqual(5);
        expect(media.timelinePreviewFrames256h?.[2].key).toMatch(/^media-upload\/[a-f0-9-]+\/timeline_preview_256\.0000002\.jpeg$/);
    });

    describe('listKeysForFrameCapture', () => {
        it('generates a list of AWS S3 keys', () => {
            expect(listKeysForFrameCapture('/judas_priest/youve-got-another-thing-comin.0000012.jpeg', '.jpeg')).toStrictEqual([
                '/judas_priest/youve-got-another-thing-comin.0000000.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000001.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000002.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000003.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000004.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000005.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000006.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000007.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000008.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000009.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000010.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000011.jpeg',
                '/judas_priest/youve-got-another-thing-comin.0000012.jpeg',
            ]);
        });
    });
});
