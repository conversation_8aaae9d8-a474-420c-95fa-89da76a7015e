import { ForbiddenError, subject } from '@casl/ability';
import busboy from 'busboy';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import {
    CreateMediaAfterUploadBodyDto,
    createMediaAfterUploadBodyValidator,
    CreateVideoThumbnailBodyDto,
    createVideoThumbnailBodyValidator,
    DeleteManyMediaParamsDto,
    deleteManyMediaParamsValidator,
    DeleteManyMediaQueryDto,
    deleteManyMediaQueryValidator,
    DuplicateMediaForPublicationBodyDto,
    duplicateMediaForPublicationBodyValidator,
    DuplicateMediaForPublicationParamsDto,
    duplicateMediaForPublicationParamsValidator,
    DuplicateMediaForPublicationResponseDto,
    DuplicateRestaurantMediaBodyDto,
    duplicateRestaurantMediaBodyValidator,
    DuplicateRestaurantMediaParamsDto,
    duplicateRestaurantMediaParamsValidator,
    FetchMediaDescriptionQueryDto,
    fetchMediaDescriptionQueryValidator,
    GetCloudStorageUploadParamsBodyDto,
    getCloudStorageUploadParamsBodyValidator,
    GetMediaByIdParamsDto,
    getMediaByIdParamsValidator,
    GetMediaByIdsQueryDto,
    getMediaByIdsQueryValidator,
    GetMediaForEditionBodyDto,
    getMediaForEditionBodyValidator,
    GetMediaForEditionPathDto,
    getMediaForEditionPathValidator,
    GetMediaForEditionResponseDto,
    GetMediaThumbnailsListBodyDto,
    getMediaThumbnailsListBodyValidator,
    GetRestaurantMediasParamsDto,
    getRestaurantMediasParamsValidator,
    GetRestaurantMediasQueryDto,
    getRestaurantMediasQueryValidator,
    GetVideoInformationBodyDto,
    getVideoInformationBodyValidator,
    GetVideoInformationResponseDto,
    MediaDto,
    MoveMediaTowardsFolderBodyDto,
    moveMediaTowardsFolderBodyValidator,
    OnlyUploadMediaQueryDto,
    onlyUploadMediaQueryValidator,
    RestaurantIdParamsDto,
    restaurantIdParamsValidator,
    UpdateMediaBodyDto,
    updateMediaBodyValidator,
    UpdateTransformDataBodyDto,
    updateTransformDataBodyValidator,
    UpdateTransformDataParamsDto,
    updateTransformDataParamsValidator,
    UploadAndCreateMediaQueryDto,
    uploadAndCreateMediaQueryValidator,
    UploadMediaV2QueryDto,
    uploadMediaV2QueryValidator,
    UploadMediaV2ResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2, CaslAction, CaslSubject, MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { MediaFilters } from ':helpers/filters/media-filters';
import { logger } from ':helpers/logger';
import { Pagination } from ':helpers/pagination';
import { RequestWithPermissions } from ':helpers/utils.types';
import { DuplicateMediaUseCase } from ':modules/media/use-cases/duplicate-media/duplicate-media.use-case';
import { GetMediaForEditionUseCase } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.use-case';
import { TranscodeHeifToJpgUseCase } from ':modules/media/use-cases/transcode-heif-to-jpg/tanscode-heif-to-jpg.use-case';
import { UpdateTransformDataUseCase } from ':modules/media/use-cases/update-transform-data/update-transform-data.use-case';
import { UploadMediaV2UseCase } from ':modules/media/use-cases/upload-media-v2/upload-media-v2.use-case';

import { CreateMediaAfterUploadUseCase } from './use-cases/create-media-after-upload/create-media-after-upload.use-case';
import { CreateMediaThumbnailUseCase } from './use-cases/create-media-thumbnail/create-media-thumbnail.use-case';
import { DeleteMediasUseCase } from './use-cases/delete-medias/delete-medias.use-case';
import { DuplicateMediasUseCase } from './use-cases/duplicate-medias/duplicate-medias.use-case';
import { FetchMediaDescriptionUseCase } from './use-cases/fetch-media-description/fetch-media-description.use-case';
import { GetCloudStorageUploadParamsUseCase } from './use-cases/get-cloud-storage-upload-params/get-cloud-storage-upload-params.use-case';
import { GetMediaByIdUseCase } from './use-cases/get-media-by-id/get-media-by-id.use-case';
import { GetMediaByIdsUseCase } from './use-cases/get-media-by-ids/get-media-by-ids.use-case';
import { GetMediaThumbnailsListUseCase } from './use-cases/get-media-thumbnails-list/get-media-thumbnails-list.use-case';
import { GetRestaurantMediasPaginatedUseCase } from './use-cases/get-restaurant-medias-paginated/get-restaurant-medias-paginated.use-case';
import { GetVideoInformationUseCase } from './use-cases/get-video-information/get-video-information.use-case';
import { MoveMediaToFolderUseCase } from './use-cases/move-media-to-folder/move-media-to-folder.use-case';
import { ReplaceMediaUrlsUseCase } from './use-cases/replace-media-urls/replace-media-urls.use-case';
import { UpdateMediaUseCase } from './use-cases/update-media/update-media.use-case';
import { UploadAndCreateMediaUseCase } from './use-cases/upload-and-create-media/upload-and-create-media.use-case';
import { UploadOnlyUseCase } from './use-cases/upload-only/upload-only.use-case';

@singleton()
export class MediasController {
    constructor(
        private readonly _getCloudStorageUploadParamsUseCase: GetCloudStorageUploadParamsUseCase,
        private readonly _getRestaurantMediasPaginatedUseCase: GetRestaurantMediasPaginatedUseCase,
        private readonly _uploadAndCreateMediaUseCase: UploadAndCreateMediaUseCase,
        private readonly _uploadOnlyUseCase: UploadOnlyUseCase,
        private readonly _getMediaByIdUseCase: GetMediaByIdUseCase,
        private readonly _updateMediaUseCase: UpdateMediaUseCase,
        private readonly _replaceMediaUrlsUseCase: ReplaceMediaUrlsUseCase,
        private readonly _deleteMediasUseCase: DeleteMediasUseCase,
        private readonly _createMediaAfterUploadUseCase: CreateMediaAfterUploadUseCase,
        private readonly _duplicateMediasUseCase: DuplicateMediasUseCase,
        private readonly _duplicateMediaUseCase: DuplicateMediaUseCase,
        private readonly _getMediaByIdsUseCase: GetMediaByIdsUseCase,
        private readonly _getVideoInformationUseCase: GetVideoInformationUseCase,
        private readonly _moveMediaFolderUseCase: MoveMediaToFolderUseCase,
        private readonly _createMediaThumbnailUseCase: CreateMediaThumbnailUseCase,
        private readonly _getMediaThumbnailsListUseCase: GetMediaThumbnailsListUseCase,
        private readonly _fetchMediaDescriptionUseCase: FetchMediaDescriptionUseCase,
        private readonly _transcodeHeifToJpgUseCase: TranscodeHeifToJpgUseCase,
        private readonly _uploadMediaV2UseCase: UploadMediaV2UseCase,
        private readonly _getMediaForEditionUseCase: GetMediaForEditionUseCase,
        private readonly _updateTransformDataUseCase: UpdateTransformDataUseCase
    ) {}

    @Query(restaurantIdParamsValidator)
    @Body(getCloudStorageUploadParamsBodyValidator)
    async getCloudStorageUploadParams(
        req: Request<any, any, GetCloudStorageUploadParamsBodyDto, RestaurantIdParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: restaurantId } = req.query;
            const { file } = req.body;

            const result = await this._getCloudStorageUploadParamsUseCase.execute({
                file,
                restaurantId,
            });

            return res.status(200).json(result);
        } catch (error) {
            logger.error('[MEDIAS] Error when uploading medias', { query: req.query, body: req.body, error });
            next(error);
        }
    }

    @Params(getRestaurantMediasParamsValidator)
    @Query(getRestaurantMediasQueryValidator)
    async handleGetRestaurantMedias(
        req: Request<GetRestaurantMediasParamsDto, any, any, GetRestaurantMediasQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const {
                page_number: pageNumber,
                page_size: pageSize,
                total,
                media_type: mediaType,
                sort_order: sortOrder,
                is_never_used: isNeverUsed,
                title,
                folder_id: folderId,
                max_video_size: maxVideoSize,
            } = req.query;
            const pagination = new Pagination({ pageNumber, pageSize, total });
            const filters = new MediaFilters({
                mediaType,
                sortOrder,
                restaurantId,
                isNeverUsed,
                title,
                folderIds: folderId === undefined ? undefined : [folderId],
                maxVideoSize,
            });

            const result = await this._getRestaurantMediasPaginatedUseCase.execute({
                pagination,
                filters,
            });

            return res.json({ data: result });
        } catch (error) {
            logger.error('[MEDIAS] Error when getting restaurant medias', { params: req.params, query: req.query, error });
            next(error);
        }
    }

    @Params(getMediaByIdParamsValidator)
    async handleGetMediaById(req: Request<GetMediaByIdParamsDto>, res: Response, next: NextFunction) {
        try {
            const { medium_id: mediaId } = req.params;
            const media = await this._getMediaByIdUseCase.execute(mediaId);
            return res.json({ msg: 'Media retrieved', data: media });
        } catch (error) {
            logger.error('[MEDIAS] Error when getting media by id', { params: req.params, error });
            next(error);
        }
    }

    @Query(restaurantIdParamsValidator)
    @Params(getMediaByIdParamsValidator)
    @Body(updateMediaBodyValidator)
    async handleUpdateMediaById(
        req: RequestWithPermissions<GetMediaByIdParamsDto, any, UpdateMediaBodyDto, RestaurantIdParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(CaslAction.UPDATE, subject(CaslSubject.MEDIA, { restaurantId }));
            const { medium_id: mediaId } = req.params;
            const { media } = req.body;
            const mediaUpdated = await this._updateMediaUseCase.execute({ mediaId, data: media });
            if (mediaUpdated === null) {
                throw new MalouError(MalouErrorCode.NOT_FOUND);
            }
            return res.json({ msg: 'Media updated', data: mediaUpdated });
        } catch (error) {
            logger.error('[MEDIAS] Error when updating media by id', { params: req.params, query: req.query, body: req.body, error });
            next(error);
        }
    }

    @Params(getMediaByIdParamsValidator)
    @Query(restaurantIdParamsValidator)
    async handleReplaceMediaUrlsById(
        req: RequestWithPermissions<GetMediaByIdParamsDto, any, any, RestaurantIdParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(CaslAction.UPDATE, subject(CaslSubject.MEDIA, { restaurantId }));
            const { medium_id: mediaId } = req.params;
            const updatedMedia = await this._replaceMediaUrlsUseCase.execute(req, mediaId);
            return res.json({ msg: 'Media updated', data: updatedMedia });
        } catch (error) {
            logger.error('[MEDIAS] Error when replacing media urls by id', { params: req.params, query: req.query, error });
            next(error);
        }
    }

    @Params(deleteManyMediaParamsValidator)
    @Query(deleteManyMediaQueryValidator)
    async handleDeleteManyMedias(
        req: Request<DeleteManyMediaParamsDto, any, any, DeleteManyMediaQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { mediaIds } = req.query;

            await this._deleteMediasUseCase.execute(mediaIds, restaurantId);

            return res.json({ msg: 'Media deleted' });
        } catch (error) {
            logger.error('[MEDIAS] Error when deleting many medias', { query: req.query, body: req.body, error });
            next(error);
        }
    }

    @Query(onlyUploadMediaQueryValidator)
    async handleUploadOnly(req: Request<any, any, any, OnlyUploadMediaQueryDto>, res: Response, next: NextFunction) {
        try {
            const { entityRelated, entityId, category } = req.query;
            const result = await this._uploadOnlyUseCase.execute({ req, props: { entityRelated, entityId, category } });
            return res.status(200).json({ msg: 'Successful upload', data: result });
        } catch (error) {
            logger.error('[MEDIAS] Error when uploading only', { query: req.query, error });
            return next(error);
        }
    }

    @Query(uploadAndCreateMediaQueryValidator)
    async handleUploadAndCreateMedia(
        req: RequestWithPermissions<any, any, any, UploadAndCreateMediaQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: restaurantId } = req.query;

            if (restaurantId) {
                // We allow any user to create a Media when it is not related to a restaurant
                assert(req.userRestaurantsAbility);
                ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                    CaslAction.CREATE,
                    subject(CaslSubject.MEDIA, { restaurantId })
                );
            }

            const result = await this._uploadAndCreateMediaUseCase.execute({ req, restaurantId });

            return res.status(200).json({ msg: 'Media created', data: result });
        } catch (error) {
            logger.error('[MEDIAS] Error when uploading and creating multiple medias', { query: req.query, body: req.body, error });
            return next(error);
        }
    }

    @Query(restaurantIdParamsValidator)
    @Body(createMediaAfterUploadBodyValidator)
    async handleCreateMediaAfterUpload(
        req: RequestWithPermissions<any, any, CreateMediaAfterUploadBodyDto, RestaurantIdParamsDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: restaurantId } = req.query;
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(CaslAction.CREATE, subject(CaslSubject.MEDIA, { restaurantId }));
            const { medias } = req.body;
            const newMedias = await this._createMediaAfterUploadUseCase.execute(medias, restaurantId);
            return res.status(200).json({ msg: 'Media created', data: newMedias });
        } catch (error) {
            logger.error('[MEDIAS] Error when creating multiple restaurant medias', { query: req.query, body: req.body, error });
            return next(error);
        }
    }

    @Params(duplicateRestaurantMediaParamsValidator)
    @Body(duplicateRestaurantMediaBodyValidator)
    async handleDuplicateRestaurantMedias(
        req: RequestWithPermissions<DuplicateRestaurantMediaParamsDto, any, DuplicateRestaurantMediaBodyDto>,
        res: Response<ApiResultV2<MediaDto[]>>,
        next: NextFunction
    ) {
        try {
            const { restaurant_id: fromRestId } = req.params;
            const { restaurantIds, originalMedia } = req.body;
            for (const restaurantId of restaurantIds) {
                assert(req.userRestaurantsAbility);
                ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                    CaslAction.CREATE,
                    subject(CaslSubject.MEDIA, { restaurantId })
                );
            }
            assert(req.user, 'User not found');
            const { _id: userId } = req.user;

            const duplicatedMedias = await this._duplicateMediasUseCase.execute(
                originalMedia,
                restaurantIds,
                userId.toString(),
                fromRestId
            );
            return res.json({ data: duplicatedMedias });
        } catch (error) {
            logger.error('[MEDIAS] Error when duplicating restaurant medias', { params: req.params, body: req.body, error });
            next(error);
        }
    }

    @Params(duplicateMediaForPublicationParamsValidator)
    @Body(duplicateMediaForPublicationBodyValidator)
    async handleDuplicateMediaForPublication(
        req: RequestWithPermissions<DuplicateMediaForPublicationParamsDto, never, DuplicateMediaForPublicationBodyDto>,
        res: Response<ApiResultV2<DuplicateMediaForPublicationResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds, publicationType } = req.body;
            assert(req.user, 'User not found');
            const result = await this._duplicateMediaUseCase.execute(req.params.mediaId, req.user._id.toString(), restaurantIds, {
                shouldSetDefaultTransformDataForPublication: true,
                publicationType,
            });
            return res.json({ data: result });
        } catch (error) {
            next(error);
        }
    }

    @Query(getMediaByIdsQueryValidator)
    async handleGetMediasByIds(req: Request<any, any, any, GetMediaByIdsQueryDto>, res: Response, next: NextFunction) {
        try {
            const { media_ids: mediaIds } = req.query;
            const medias = await this._getMediaByIdsUseCase.execute(mediaIds);
            return res.status(200).json({ msg: 'Medias found', data: medias });
        } catch (error) {
            logger.error('[MEDIAS] Error when getting medias by ids', { query: req.query, error });
            next(error);
        }
    }

    @Body(moveMediaTowardsFolderBodyValidator)
    async handleMoveMediaTowardsFolder(req: Request<any, any, MoveMediaTowardsFolderBodyDto>, res: Response, next: NextFunction) {
        try {
            const { folderId, mediaIds } = req.body;
            await this._moveMediaFolderUseCase.execute(mediaIds, folderId);
            return res.status(204).end();
        } catch (error) {
            logger.error('[MEDIAS] Error when moving medias towards folder', { body: req.body, error });
            next(error);
        }
    }

    @Body(getVideoInformationBodyValidator)
    async handleGetVideoInformation(
        req: Request<any, any, GetVideoInformationBodyDto>,
        res: Response<ApiResultV2<GetVideoInformationResponseDto>>,
        next: NextFunction
    ) {
        try {
            const videoInformation = await this._getVideoInformationUseCase.execute(req.body.url);
            return res.status(200).json({ data: videoInformation });
        } catch (e) {
            next(e);
        }
    }

    async handleTranscodeHeifToJpg(req: Request & { file: Express.Multer.File }, res: Response<Buffer>, next: NextFunction) {
        try {
            const jpgBuffer = await this._transcodeHeifToJpgUseCase.execute(req.file.buffer);
            return res.status(200).type('image/jpg').send(jpgBuffer);
        } catch (e) {
            next(e);
        }
    }

    @Body(createVideoThumbnailBodyValidator)
    async handleCreateVideoThumbnail(req: Request<any, any, CreateVideoThumbnailBodyDto>, res: Response, next: NextFunction) {
        try {
            const { medium_id: mediaId, moment_in_seconds: momentInSeconds } = req.body;
            const media = await this._createMediaThumbnailUseCase.execute(mediaId, momentInSeconds);
            return res.status(200).json({ data: media });
        } catch (error) {
            logger.error('[MEDIAS] Error when creating video thumbnail', { body: req.body, error });
            next(error);
        }
    }

    @Body(getMediaThumbnailsListBodyValidator)
    async handleGetMediaThumbnailsList(req: Request<any, any, GetMediaThumbnailsListBodyDto>, res: Response, next: NextFunction) {
        try {
            const { medium_id: mediaId, moments_in_seconds: momentsInSeconds } = req.body;
            const thumbnails = await this._getMediaThumbnailsListUseCase.execute(mediaId, momentsInSeconds);
            return res.status(200).json({ data: thumbnails });
        } catch (error) {
            logger.error('[MEDIAS] Error when creating video thumbnails', { body: req.body, error });
            next(error);
        }
    }

    @Query(fetchMediaDescriptionQueryValidator)
    async handleFetchMediaDescription(req: Request<any, any, any, FetchMediaDescriptionQueryDto>, res: Response, next: NextFunction) {
        try {
            const { _id: userId } = req.user;
            const { mediaIds } = req.query;
            try {
                await this._fetchMediaDescriptionUseCase.execute({ mediaIds, userId });
            } catch (err) {
                logger.error('[MEDIAS] Error when fetching media description (Failed silently)', { params: req.params, err });
                return res.json({ data: null });
            }
            return res.json({ data: null });
        } catch (error) {
            logger.error('[MEDIAS] Error when fetching media description', { params: req.params, error });
            next(error);
        }
    }

    @Query(uploadMediaV2QueryValidator)
    async uploadMediaV2(
        req: RequestWithPermissions<{}, {}, {}, UploadMediaV2QueryDto>,
        res: Response<ApiResultV2<UploadMediaV2ResponseDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.userRestaurantsAbility);
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.CREATE,
                subject(CaslSubject.MEDIA, { restaurantId: req.query.restaurantId })
            );
        } catch (error) {
            next(error);
            return;
        }

        const { restaurantId, folderId, videoHeightInPixels, videoWidthInPixels, videoDurationInMilliseconds } = req.query;

        let bb: busboy.Busboy;
        try {
            bb = busboy({ headers: req.headers });
        } catch (error) {
            // Happens with a malformed content-type
            next(new MalouError(MalouErrorCode.BAD_REQUEST));
            return;
        }

        bb.on('file', async (userFileName, fileStream, _info) => {
            try {
                const result = await this._uploadMediaV2UseCase.execute({
                    userFileName,
                    source: fileStream,
                    restaurantId,
                    folderId: folderId ?? undefined,
                    additionalInfo:
                        videoWidthInPixels && videoHeightInPixels && videoDurationInMilliseconds
                            ? { videoWidthInPixels, videoHeightInPixels, videoDurationInMilliseconds }
                            : undefined,
                });

                res.json({ data: result });
            } catch (err) {
                next(err);
            }
        });
        req.pipe(bb);
    }

    @Params(getMediaForEditionPathValidator)
    @Body(getMediaForEditionBodyValidator)
    async getMediaForEdition(
        req: Request<GetMediaForEditionPathDto, {}, GetMediaForEditionBodyDto>,
        res: Response<ApiResultV2<GetMediaForEditionResponseDto>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const dto = await this._getMediaForEditionUseCase.execute(req.params.mediaId, req.body.publicationType);
            res.json({ data: dto });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateTransformDataParamsValidator)
    @Body(updateTransformDataBodyValidator)
    async handleUpdateTransformData(
        req: Request<UpdateTransformDataParamsDto, any, UpdateTransformDataBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            await this._updateTransformDataUseCase.execute(req.params.mediaId, req.body);
            return res.status(204).end();
        } catch (error) {
            next(error);
        }
    }
}
