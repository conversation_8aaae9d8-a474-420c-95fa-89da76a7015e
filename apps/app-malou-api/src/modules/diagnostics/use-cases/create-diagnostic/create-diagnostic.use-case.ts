import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';
import { v4 as uuidV4 } from 'uuid';

import { DiagnosticDto } from '@malou-io/package-dto';
import { IDiagnostic, IDiagnosticWithCategory, toDbId } from '@malou-io/package-models';
import { CountryCode, MalouErrorCode, MaloupeLocale, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { GmbCategoryIdEnum } from ':modules/categories/types';
import DiagnosticsRepository from ':modules/diagnostics/diagnostic.repository';
import {
    MINIMUM_AVERAGE_FOLLOWER_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
    MINIMUM_AVERAGE_LIKE_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
    MINIMUM_AVERAGE_POST_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
} from ':modules/diagnostics/diagnostics.constants';
import { GetLocationCategoryService } from ':modules/diagnostics/services/get-location-category/get-location-category.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GmapsProvider } from ':providers/google/gmaps.provider';
import { PlaceDetail } from ':providers/google/gmaps.provider.interface';
import { SlackChannel, SlackService } from ':services/slack.service';

@autoInjectable()
export class CreateOrRetrieveDiagnosticUseCase {
    constructor(
        private readonly _gmapsProvider: GmapsProvider,
        private readonly _diagnosticsRepository: DiagnosticsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _getLocationCategoryService: GetLocationCategoryService,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute({ placeId, lang }: { placeId: string; lang: MaloupeLocale }): Promise<DiagnosticDto> {
        const existingRestaurantId = await this._restaurantsRepository.getActiveRestaurantIdByPlaceId(placeId);
        const startOfMonth = DateTime.now().startOf('month').toJSDate();
        const existingDiagnostic = await this._diagnosticsRepository.getDiagnosticByPlaceIdAfterDate(placeId, startOfMonth);
        if (existingDiagnostic) {
            return existingDiagnostic.toDto();
        }
        try {
            const placeDetails = await this._gmapsProvider.getPlaceDetails(placeId, { apiKey: Config.geolocation.maloupeGmapsApiKey });
            const partialDiagnostic = await this._mapPlaceDetailToLocation(placeId, placeDetails, existingRestaurantId, lang);
            const diagnostic = await this._diagnosticsRepository.createAndPopulate(partialDiagnostic);
            if (!diagnostic) {
                throw new MalouError(MalouErrorCode.ERROR_WHILE_CREATING_DIAGNOSTIC, {
                    message: 'cannot create diagnostic',
                    metadata: { placeId },
                });
            }
            return diagnostic.toDto();
        } catch (error: any) {
            this._slackService.sendAlert({
                channel: SlackChannel.MALOUPE_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.ERROR_WHILE_CREATING_DIAGNOSTIC, {
                        message: error.message,
                        metadata: { placeId },
                    }),
                    restaurantId: existingRestaurantId,
                    metadata: { placeId },
                },
            });

            throw error;
        }
    }

    private async _mapPlaceDetailToLocation(
        placeId: string,
        placeDetails: PlaceDetail,
        restaurantId: string | null,
        lang: MaloupeLocale
    ): Promise<Partial<IDiagnostic>> {
        const address = {
            ...this._mapPlaceDetailToMalouAddress(placeDetails),
            formattedAddress: placeDetails.formattedAddress,
        };
        const categoryId = placeDetails.primaryType ? `gcid:${placeDetails.primaryType}` : GmbCategoryIdEnum.RESTAURANT;
        const category = await this._getLocationCategoryService.execute(categoryId);
        const instagramPage = await this._getInstagramPageIfAvailable(restaurantId);

        return {
            placeId,
            malouDiagnosticId: uuidV4(),
            restaurantId: restaurantId ? toDbId(restaurantId) : undefined,
            restaurant: {
                name: placeDetails.displayName.text,
                address,
                categoryId: toDbId(category?._id),
                types: placeDetails.types,
                openingHours: placeDetails.regularOpeningHours ?? null,
                rating: placeDetails.rating,
                services: {
                    acceptsCashOnly: placeDetails.paymentOptions?.acceptsCashOnly ?? null,
                    acceptsCreditCards: placeDetails.paymentOptions?.acceptsCreditCards ?? null,
                    acceptsDebitCards: placeDetails.paymentOptions?.acceptsDebitCards ?? null,
                    acceptsNfc: placeDetails.paymentOptions?.acceptsNfc ?? null,
                    delivery: placeDetails.delivery ?? null,
                    dineIn: placeDetails.dineIn ?? null,
                    freeParkingLot: placeDetails.parkingOptions?.freeParkingLot ?? null,
                    freeStreetParking: placeDetails.parkingOptions?.freeStreetParking ?? null,
                    goodForWatchingSports: placeDetails.goodForWatchingSports ?? null,
                    liveMusic: placeDetails.liveMusic ?? null,
                    menuForChildren: placeDetails.menuForChildren ?? null,
                    outdoorSeating: placeDetails.outdoorSeating ?? null,
                    paidStreetParking: placeDetails.parkingOptions?.paidStreetParking ?? null,
                    reservable: placeDetails.reservable ?? null,
                    servesBeer: placeDetails.servesBeer ?? null,
                    servesBreakfast: placeDetails.servesBreakfast ?? null,
                    servesBrunch: placeDetails.servesBrunch ?? null,
                    servesCocktails: placeDetails.servesCocktails ?? null,
                    servesCoffee: placeDetails.servesCoffee ?? null,
                    servesDessert: placeDetails.servesDessert ?? null,
                    servesLunch: placeDetails.servesLunch ?? null,
                    servesWine: placeDetails.servesWine ?? null,
                    takeout: placeDetails.takeout ?? null,
                    wheelchairAccessibleParking: placeDetails.accessibilityOptions?.wheelchairAccessibleParking ?? null,
                    wheelchairAccessibleSeating: placeDetails.accessibilityOptions?.wheelchairAccessibleSeating ?? null,
                },
                phoneNumber: placeDetails.internationalPhoneNumber,
                latlng: {
                    lat: placeDetails.location.latitude,
                    lng: placeDetails.location.longitude,
                },
            },
            instagramPage,
            photoCount: placeDetails.photos?.length ?? 0,
            reviewCount: placeDetails.userRatingCount,
            reviews: (placeDetails.reviews ?? []).map((review) => ({
                author: review.authorAttribution?.displayName,
                text: review.originalText?.text,
                socialId: this._extractSocialIdFromReview(review.name),
                rating: review.rating,
                language: review.originalText?.languageCode,
            })),
            events: [],
            language: lang,
            createdAt: new Date(),
        };
    }

    private _mapPlaceDetailToMalouAddress(placeDetail: PlaceDetail): IDiagnosticWithCategory['restaurant']['address'] {
        const addressComponents = placeDetail.addressComponents;
        let streetNumber = addressComponents.find((component) => component.types.includes('street_number'))?.longText;
        let route = addressComponents.find((component) => component.types.includes('route'))?.longText;
        const postalCode = addressComponents.find((component) => component.types.includes('postal_code'))?.longText;
        const locality = addressComponents.find((component) => component.types.includes('locality'))?.longText;
        const administrativeArea = addressComponents.find((component) => component.types.includes('administrative_area_level_1'))?.longText;
        const country = addressComponents.find((component) => component.types.includes('country'))?.longText;
        const regionCode = addressComponents.find((component) => component.types.includes('country'))?.shortText as CountryCode;
        if (!streetNumber || !route || !postalCode || !locality || !country) {
            // There is a case where the address components are not present in the place detail, so we need to extract the address from the formatted address
            // ex: 92 ZAC de Metzange Buchel, 57100 Thionville, France
            // PlaceId: "ChIJ25_vV5wvlUcRJIfYDKsKXhU"
            const streetNumberRegex = /^\d+/;
            const formattedAddress = placeDetail.formattedAddress;
            const streetNumberMatch = RegExp(streetNumberRegex).exec(formattedAddress);
            if (streetNumberMatch) {
                streetNumber = streetNumberMatch[0];
            }
            const routeMatch = RegExp(/([A-Za-z-\s])+,/).exec(formattedAddress);
            if (routeMatch) {
                route = routeMatch[0].replace(',', '').trim();
            }

            if (!postalCode || !locality || !country) {
                const missingFields = [!postalCode && 'postalCode', !locality && 'locality', !country && 'country'].filter(
                    Boolean
                ) as string[];
                throw new MalouError(MalouErrorCode.GMB_MISSING_ADDRESS, {
                    message: `Missing ${missingFields.join(', ')} in address components`,
                    metadata: { addressComponents, placeId: placeDetail.id },
                });
            }
        }
        return {
            streetNumber,
            route,
            postalCode,
            locality: locality ?? administrativeArea,
            country,
            regionCode,
        };
    }

    private async _getInstagramPageIfAvailable(restaurantId: string | null): Promise<IDiagnostic['instagramPage'] | null> {
        if (!restaurantId) {
            return null;
        }
        const restaurantInstagramPlatform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(
            restaurantId,
            PlatformKey.INSTAGRAM
        );
        if (!restaurantInstagramPlatform) {
            return null;
        }
        const userName = restaurantInstagramPlatform.socialLink?.split('/').pop() ?? '';
        return {
            name: restaurantInstagramPlatform.name ?? userName,
            userName,
            averageLikeCount: 0,
            followerCount: 0,
            postCount: 0,
            averageFollowerCountForSimilarRestaurants: MINIMUM_AVERAGE_FOLLOWER_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
            averageLikeCountForSimilarRestaurants: MINIMUM_AVERAGE_LIKE_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
            averagePostCountForSimilarRestaurants: MINIMUM_AVERAGE_POST_COUNT_FOR_SIMILAR_INSTAGRAM_ACCOUNTS,
        };
    }

    /**
     * Extract the social ID from the review name.
     * @param reviewName The review name.
     * Example: 'places/ChIJ3XGO4ykuqBIRCSFFEonSZeY/reviews/ChdDSUhNMG9nS0VJQ0FnSUNoeE9fcTV3RRAB'
     */
    private _extractSocialIdFromReview(reviewName: string): string {
        return reviewName.split('reviews/').pop() ?? '';
    }
}
