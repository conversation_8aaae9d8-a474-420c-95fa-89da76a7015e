import { autoInjectable } from 'tsyringe';

import { IDiagnosticWithCategory } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import CategoriesRepository from ':modules/categories/categories.repository';
import { GmbCategoryIdEnum } from ':modules/categories/types';
import { SlackChannel, SlackService } from ':services/slack.service';

const DEFAULT_RESTAURANT_CATEGORY = GmbCategoryIdEnum.RESTAURANT;
const DEFAULT_HOTEL_CATEGORY = GmbCategoryIdEnum.HOTEL;

@autoInjectable()
export class GetLocationCategoryService {
    constructor(
        private readonly _categoriesRepository: CategoriesRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(categoryId: string): Promise<IDiagnosticWithCategory['restaurant']['category']> {
        const category = await this._categoriesRepository.getUsableCategoryByCategoryId(categoryId);
        if (category) {
            return category;
        }

        this._slackService.sendAlert({
            channel: SlackChannel.MALOUPE_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.DIAGNOSTIC_INVALID_CATEGORY, {
                    message: 'Category is invalid for diagnostic',
                    metadata: { categoryId },
                }),
                metadata: { categoryId },
            },
        });

        const defaultCategoryId = categoryId.includes('hotel') ? DEFAULT_HOTEL_CATEGORY : DEFAULT_RESTAURANT_CATEGORY;
        const defaultCategory = await this._categoriesRepository.getUsableCategoryByCategoryId(defaultCategoryId);

        if (!defaultCategory) {
            throw new MalouError(MalouErrorCode.DIAGNOSTIC_INVALID_CATEGORY, {
                message: 'Cannot find default category for diagnostic',
                metadata: { categoryId: defaultCategoryId },
            });
        }

        return defaultCategory;
    }
}
