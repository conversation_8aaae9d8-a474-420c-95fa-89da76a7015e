import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';

@singleton()
export class SchedulePostPublicationService {
    constructor(private readonly _agendaSingleton: AgendaSingleton) {}

    async schedulePostPublication(userId: string, postId: string, date: Date): Promise<void> {
        await this.cancelPostPublication(postId);
        await this._agendaSingleton.schedule(date, AgendaJobName.PREPARE_POST, { userId: toDbId(userId), postId: toDbId(postId) });

        logger.info('[POST PUBLICATION] Rescheduled post publication', { postId, date });
    }

    async cancelPostPublication(postId: string): Promise<void> {
        const deletedJobsCount = await this._agendaSingleton.deleteJobs({
            name: AgendaJobName.PREPARE_POST,
            'data.postId': { $in: [postId, toDbId(postId)] },
        });

        logger.info('[POST PUBLICATION] Cancelled post publication', { postId, deletedJobsCount });
    }
}
