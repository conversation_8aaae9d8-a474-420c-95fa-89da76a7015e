import { assert } from 'node:console';
import { singleton } from 'tsyringe';

import { errorR<PERSON>lacer, MalouErrorCode, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { CheckPostStatusOnFacebookUseCase } from ':modules/posts/v2/use-cases/check-post-status-on-platform/platforms/check-post-status-on-facebook.use-case';
import { SlackChannel, SlackService } from ':services/slack.service';

type CheckSocialPostUseCaseType = { execute: ({ postId, socialId }: { postId: string; socialId: string }) => Promise<void> };

// TODO posts-v2 write a test for this use case
@singleton()
export class CheckPostStatusOnPlatformUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _checkPostStatusOnFacebookUseCase: CheckPostStatusOnFacebookUseCase,
        private readonly _slackService: SlackService
    ) {}

    async execute({ postId, socialId }: { postId: string; socialId: string }): Promise<void> {
        try {
            const post = await this._postsRepository.findOne({
                filter: { _id: postId },
                options: { lean: true },
            });
            if (!post) {
                logger.info('[POST PUBLICATION] Post does not exists', { postId });
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { postId } });
            }
            assert(post.key, 'Post key is missing');

            const checkSocialPostUseCase: CheckSocialPostUseCaseType = {
                [PlatformKey.FACEBOOK]: this._checkPostStatusOnFacebookUseCase,
            }[post.key!];
            assert(checkSocialPostUseCase, 'checkSocialPostUseCase is not implemented');

            await checkSocialPostUseCase.execute({ postId, socialId });
        } catch (err: any) {
            logger.error('[POST PUBLICATION] Failed to check post status', { err });

            await this._postsRepository.updateOne({
                filter: { _id: postId },
                update: {
                    published: PostPublicationStatus.ERROR,
                    errorData: err?.message ?? JSON.stringify(err, errorReplacer),
                    errorStage: 'CheckPostStatusOnPlatformUseCase',
                    isPublishing: false,
                },
            });

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.POSTS_V2_ALERTS });

            throw err;
        }
    }
}
