import { Ability, AbilityTuple, ForbiddenError, MongoQuery, subject } from '@casl/ability';
import axios from 'axios';
import { compact, difference, isUndefined, omitBy } from 'lodash';
import { DateTime } from 'luxon';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert';
import { format } from 'node:util';
import { singleton } from 'tsyringe';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

import { DuplicatePostV2BodyDto, DuplicatePostV2ResponseDto, postMediaValidator, ReelThumbnail } from '@malou-io/package-dto';
import { DbId, IPost, newDbId, toDbId, toDbIds } from '@malou-io/package-models';
import {
    CallToActionType,
    CaslAction,
    CaslSubject,
    getFeatureFlaggedPlatforms,
    getPublicationType,
    getSocialPlatformKeys,
    isBeforeNow,
    isNotNil,
    MalouErrorCode,
    MapstrCtaButtonType,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    PostType,
    ProcessingMediaStatus,
    PublicationType,
    SeoPostCallToAction,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { DuplicateMediaUseCase } from ':modules/media/use-cases/duplicate-media/duplicate-media.use-case';
import { GetMediaForEditionService } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.service';
import { UploadMediaV2UseCase } from ':modules/media/use-cases/upload-media-v2/upload-media-v2.use-case';
import { GetDisconnectedPlatformsForRestaurantUseCase } from ':modules/platforms/use-cases/get-disconnected-platforms-for-restaurant/get-disconnected-platforms-for-restaurant.use-case';
import { SocialPostHashtag } from ':modules/posts/entities/social-post-hashtag.entity';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { SeoPost, SeoPostProps } from ':modules/posts/v2/entities/seo-post.entity';
import { SocialPost, SocialPostProps } from ':modules/posts/v2/entities/social-post.entity';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { SchedulePostPublicationService } from ':modules/posts/v2/services/schedule-post-publication/schedule-post-publication.service';
import { WaitForMediaProcessingUseCase } from ':modules/processing-medias/use-cases/wait-for-media-processing.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

import { FAKE_TRANSFORM_DATA, waitUntilReelThumbnailIsExtracted } from '../update-social-post/update-social-post.use-case';

@singleton()
export class DuplicatePostUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _getMediaForEditionService: GetMediaForEditionService,
        private readonly _schedulePostPublicationService: SchedulePostPublicationService,
        private readonly _duplicateMediaUseCase: DuplicateMediaUseCase,
        private readonly _uploadMediaV2UseCase: UploadMediaV2UseCase,
        private readonly _getDisconnectedPlatformsForRestaurantUseCase: GetDisconnectedPlatformsForRestaurantUseCase,
        private readonly _tiktokQueryCreatorInfoUseCase: TiktokQueryCreatorInfoUseCase,
        private readonly _waitForMediaProcessingUseCase: WaitForMediaProcessingUseCase
    ) {}

    // Rules for duplication: https://www.notion.so/welcomehomemalou/Social-Media-Duplication-1a6c978b4cbe80a88862e93fdf1f6d7d
    async execute({
        restaurantIds,
        postIdsToDuplicate,
        author,
        userRestaurantsAbility,
        postDestination,
        fromRestaurantId,
        customFields,
    }: {
        restaurantIds: string[];
        postIdsToDuplicate: string[];
        author: PostAuthorProps;
        userRestaurantsAbility: Ability<AbilityTuple, MongoQuery>;
        postDestination: PostSource;
        fromRestaurantId: string;
        customFields?: DuplicatePostV2BodyDto['customFields'];
    }): Promise<DuplicatePostV2ResponseDto> {
        const posts = await this._postsRepository.findByIds(postIdsToDuplicate);

        // Check that all posts exist
        if (posts.length !== postIdsToDuplicate.length) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, {
                metadata: { postIds: postIdsToDuplicate.filter((postId) => !posts.some((post) => post._id.toString() === postId)) },
            });
        }

        await Promise.all(postIdsToDuplicate.map((postId) => waitUntilReelThumbnailIsExtracted(postId)));

        // Check user permissions
        for (const restaurantId of restaurantIds) {
            ForbiddenError.from(userRestaurantsAbility).throwUnlessCan(
                CaslAction.PUBLISH,
                subject(CaslSubject.SOCIAL_POST, { restaurantId })
            );
            for (const post of posts) {
                if (!post || (post.restaurantId && post.restaurantId.toString() !== fromRestaurantId)) {
                    throw new MalouError(MalouErrorCode.BAD_REQUEST);
                }
                ForbiddenError.from(userRestaurantsAbility).throwUnlessCan(CaslAction.CREATE, subject(CaslSubject.POST, { restaurantId }));
            }
        }

        const result: DuplicatePostV2ResponseDto = { socialPostsDuplicated: [], seoPostsDuplicated: [] };

        if (postDestination === PostSource.SOCIAL) {
            const postsToCreateWithRestaurantIds = await this._buildSocialPostsToCreate(
                posts,
                restaurantIds,
                author,
                fromRestaurantId,
                customFields
            );
            await this._postsRepository.createMultipleSocialPosts(postsToCreateWithRestaurantIds);
            await this._schedulePostsPublication(postsToCreateWithRestaurantIds);
            result.socialPostsDuplicated = postsToCreateWithRestaurantIds.map(({ post, restaurantId }) => ({
                post: post.toDto(),
                restaurantId,
            }));
        } else if (postDestination === PostSource.SEO) {
            const postsToCreateWithRestaurantIds = await this._buildSeoPostsToCreate(
                posts,
                restaurantIds,
                author,
                fromRestaurantId,
                customFields
            );
            await this._postsRepository.createMultipleSeoPosts(postsToCreateWithRestaurantIds);
            await this._schedulePostsPublication(postsToCreateWithRestaurantIds);
            result.seoPostsDuplicated = postsToCreateWithRestaurantIds.map(({ post, restaurantId }) => ({
                post: post.toDto(),
                restaurantId,
            }));
        }

        return result;
    }

    /**
     * SOCIAL POSTS METHODS
     */

    /**
     * Returns null if the post is not a reel, or if the post has no thumnail and is not published.
     */
    private async _createThumbnailIdForPublishedReel(post: IPost): Promise<string | null> {
        /** Returns the ID of the created media */
        const uploadAttachment = async (url: string): Promise<string> => {
            const result = await this._createAttachmentFromUrlAndName(url, undefined, post.restaurantId!.toString());
            if (result.isErr()) {
                assert.fail(format('_createAttachmentFromUrlAndName failed %j', { error: result.error, url, postId: post._id }));
            }
            return result.value;
        };

        if (post.postType !== PostType.REEL) {
            return null;
        }

        // we always prefer `socialAttachments[0].thumbnailUrl` over thumbnail/reelThumbnail
        // because:
        //  - if the reel is published, `socialAttachments` is always set and:
        //      - reelThumbnail will no longer be updated because it’s not possible to update
        //      published reels on Malou
        //      - if the reel is updated on the external platform, socialAttachments[0].thumbnailUrl
        //      will be updated and we wand to reflect these changes on the Malou app
        //  - if the reel is not published, socialAttachments is not set and we must use
        //  reelThumbnail (the Malou thumbnail) instead.

        if (!post.socialAttachments || post.socialAttachments.length === 0) {
            // the post is not published yet
            if (!post.thumbnail) {
                return null;
            }
            return post.thumbnail.toString();
        }

        // `thumbnailUrl` is supposed to be the URL of the thumbnail generated by Facebook,
        // however some reels don’t have this property.
        const thumbnailUrl = post.socialAttachments[0].thumbnailUrl;
        if (!thumbnailUrl) {
            return null;
        }
        assert(post.socialAttachments[0].thumbnailUrl, 'The post has no thumbnail');
        return await uploadAttachment(post.socialAttachments[0].thumbnailUrl);
    }

    /**
     * ⚠️ While this function returns a ReelThumbnail, only the field `media.id` of the property `media` is valid.
     * All other fields of the `media` objects are invalid and should not be used.
     *
     * See _mapSocialPostToPostDocumentForCreation in posts.repository.ts
     */
    private async _getReelThumbnailProperty(post: IPost): Promise<ReelThumbnail | undefined> {
        const mediaId: string | null = (await this._createThumbnailIdForPublishedReel(post)) ?? post.thumbnail?.toString() ?? null;

        let media: z.infer<typeof postMediaValidator> | undefined = undefined;
        if (mediaId) {
            // Many old pictures don’t have the required properties below so we use fake values. It’s okay
            // because we only need media.id to duplicate the post.
            media = {
                type: MediaType.PHOTO,
                id: mediaId,
                thumbnail1024OutsideUrl: '',
                thumbnail256OutsideUrl: '',
                transformData: FAKE_TRANSFORM_DATA,
                aspectRatio: 1,
            };
        }

        if (post.thumbnailOffsetTimeInMs) {
            return {
                type: 'videoFrame',
                media,
                thumbnailOffsetTimeInMs: post.thumbnailOffsetTimeInMs,
            };
        }
        if (media) {
            return {
                type: 'custom',
                media,
            };
        }
        return undefined;
    }

    private async _buildSocialPostsToCreate(
        posts: IPost[],
        restaurantIds: string[],
        author: PostAuthorProps,
        fromRestaurantId: string,
        customFields: DuplicatePostV2BodyDto['customFields']
    ): Promise<{ post: SocialPost; restaurantId: string; duplicatedFromRestaurantId: string }[]> {
        const postsToCreateWithRestaurantIds: { post: SocialPost; restaurantId: string; duplicatedFromRestaurantId: string }[] = [];

        const defaultPlannedPublicationDate = this._getTomorrowDateRoundedUpToQuarterHour();

        for (const post of posts) {
            let reelThumbnail: ReelThumbnail | undefined = undefined;
            if (post.postType === PostType.REEL) {
                reelThumbnail = await this._getReelThumbnailProperty(post);
            }

            const postIsDraftOrPending = [PostPublicationStatus.PENDING, PostPublicationStatus.DRAFT].includes(post.published);
            const newPost = new SocialPost({
                id: newDbId().toString(),
                title: post.title,
                text: post.text ?? '',
                platformKeys: post.key ? [post.key] : (post.keys ?? []),
                published: postIsDraftOrPending ? post.published : PostPublicationStatus.DRAFT,
                isPublishing: false,
                postType: post.postType,
                plannedPublicationDate:
                    postIsDraftOrPending && post.plannedPublicationDate && !isBeforeNow(post.plannedPublicationDate)
                        ? post.plannedPublicationDate
                        : defaultPlannedPublicationDate,
                attachments: [], // Will be filled later
                hashtags: post.hashtags
                    ? {
                          selected:
                              post.hashtags.selected?.map(
                                  (h) =>
                                      new SocialPostHashtag({
                                          id: h._id ? h._id.toString() : (h as any).id, // TODO remove this when hashtags are no longer dirty in db
                                          text: h.text,
                                          isMain: h.isMain,
                                          isCustomerInput: h.isCustomerInput,
                                          type: h.type,
                                          createdAt: h.createdAt,
                                          updatedAt: h.updatedAt,
                                      })
                              ) ?? [],
                          suggested:
                              post.hashtags.suggested?.map(
                                  (h) =>
                                      new SocialPostHashtag({
                                          id: h._id ? h._id.toString() : (h as any).id, // TODO remove this when hashtags are no longer dirty in db
                                          text: h.text,
                                          isMain: h.isMain,
                                          isCustomerInput: h.isCustomerInput,
                                          type: h.type,
                                          createdAt: h.createdAt,
                                          updatedAt: h.updatedAt,
                                      })
                              ) ?? [],
                      }
                    : undefined,
                callToAction: post.callToAction
                    ? {
                          actionType: post.callToAction.actionType as MapstrCtaButtonType,
                          url: post.callToAction.url ?? '',
                      }
                    : undefined,
                location: post.location ?? undefined,
                feedbacks: undefined, // Do not duplicate feedbacks
                error: undefined, // Do not duplicate error
                socialLink: undefined, // Do not duplicate social data
                socialCreatedAt: undefined, // Do not duplicate social data
                author: new PostAuthor(author), // Always use the author from the request
                userTagsList: post.userTagsList ?? [],
                bindingId: undefined, // Do not duplicate bindingId, will be set later,
                tiktokOptions: post.tiktokOptions ?? {
                    privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                    interactionAbility: {
                        comment: false,
                        duet: false,
                        stitch: false,
                    },
                    contentDisclosureSettings: {
                        isActivated: false,
                        yourBrand: false,
                        brandedContent: false,
                    },
                }, // Will be updated later if needed because of the TikTok account restrictions
                reelThumbnail,
                instagramCollaboratorsUsernames: post.instagramCollaboratorsUsernames,
            });

            let attachmentsToDuplicate = compact(post.attachments);

            if ((post.socialAttachments?.length ?? 0) > (post.attachments?.length ?? 0)) {
                const urlsAndSocialIds =
                    post.socialAttachments?.map((socialAttachment) => ({
                        url: socialAttachment.urls.original,
                        name: socialAttachment.socialId ?? undefined,
                    })) ?? [];
                const newAttachments = await this._createAttachmentsFromUrlAndName(urlsAndSocialIds, fromRestaurantId);
                attachmentsToDuplicate = toDbIds(newAttachments);
                await this._postsRepository.findOneAndUpdate({
                    filter: { _id: post._id },
                    update: { attachments: attachmentsToDuplicate },
                });
            }

            const allNewAttachmentIdAndRestaurantIds = await this._duplicateAttachments(attachmentsToDuplicate, author, restaurantIds);

            for (const restaurantId of restaurantIds) {
                // Update the platform keys to match the new restaurant connected platforms
                const connectedSocialPlatformKeys = await this._getConnectedSocialPlatformKeys(restaurantId, author.id);
                const platformKeys = newPost.platformKeys.filter((key) => connectedSocialPlatformKeys.includes(key));
                newPost.platformKeys = platformKeys;
                if (newPost.platformKeys.length === 0) {
                    newPost.published = PostPublicationStatus.DRAFT;
                }

                // Duplicate tiktokOptions
                const tiktokOptions = platformKeys.includes(PlatformKey.TIKTOK)
                    ? await this._computeAvailableTiktokOptions(restaurantId, newPost.tiktokOptions)
                    : newPost.tiktokOptions;

                // Duplicate attachments
                const publicationType = getPublicationType(newPost.postType, false);
                const newAttachmentIds = allNewAttachmentIdAndRestaurantIds
                    .filter((data) => data.restaurantId === restaurantId)
                    .map((data) => data.duplicatedMediaId);
                newPost.attachments = await Promise.all(
                    newAttachmentIds.map((id) => this._getMediaForEditionService.getMedia(id, publicationType))
                );

                const customFieldsForRestaurant = this._buildCustomSocialPostFieldsForRestaurant(customFields, restaurantId);

                postsToCreateWithRestaurantIds.push({
                    post: newPost.cloneWith({ id: newDbId().toString(), ...customFieldsForRestaurant, bindingId: uuidv4(), tiktokOptions }),
                    restaurantId,
                    duplicatedFromRestaurantId: fromRestaurantId,
                });
            }
        }

        return postsToCreateWithRestaurantIds;
    }

    private _buildCustomSocialPostFieldsForRestaurant(
        customFields: DuplicatePostV2BodyDto['customFields'],
        restaurantId: string
    ): Partial<SocialPostProps> {
        if (!customFields) {
            return {};
        }
        const customFieldsForRestaurant = customFields.find((customField) => customField.restaurantId === restaurantId);
        if (!customFieldsForRestaurant) {
            return {};
        }

        // Delete all undefined fields but keep null fields because null is a valid value
        return Object.fromEntries(Object.entries(customFieldsForRestaurant).filter(([_, value]) => value !== undefined));
    }

    private async _computeAvailableTiktokOptions(
        restaurantId: string,
        tiktokOptions: SocialPost['tiktokOptions']
    ): Promise<SocialPost['tiktokOptions']> {
        const creatorInfo = await this._tiktokQueryCreatorInfoUseCase.execute({ restaurantId });

        const newTiktokOptions = { ...tiktokOptions };

        if (creatorInfo.commentDisabled) {
            newTiktokOptions.interactionAbility.comment = false;
        }
        if (creatorInfo.duetDisabled) {
            newTiktokOptions.interactionAbility.duet = false;
        }
        if (creatorInfo.stitchDisabled) {
            newTiktokOptions.interactionAbility.stitch = false;
        }

        newTiktokOptions.privacyStatus = this._getAvailablePrivacyStatus(creatorInfo.privacyLevelOptions, tiktokOptions.privacyStatus);

        return newTiktokOptions;
    }

    private _getAvailablePrivacyStatus(
        availablePrivacyStatus: TiktokPrivacyStatus[],
        currentPrivacyStatus: TiktokPrivacyStatus
    ): TiktokPrivacyStatus {
        if (availablePrivacyStatus.includes(currentPrivacyStatus)) {
            return currentPrivacyStatus;
        }
        const sortedPrivacyStatus = [
            TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
            TiktokPrivacyStatus.FOLLOWER_OF_CREATOR,
            TiktokPrivacyStatus.FOLLOWER_OF_CREATOR,
            TiktokPrivacyStatus.SELF_ONLY,
        ].filter((status) => availablePrivacyStatus.includes(status));
        return sortedPrivacyStatus[0];
    }

    /**
     * SEO POSTS METHODS
     */

    private async _buildSeoPostsToCreate(
        posts: IPost[],
        restaurantIds: string[],
        author: PostAuthorProps,
        fromRestaurantId: string,
        customFields: DuplicatePostV2BodyDto['customFields']
    ): Promise<{ post: SeoPost; restaurantId: string; duplicatedFromRestaurantId: string }[]> {
        const postsToCreateWithRestaurantIds: { post: SeoPost; restaurantId: string; duplicatedFromRestaurantId: string }[] = [];

        const defaultPlannedPublicationDate = this._getTomorrowDateRoundedUpToQuarterHour();

        const defaultCallToActionsByRestaurantId = await this._getDefaultCallToActionsForSeoPost(restaurantIds);

        for (const post of posts) {
            const postIsDraftOrPending = [PostPublicationStatus.PENDING, PostPublicationStatus.DRAFT].includes(post.published);

            const newPost = new SeoPost({
                id: newDbId().toString(),
                text: post.text ?? '',
                key: PlatformKey.GMB,
                published: PostPublicationStatus.DRAFT,
                postType: PostType.IMAGE, // the GMB API only supports posts with a single picture
                postTopic: SeoPostTopic.STANDARD,
                event: undefined,
                offer: undefined,
                plannedPublicationDate:
                    postIsDraftOrPending && post.plannedPublicationDate && !isBeforeNow(post.plannedPublicationDate)
                        ? post.plannedPublicationDate
                        : defaultPlannedPublicationDate,
                attachments: [], // Will be filled later
                callToAction: undefined, // Will be filled later
                feedbacks: undefined, // Do not duplicate feedbacks
                error: undefined, // Do not duplicate error
                socialLink: undefined, // Do not duplicate social data
                socialCreatedAt: undefined, // Do not duplicate social data
                author: new PostAuthor(author), // Always use the author from the request
                bindingId: undefined, // Do not duplicate bindingId, will be set later
            });

            const attachmentToDuplicate = await this._getAttachmentToDuplicateForSeoPost(post, fromRestaurantId);

            // re-create medias in destination restaurants
            const allNewAttachmentIdAndRestaurantIds = await this._duplicateAttachments(
                attachmentToDuplicate ? [attachmentToDuplicate] : [],
                author,
                restaurantIds
            );

            for (const restaurantId of restaurantIds) {
                // Duplicate attachments
                const newAttachmentIds = allNewAttachmentIdAndRestaurantIds
                    .filter((data) => data.restaurantId === restaurantId)
                    .map((data) => data.duplicatedMediaId);
                newPost.attachments = await Promise.all(
                    newAttachmentIds.map((id) => this._getMediaForEditionService.getMedia(id, PublicationType.POST))
                );

                // Initialize callToAction with the restaurant's website if it exists
                newPost.callToAction = defaultCallToActionsByRestaurantId.find(
                    (callToActionByRestaurantId) => callToActionByRestaurantId.restaurantId === restaurantId
                )?.callToAction;

                // Add custom fields for the restaurant
                const customFieldsForRestaurant = this._buildCustomSeoPostFieldsForRestaurant(customFields, restaurantId);

                postsToCreateWithRestaurantIds.push({
                    post: newPost.cloneWith({ id: newDbId().toString(), bindingId: uuidv4(), ...customFieldsForRestaurant }),
                    restaurantId,
                    duplicatedFromRestaurantId: fromRestaurantId,
                });
            }
        }

        return postsToCreateWithRestaurantIds;
    }

    private _buildCustomSeoPostFieldsForRestaurant(
        customFields: DuplicatePostV2BodyDto['customFields'],
        restaurantId: string
    ): Partial<SeoPostProps> {
        if (!customFields) {
            return {};
        }
        const customFieldsForRestaurant = customFields.find((customField) => customField.restaurantId === restaurantId);
        if (!customFieldsForRestaurant) {
            return {};
        }

        // Delete all undefined fields but keep null fields because null is a valid value
        return omitBy(
            {
                text: customFieldsForRestaurant.text,
                published: customFieldsForRestaurant.published,
                plannedPublicationDate: customFieldsForRestaurant.plannedPublicationDate,
                platformKeys: customFieldsForRestaurant.platformKeys,
            },
            isUndefined
        );
    }

    /**
     * Returns the ID of the attachment to duplicate to the SEO post, or null if the post has no attachments.
     * We choose only one attachment because SEO posts can’t have many attachments.
     *
     * The returned attachment is always a single photo because the GMB API doen’t support anything else.
     */
    private async _getAttachmentToDuplicateForSeoPost(post: IPost, fromRestaurantId: string): Promise<DbId | null> {
        /** Returns the ID of the created media */
        const uploadAttachment = async (url: string, name: string | undefined): Promise<DbId> => {
            const result = await this._createAttachmentFromUrlAndName(url, name, fromRestaurantId);
            if (result.isErr()) {
                assert.fail(format('_createAttachmentFromUrlAndName failed %j', { error: result.error, url, postId: post._id }));
            }
            return toDbId(result.value);
        };

        // Separate the cases when we have socialAttachments and when we don't
        // First case: with socialAttachments
        if ((post.socialAttachments?.length ?? 0) > (post.attachments?.length ?? 0)) {
            const firstAttachment = post.socialAttachments?.[0];
            assert(firstAttachment, 'firstAttachment is undefined');
            if (firstAttachment?.type === SocialAttachmentsMediaTypes.IMAGE) {
                return await uploadAttachment(firstAttachment.urls.original, firstAttachment.socialId ?? undefined);
            } else if (firstAttachment?.type === SocialAttachmentsMediaTypes.VIDEO && firstAttachment.thumbnailUrl) {
                return await uploadAttachment(firstAttachment.thumbnailUrl, firstAttachment.socialId ?? undefined);
            }
        }

        // Second case: without socialAttachments
        if ((post.attachments?.length ?? 0) > 0) {
            const firstAttachmentId = post.thumbnail ?? post.attachments?.[0];
            const firstAttachment = firstAttachmentId ? await this._mediasRepository.findById(firstAttachmentId.toString()) : undefined;
            if (firstAttachment && firstAttachmentId) {
                if (firstAttachment.type === MediaType.PHOTO) {
                    return firstAttachmentId;
                } else if (firstAttachment.type === MediaType.VIDEO) {
                    const thumbnailUrl =
                        firstAttachment.storedObjects?.thumbnail1024Outside?.publicUrl ??
                        firstAttachment.thumbnail1024Outside?.publicUrl ??
                        firstAttachment.thumbnail;
                    if (thumbnailUrl) {
                        return await uploadAttachment(thumbnailUrl, firstAttachment.socialId);
                    }
                }
            }
        }

        return null;
    }

    private async _getDefaultCallToActionsForSeoPost(
        restaurantIds: string[]
    ): Promise<{ callToAction: SeoPostCallToAction | undefined; restaurantId: string }[]> {
        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: toDbIds(restaurantIds) } },
            projection: { website: 1 },
            options: { lean: true },
        });
        return restaurants.map((restaurant) => ({
            callToAction: restaurant.website ? { actionType: CallToActionType.LEARN_MORE, url: restaurant.website } : undefined,
            restaurantId: restaurant._id.toString(),
        }));
    }

    /**
     * COMMON METHODS
     */

    private _getTomorrowDateRoundedUpToQuarterHour(): Date {
        const tomorrow = DateTime.utc().plus({ days: 1 });
        const currentMinute = tomorrow.minute;
        // Round up to the next 15-minute interval
        const minutesModulo15 = currentMinute % 15;
        const minutesToNext = minutesModulo15 === 0 ? 0 : 15 - minutesModulo15;
        return tomorrow.plus({ minutes: minutesToNext }).toJSDate();
    }

    private async _schedulePostsPublication(
        postsWithRestaurantId: { post: SocialPost; restaurantId: string }[] | { post: SeoPost; restaurantId: string }[]
    ): Promise<void> {
        const postsThatNeedToBeScheduled = postsWithRestaurantId
            .filter(({ post }) => post.published === PostPublicationStatus.PENDING)
            .map(({ post }) => post);

        for (const post of postsThatNeedToBeScheduled) {
            if (post.author) {
                await this._schedulePostPublicationService.schedulePostPublication(post.author.id, post.id, post.plannedPublicationDate);
            } else {
                logger.error('[DUPLICATE_POST] Missing author id in post', { postId: post.id });
            }
        }
    }

    /**
     * This function has a side effect: the created attachment will appear in the gallery.
     * @returns the ID of the created media
     */
    private async _createAttachmentFromUrlAndName(
        url: string,
        /** The name that will be displayed in the gallery */
        name: string | undefined,
        restaurantId: string
    ): Promise<Result<string, string>> {
        const response = await axios.get(url, { responseType: 'stream' });
        const result = await this._uploadMediaV2UseCase.execute({
            userFileName: name ?? 'Media',
            source: response.data,
            restaurantId,
        });
        const processingMediaResult = await this._waitForMediaProcessingUseCase.execute(result.processingMediaId);
        if (
            processingMediaResult.isOk() &&
            processingMediaResult.value.status === ProcessingMediaStatus.SUCCESS &&
            processingMediaResult.value.mediaId
        ) {
            return ok(processingMediaResult.value.mediaId);
        }
        if (processingMediaResult.isOk()) {
            return err(
                // eslint-disable-next-line max-len
                `Bad processing media status: ${processingMediaResult.value.status}, for processing media id: ${processingMediaResult.value._id.toString()}`
            );
        }
        return err(processingMediaResult.error);
    }

    /**
     * This function has a side effect: created attachments will appear in the gallery.
     * @returns IDs of the created medias
     */
    private async _createAttachmentsFromUrlAndName(
        urlsAndNames: {
            url: string;
            /** The name that will be displayed in the gallery */
            name?: string;
        }[],
        restaurantId: string
    ): Promise<string[]> {
        const mediaIds: string[] = [];
        for (const urlAndName of urlsAndNames) {
            const res = await this._createAttachmentFromUrlAndName(urlAndName.url, urlAndName.name, restaurantId);
            if (res.isOk()) {
                mediaIds.push(res.value);
            }
        }
        return mediaIds;
    }

    private async _duplicateAttachments(
        attachmentsToDuplicate: DbId[],
        author: PostAuthorProps,
        restaurantIds: string[]
    ): Promise<{ duplicatedMediaId: string; restaurantId: string }[]> {
        const result: { duplicatedMediaId: string; restaurantId: string }[] = [];
        for (const attachment of attachmentsToDuplicate) {
            const duplicatedAttachmentWithRestaurantIds = await this._duplicateMediaUseCase.execute(
                attachment.toString(),
                author.id,
                restaurantIds
            );
            result.push(...duplicatedAttachmentWithRestaurantIds);
        }
        return result;
    }

    private async _getConnectedSocialPlatformKeys(restaurantId: string, userId: string): Promise<PlatformKey[]> {
        const socialPlatformKeys = getSocialPlatformKeys();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();

        const availablePlatformKeys: PlatformKey[] = [];

        for (const platformKey of socialPlatformKeys) {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((p) => p.key === platformKey);
            if (!featureFlaggedPlatform) {
                availablePlatformKeys.push(platformKey);
                continue;
            }
            const isPlatformAvailable = featureFlaggedPlatform.featureFlagKey
                ? await isFeatureAvailableForUser({ userId, featureName: featureFlaggedPlatform.featureFlagKey })
                : true;
            if (isPlatformAvailable) {
                availablePlatformKeys.push(platformKey);
            }
        }

        const disconnectedPlatforms = await this._getDisconnectedPlatformsForRestaurantUseCase.execute(restaurantId, userId);
        return difference(availablePlatformKeys, disconnectedPlatforms.map((e) => e.key).filter(isNotNil));
    }
}
