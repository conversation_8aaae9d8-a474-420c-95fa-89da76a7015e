import assert from 'assert';
import { AxiosResponse } from 'axios';
import { singleton } from 'tsyringe';

import { IPost, IPostWithAttachments, IPostWithAttachmentsAndThumbnail } from '@malou-io/package-models';
import {
    GmbPostsMetric,
    isNotNil,
    MalouErrorCode,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightFiltersGmb } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import { PostInsightMetric } from ':modules/credentials/platforms/gmb/interfaces';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { GmbMediaFormat, GmbMediaItemOutput } from ':modules/platforms/platforms/gmb/gmb.types';
import { MalouPostData, PlatformPostUseCases } from ':modules/posts/posts.interface';
import { SlackChannel, SlackService } from ':services/slack.service';

import PostsRepository from '../../posts.repository';
import { GmbPostMapper } from './gmb-post-mapper';
import { GmbLocalPost, GmbLocalPostState } from './gmb-post.interface';
import { GMB_PUBLISH_POST_MAX_RETRIES, GMB_PUBLISH_POST_RETRY_INTERVAL } from './gmb-posts.constants';
import { PostsInsightsKeyValue } from './posts-insights.interface';

const postMapper = new GmbPostMapper();

@singleton()
export class GmbPostsUseCases implements PlatformPostUseCases {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _gmbApiProviderUseCases: GmbApiProviderUseCases,
        private readonly _slackService: SlackService
    ) {}

    async synchronize({ platform }: { platform: Platform }): Promise<MalouPostData[]> {
        const { credentials, apiEndpoint } = platform;
        const credentialId = credentials?.[0];

        if (!credentialId || !apiEndpoint) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, { metadata: { platformId: platform._id } });
        }
        const gmbPosts = await this._gmbApiProviderUseCases.getLocationLocalPosts(credentialId.toString(), apiEndpoint);
        // if posts were deleted on platforms, set them as unpublished on malou
        const ninPostSocialIds = (gmbPosts || []).map((p) => p.name);
        await this._postsRepository.setRestaurantUnpublishedPosts({ platformId: platform._id, ninPostSocialIds });

        if (!gmbPosts) {
            return [];
        }

        const gmbPublishedPost = gmbPosts.filter((p) => {
            // take only "Live" and "Rejected" posts because "Processing" posts never seem to be published
            if ([GmbLocalPostState.LIVE, GmbLocalPostState.REJECTED].includes(p.state)) {
                // yes, posts without media exists
                if (!p.media) {
                    return true;
                }

                // take only posts with photos, we don't handle videos yet
                return p.media.every((m) => m.mediaFormat === GmbMediaFormat.PHOTO);
            }

            return false;
        });

        // if post was created on platform, create corresponding malou media object
        const mappedPosts = gmbPublishedPost.map((p) => postMapper.mapToMalouPost({ post: p, platform }));
        return mappedPosts;
    }

    /**
     * Publish local post and update socialId and publish fields on response
     * @returns mappedToMalou post
     */
    async publish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }) {
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId: post.restaurantId, key: PlatformKey.GMB },
            options: { lean: true },
        });

        const credentialId = platform?.credentials?.[0];
        const apiEndpoint = platform?.apiEndpoint;
        if (!credentialId || !apiEndpoint) {
            await this._postsRepository.upsert({
                filter: { _id: post._id },
                update: {
                    published: PostPublicationStatus.ERROR,
                    isPublishing: false,
                    errorData: MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND,
                },
            });
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const mappedPost = postMapper.mapToPlatformPost({ post });
        let attempts = 0;
        const retries = 1;
        const delayMs = 40 * TimeInMilliseconds.SECOND;
        let res: { data: GmbLocalPost<GmbMediaItemOutput> } | undefined;
        while (attempts <= retries) {
            try {
                res = await this._gmbApiProviderUseCases.createLocationLocalPost(credentialId, apiEndpoint, mappedPost);
                break;
            } catch (error: any) {
                attempts++;
                if (attempts > retries) {
                    this._slackService.sendAlert({
                        channel: SlackChannel.POSTS_V1_ALERTS,
                        data: {
                            err: error,
                            restaurantId: post.restaurantId.toString(),
                            metadata: { message: 'GMB_FAILED_TO_PUBLISH_POST', postId: post._id.toString() },
                        },
                    });
                    throw error;
                }
                logger.warn('[GMB_PUBLISH_POST_FAILED] Error trying to publish gmb post', {
                    post,
                    error,
                    errorMetadata: error instanceof MalouError ? error.metadata : null,
                });
                await waitFor(delayMs);
            }
        }

        assert(res, 'res is undefined');

        setTimeout(async () => {
            try {
                const fetched = await this.fetchPost({ post: { socialId: res.data.name, restaurantId: post.restaurantId } });
                assert(fetched, 'fetched is undefined');
                await this._postsRepository.upsert({ filter: { _id: post._id }, update: fetched });
            } catch (error) {
                logger.warn('[GMB_PUBLISH_POST] Error trying to fetch gmb post', { post, error });
            }
        }, 1);
        logger.info('[GMB_PUBLISH_POST] Ending publishing post', {
            post,
            data: res?.data || res,
        });

        return postMapper.mapToMalouPost({ post: res.data, platform: new Platform(platform) });
    }

    async fetchPost({ post }) {
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId: post.restaurantId, key: PlatformKey.GMB },
            options: { lean: true },
        });

        const credentialId = platform?.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        let retries = 0;
        let published = false;
        let res: AxiosResponse<GmbLocalPost<GmbMediaItemOutput>> | undefined;
        while (!published && retries < GMB_PUBLISH_POST_MAX_RETRIES) {
            res = await this._gmbApiProviderUseCases.getLocationLocalPost(credentialId, post.socialId);
            if (res?.data.state === GmbLocalPostState.LIVE) {
                published = true;
                break;
            }
            retries++;
            await waitFor(GMB_PUBLISH_POST_RETRY_INTERVAL);
        }
        return res ? postMapper.mapToMalouPost({ post: res.data, platform: new Platform(platform) }) : undefined;
    }

    async fetchPostsWithInsights(restaurantId: string, filters: PlatformInsightFiltersGmb) {
        const { startDate, endDate } = filters;
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.GMB);
        const credentialId = platform?.credentials?.[0];
        const apiEndpoint = platform?.apiEndpoint;

        assert(apiEndpoint, 'apiEndpoint is undefined');

        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const posts = await this._postsRepository.find({
            filter: { key: PlatformKey.GMB, restaurantId, socialId: { $exists: true } },
            projection: {
                socialId: 1,
                text: 1,
                socialAttachments: 1,
                socialCreatedAt: 1,
                key: 1,
                socialLink: 1,
            },
            options: {
                sort: { socialCreatedAt: -1 },
                lean: true,
            },
        });
        const MAX_POSTS_RETRIEVABLE = 99;
        const insightRequestBody = {
            localPostNames: posts
                .slice(0, MAX_POSTS_RETRIEVABLE)
                .map((p) => p.socialId)
                .filter(isNotNil),
            basicRequest: {
                metricRequests: [
                    {
                        metric: 'ALL',
                    },
                ],
                timeRange: {
                    startTime: startDate.toISOString(),
                    endTime: endDate.toISOString(),
                },
            },
        };

        const result = await this._gmbApiProviderUseCases.getPostsInsights(credentialId, apiEndpoint, insightRequestBody);
        const postsInsights = result?.localPostMetrics ?? [];
        const mappedInsights = this._mapGmbPostsInsights(postsInsights);
        return posts.map((p) => ({
            ...p,
            insights: p.socialId ? mappedInsights[p.socialId] : undefined,
        }));
    }

    /**
     * Updates post on gmb
     * @returns mappedToMalou post
     */
    async updatePost({ post }: { post: IPostWithAttachments }): Promise<IPost> {
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId: post.restaurantId, key: PlatformKey.GMB },
            options: { lean: true },
        });

        const credentialId = platform?.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const localPostId = post.socialId;
        const localPost = postMapper.mapToPlatformPost({ post });
        const res = await this._gmbApiProviderUseCases.updateLocationLocalPost(credentialId, localPostId, localPost);
        assert(res, 'res is undefined');
        return postMapper.mapToMalouPost({ post: res.data, platform: new Platform(platform) }) as IPost;
    }

    /**
     * Deletes post on gmb
     * @param {Object} object
     * @param {User} object.user
     * @param {Post} object.post
     * @returns {Promise<PostI>} mappedToMalou deleted post
     */
    async deletePost({ post }: { post: IPost }): Promise<any> {
        const platform = await this._platformsRepository.findOne({
            filter: { restaurantId: post.restaurantId, key: PlatformKey.GMB },
            options: { lean: true },
        });

        const credentialId = platform?.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        return this._gmbApiProviderUseCases.deleteLocationLocalPost(credentialId, post.socialId);
    }

    publishStory(platformId: string, creationId: string, type: MediaType, postId: string): Promise<IPost> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'publishStory is not implemented for Google',
            metadata: { platformId, creationId, type, postId },
        });
    }

    synchronizeStories(platform: Platform): Promise<Partial<IPost>[]> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'synchronizeStories is not implemented for Google',
            metadata: { platform },
        });
    }

    completePublish({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<MalouPostData | void> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'completePublish is not implemented for Google',
            metadata: { post },
        });
    }

    getCompetitorsPosts(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'getCompetitorsPosts is not implemented for Google',
            metadata: { platformKey: PlatformKey.GMB },
        });
    }

    createStoryList(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'createStoryList is not implemented for Google',
            metadata: { platformKey: PlatformKey.GMB },
        });
    }

    upsertStoryAndSaveAttachments(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'upsertStoryAndSaveAttachments is not implemented for Google',
            metadata: { platformKey: PlatformKey.GMB },
        });
    }

    searchAccounts(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'searchAccounts is not implemented for Google',
            metadata: { platformKey: PlatformKey.GMB },
        });
    }

    oembed(): void {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'oembed is not implemented for Google',
            metadata: { platformKey: PlatformKey.GMB },
        });
    }

    private _mapGmbPostsInsights(postsInsights: PostInsightMetric[]): PostsInsightsKeyValue {
        const postsInsightsKeyValue: PostsInsightsKeyValue = {};
        postsInsights.forEach((postInsight) => {
            const { localPostName, metricValues } = postInsight;
            const viewed = parseInt(
                metricValues.find((m) => m.metric === GmbPostsMetric.LOCAL_POST_VIEWS_SEARCH)?.totalValue?.value ?? '0',
                10
            );
            const clicked = parseInt(
                metricValues.find((m) => m.metric === GmbPostsMetric.LOCAL_POST_ACTIONS_CALL_TO_ACTION)?.totalValue?.value ?? '0',
                10
            );
            postsInsightsKeyValue[localPostName] = { viewed, clicked };
        });
        return postsInsightsKeyValue;
    }
}
