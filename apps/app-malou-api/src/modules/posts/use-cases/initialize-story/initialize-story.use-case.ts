import assert from 'assert';
import { omit } from 'lodash';
import { singleton } from 'tsyringe';

import { IPost, toDbId } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostPublicationStatus } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class InitializeStoryUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _agendaSingleton: AgendaSingleton,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _slackService: SlackService
    ) {}

    async execute(restaurantId: string, malouStoryId: string, keys: PlatformKey[]): Promise<void> {
        let firstPostId: string | undefined;
        try {
            const posts = await this._postsRepository.find({
                filter: { isStory: true, malouStoryId },
                options: { lean: true, sort: { plannedPublicationDate: -1 } },
            });

            const firstPost = posts[0];
            firstPostId = firstPost._id.toString();

            await Promise.all(posts.map((post) => this._createStoryForEachPlatform(post, restaurantId)));

            if (firstPost?.plannedPublicationDate) {
                for (const key of keys) {
                    const jobData = {
                        platformKey: key,
                        malouStoryId,
                        restaurantId,
                    };
                    logger.info('[INITIALIZE_STORY] will prepare story:', { jobData });
                    // TODO: can be fetched outside
                    await this._agendaSingleton.now(AgendaJobName.PREPARE_STORY, jobData);
                }
            } else {
                logger.error('[INITIALIZE_STORY] Error: no post with plannedPublicationDate found', { malouStoryId, posts, restaurantId });
            }
        } catch (err) {
            logger.error(`[INITIALIZE_STORY] Error: ${JSON.stringify(err)} - ${err}`);
            if (firstPostId) {
                void this._createPostErrorNotificationProducer.execute({ postId: firstPostId });
            }
            this._slackService.sendAlert({
                channel: SlackChannel.POSTS_V1_ALERTS,
                data: {
                    err,
                    metadata: { message: 'INITIALIZE_STORY_ERROR', firstPostId },
                },
            });
            throw err;
        }
    }

    private async _createStoryForEachPlatform(post: IPost, restaurantId: string): Promise<void> {
        const keys = post.keys;
        assert(keys, 'Missing keys');
        await this._postsRepository.updateOne({
            filter: { _id: post._id },
            update: { key: keys[0], keys: [] },
        });

        // TODO: Passer en Promise.all
        for (const key of keys) {
            const platform = await this._platformsRepository.findOne({
                filter: { restaurantId: toDbId(restaurantId), key },
                options: { lean: true },
                projection: { _id: 1 },
            });
            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: 'Platform not found',
                    metadata: { restaurantId, key },
                });
            }
            const existingStory = await this._postsRepository.findOne({
                filter: { _id: post._id, key },
                options: { lean: true },
                projection: { _id: 1 },
            });
            if (!existingStory) {
                const sortDate = post.socialCreatedAt ?? post.plannedPublicationDate ?? new Date();
                await this._postsRepository.create({
                    data: {
                        ...omit(post, ['_id', 'platformId']),
                        key: PlatformKey[key.toUpperCase()],
                        keys: [],
                        platformId: platform._id,
                        malouStoryId: post.malouStoryId,
                        isStory: true,
                        published: PostPublicationStatus.PENDING,
                        sortDate,
                    },
                    options: { lean: true },
                });
            } else {
                await this._postsRepository.updateOne({
                    filter: { _id: existingStory._id },
                    update: { platformId: platform._id, keys: [], published: PostPublicationStatus.PENDING },
                });
            }
        }
    }
}
