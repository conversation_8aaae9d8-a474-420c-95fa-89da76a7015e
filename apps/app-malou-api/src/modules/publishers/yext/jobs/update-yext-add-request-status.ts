import 'reflect-metadata';

import ':env';

import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { MalouErrorCode, TimeInMinutes } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { updateYextAddRequestStatusValidator } from ':helpers/validators/jobs/update-yext-add-request-status.job.validator';
import UpdateAddRequestStatusUseCase from ':modules/publishers/yext/use-cases/update-add-request-status/update-add-request-status.use-case';
import ':plugins/db';
import { SlackChannel, SlackService } from ':services/slack.service';

const CHECK_ADD_REQUEST_STATUS_MAX_ATTEMPTS = 7;
const DEFAULT_RETRY_DELAY = 2 * TimeInMinutes.DAY;

function computeRetryDelayInMinutes(failCount: number): number {
    const retryDelay = [
        5 * TimeInMinutes.MINUTE,
        TimeInMinutes.HOUR,
        12 * TimeInMinutes.HOUR,
        12 * TimeInMinutes.HOUR,
        12 * TimeInMinutes.HOUR,
        1 * TimeInMinutes.DAY,
    ][failCount];
    return retryDelay ?? DEFAULT_RETRY_DELAY;
}

type DataAttributes = z.infer<typeof updateYextAddRequestStatusValidator>;

@singleton()
export class UpdateYextAddRequestStatusJob extends GenericJobDefinition {
    constructor(
        private readonly _updateAddRequestStatusUseCase: UpdateAddRequestStatusUseCase,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.UPDATE_YEXT_ADD_REQUEST_STATUS,
            shouldDeleteJobOnSuccess: true,
            retryStrategy: {
                maxAttemptsCount: CHECK_ADD_REQUEST_STATUS_MAX_ATTEMPTS,
                computeRetryDelayInMinutes,
                executeAfterMaxAttemptsCount: async (_err: Error, job: Job<DataAttributes>) => {
                    this._slackService.sendAlert({
                        data: {
                            err: new MalouError(MalouErrorCode.UPDATE_YEXT_ADD_REQUEST_STATUS_MAX_RETRY, {
                                message: 'update_yext_add_request_status_max_retry:  Max retry attempts reached',
                            }),
                            metadata: job.attrs.data,
                        },
                        channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
                        shouldPing: true,
                    });
                },
            },
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const jobData = updateYextAddRequestStatusValidator.parse(job.attrs.data);

        const yextLocation = await this._updateAddRequestStatusUseCase.execute(jobData.yextLocationId);

        // Throw an error to retry the job until the add request is processed
        if (!yextLocation.isInAFinalState()) {
            throw new MalouError(MalouErrorCode.YEXT_LOCATION_STATUS_IS_NOT_PROCESSED, {
                message: 'Yext location status is not processed yet',
            });
        }
    }
}
