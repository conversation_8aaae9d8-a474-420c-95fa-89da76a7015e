import 'reflect-metadata';

import ':env';

import { Job } from 'agenda';
import { isError } from 'lodash';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { MalouErrorCode } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { locationDeletionValidator } from ':helpers/validators/jobs/location-deletion.job.validator';
import DeleteLocationUseCase from ':modules/publishers/yext/use-cases/delete-location/delete-location.use-case';
import ':plugins/db';
import { SlackChannel, SlackService } from ':services/slack.service';

type DataAttributes = z.infer<typeof locationDeletionValidator>;

@singleton()
export class LocationDeletionJob extends GenericJobDefinition {
    constructor(
        private readonly _yextDeleteLocationUseCase: DeleteLocationUseCase,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.YEXT_DELETE_LOCATION,
            shouldDeleteJobOnSuccess: true,
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const jobData = locationDeletionValidator.parse(job.attrs.data);

        try {
            await this._yextDeleteLocationUseCase.execute(jobData.restaurantId);
        } catch (e: unknown) {
            this._slackService.sendAlert({
                channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.YEXT_DELETE_LOCATION_FAILED, {
                        message: isError(e) ? e.message : '-',
                        metadata: jobData,
                    }),
                    restaurantId: jobData.restaurantId,
                },
                shouldPing: true,
            });

            throw e;
        }
    }
}
