import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { UpdateCurrentReleaseBodyDto, updateCurrentReleaseBodyValidator } from '@malou-io/package-dto';

import { Body } from ':helpers/decorators/validators';
import { clearMaintenanceCache } from ':plugins/maintenance-header.middleware';

import MaintenanceRepository from './maintenance.repository';

@singleton()
export default class MaintenanceController {
    constructor(private _maintenanceRepository: MaintenanceRepository) {}

    handleGetMaintenance = async (_req: Request, res: Response, next: NextFunction) => {
        try {
            let maintenanceMode = await this._maintenanceRepository.findOne({ filter: {} });
            if (!maintenanceMode) {
                maintenanceMode = await this._maintenanceRepository.insertFirst();
            }
            return res.json({
                data: maintenanceMode,
            });
        } catch (err) {
            return next(err);
        }
    };

    handleSetMaintenance = async (req: Request, res: Response, next: NextFunction) => {
        try {
            await this._maintenanceRepository.findOneAndUpdate({ filter: {}, update: req.body });
            clearMaintenanceCache();
            return res.json({
                msg: 'success',
            });
        } catch (err) {
            return next(err);
        }
    };

    handleGetCurrentRelease = async (_req: Request, res: Response<{ currentRelease: string }>, next: NextFunction) => {
        try {
            const maintenanceMode = await this._maintenanceRepository.findOne({ filter: {} });
            assert(maintenanceMode);
            return res.json({
                currentRelease: maintenanceMode.currentRelease,
            });
        } catch (err) {
            return next(err);
        }
    };

    @Body(updateCurrentReleaseBodyValidator)
    async handleSetCurrentRelease(req: Request<any, any, UpdateCurrentReleaseBodyDto>, res: Response) {
        try {
            await this._maintenanceRepository.findOneAndUpdate({ filter: {}, update: { currentRelease: req.body.currentRelease } });
            clearMaintenanceCache();
            return res.json({
                msg: 'success',
            });
        } catch (err) {
            return res.json({ msg: 'error', err });
        }
    }
}
