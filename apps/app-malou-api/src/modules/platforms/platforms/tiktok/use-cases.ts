import { singleton } from 'tsyringe';

import { IPlatform } from '@malou-io/package-models';
import { MalouErrorCode } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class TiktokPlatformsUseCases {
    constructor(
        private readonly _tiktokQueryCreatorInfoUseCase: TiktokQueryCreatorInfoUseCase,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async getOverviewData({ restaurantId }: { restaurantId: string }): Promise<{ name: string }> {
        try {
            const userInfo = await this._tiktokQueryCreatorInfoUseCase.execute({ restaurantId });
            return { name: userInfo.creatorUserName };
        } catch (error: any) {
            const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: restaurantId }, options: { lean: true } });
            if (restaurant) {
                logger.warn('[TIKTOK_OVERVIEW_ERROR] An error occurred while fetching the creator info for restaurant', restaurant, error);
            } else {
                logger.warn('[TIKTOK_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
            throw new MalouError(MalouErrorCode.RETRIEVE_OVERVIEW_DATA_ERROR, {
                message: error.message ?? error,
                metadata: { rawError: error },
            });
        }
    }

    mapOverviewDataToMalou(data: { name: string }, _restaurantId: string): Partial<IPlatform> {
        return data.name
            ? {
                  name: data.name,
                  socialLink: `https://www.tiktok.com/@${data.name}`,
              }
            : {};
    }
}
