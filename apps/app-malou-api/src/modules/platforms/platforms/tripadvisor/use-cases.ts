import { DateTime } from 'luxon';
import { inject, singleton } from 'tsyringe';

import { ID, IPlatform } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, PlatformKey, platformsKeys, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { randomString, retryPromisePlatformsScrapper } from ':helpers/utils';
import { fetchUntilWorks } from ':microservices/node-crawler';
import * as platformsScrapper from ':microservices/platforms-scrapper';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { PlatformUseCases } from ':modules/platforms/platforms.interface';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { TripadvisorMapper } from ':modules/platforms/platforms/tripadvisor/tripadvisor-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackService } from ':services/slack.service';

@singleton()
export class TripadvisorPlatformsUseCases implements PlatformUseCases {
    constructor(
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _tripadvisorApiMapper: TripadvisorMapper,
        @inject(SlackService)
        private readonly _slackService: SlackService
    ) {}

    async getOverviewData({ restaurantId }: { restaurantId: string }) {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TRIPADVISOR);
        if (!platform || !platform.socialId) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }

        const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: restaurantId }, options: { lean: true } });

        let overviewData;
        let isClaimed;
        let locationData;
        let menuUrl: null | string;
        let tags;
        try {
            // get other data with the crawler
            overviewData = await fetchUntilWorks({
                params: {
                    url: `${platformsKeys.TRIPADVISOR.baseUrl}/data/1.0/restaurant/${platform.socialId}/overview`,
                    headers: { ...Config.platforms.tripadvisor.api.headers, uid: randomString(10) },
                },
                isResponseValid: (res) => res?.name,
            });
        } catch (error: any) {
            overviewData = null;
            if (restaurant) {
                // eslint-disable-next-line max-len
                logger.warn(`[TRIPADVISOR_OVERVIEW_ERROR] An error occurred while fetching the overview data`, restaurant, error);
            } else {
                logger.warn('[TRIPADVISOR_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
        }

        try {
            isClaimed = await fetchUntilWorks({
                params: {
                    url: `${platformsKeys.TRIPADVISOR.baseUrl}/data/1.0/restaurant/${platform.socialId}/ownerStatus`,
                    headers: { ...Config.platforms.tripadvisor.api.headers, uid: randomString(10) },
                },
                isResponseValid: (res) => res && (res.isVerified === true || res.isVerified === false),
            });
        } catch (error: any) {
            isClaimed = null;
            if (restaurant) {
                // eslint-disable-next-line max-len
                logger.warn(`[TRIPADVISOR_OVERVIEW_ERROR] An error occurred while fetching the claimed data`, restaurant, error);
            } else {
                logger.warn('[TRIPADVISOR_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
        }

        try {
            locationData = await fetchUntilWorks({
                params: {
                    url: `${platformsKeys.TRIPADVISOR.baseUrl}/data/1.0/location/${platform.socialId}`,
                    headers: { ...Config.platforms.tripadvisor.api.headers, uid: randomString(10) },
                },
                isResponseValid: (res) => res?.name,
            });
        } catch (error: any) {
            locationData = null;
            // eslint-disable-next-line max-len
            if (restaurant) {
                logger.warn(`[TRIPADVISOR_OVERVIEW_ERROR] An error occurred while fetching the location data`, restaurant, error);
            } else {
                logger.warn('[TRIPADVISOR_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
        }

        try {
            // get menuUrl with the scrapper
            const platformsScrapperEndpoint = `/${platform.socialId}`;
            const promiseLocationOverview = platformsScrapper.locationOverview(PlatformKey.TRIPADVISOR, restaurantId.toString(), {
                endpoint: platformsScrapperEndpoint,
            });

            const locationOverviewResults = await retryPromisePlatformsScrapper(
                promiseLocationOverview,
                Config.services.platformsScrapper.retries,
                2000
            );
            menuUrl = null;
            if (locationOverviewResults?.data) {
                menuUrl = locationOverviewResults.data.menuUrl;
                tags = locationOverviewResults.data.tags;
            }
        } catch (error: any) {
            menuUrl = null;
            tags = null;
            // eslint-disable-next-line max-len
            if (restaurant) {
                logger.warn(`[TRIPADVISOR_OVERVIEW_ERROR] An error occurred while fetching the menu data`, restaurant, error);
            } else {
                logger.warn('[TRIPADVISOR_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
        }

        return {
            ...overviewData,
            ...isClaimed,
            ...locationData,
            menuUrl,
            ...tags,
        };
    }

    mapOverviewDataToMalou(data) {
        return this._tripadvisorApiMapper.toMalouMapper(data);
    }

    async upsertLinkedComponents({ platform }: { platform?: IPlatform }) {
        const today = DateTime.local();
        const { year, month, day } = today;
        const rating = platform?.rating;
        if (isNotNil(rating)) {
            const filter = {
                socialId: platform?.socialId ?? undefined,
                platformKey: platform?.key,
                metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                year,
                month: month - 1,
                day,
            };
            const insight = {
                ...filter,
                value: rating,
                date: today.toJSDate(),
            };
            try {
                await this._platformInsightsRepository.upsert({ filter, update: insight });
            } catch (e: any) {
                if (e.code !== 11000) {
                    throw e;
                }
            }
        }
    }

    getSocialId(_restaurantId: string): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, { message: 'TripadvisorPlatformsUseCases does not implement getSocialId !' });
    }

    scrapPlatformEndpoint(_endpoint: string): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorPlatformsUseCases does not implement scrapPlatformEndpoint !',
        });
    }

    getLocationData(_params: { credentialId?: ID; socialId?: string; apiEndpointV2?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorPlatformsUseCases does not implement getLocationData !',
        });
    }

    getProfileAndCoverMedia(_params: { credentialId?: ID; socialId?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorPlatformsUseCases does not implement getProfileAndCoverMedia !',
        });
    }
}
