import { singleton } from 'tsyringe';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>e, PopulateBuilderHelper } from '@malou-io/package-models';

import { AttributesRepository } from ':modules/attributes/attributes.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class FindAttributeService {
    constructor(
        private readonly _attributesRepository: AttributesRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(attributeId: string): Promise<PopulateBuilderHelper<IAttribute, [{ path: 'platformAttributes' }]> | null> {
        const attribute = await this._attributesRepository.findOne({
            filter: { attributeId },
            options: { populate: [{ path: 'platformAttributes' }], lean: true },
        });
        if (!attribute) {
            this._slackService.sendMessage({
                text: await this._formatUpdatedCategoriesMessageForSlack(attributeId),
                channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
            });
            return null;
        }
        return attribute;
    }

    private async _formatUpdatedCategoriesMessageForSlack(attributeId: string): Promise<string> {
        const context = await this._slackService.createContextForSlack();

        return `🫃 *New attribute* "${attributeId}" needs to be inserted in db${context}`;
    }
}
