import { DateTime } from 'luxon';
import request from 'request-promise-native';
import { singleton } from 'tsyringe';

import { ID, IPlatform } from '@malou-io/package-models';
import { isNotNil, MalouErrorCode, PlatformKey, StoredInDBInsightsMetric, waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { retryPromisePlatformsScrapper } from ':helpers/utils';
import * as platformsScrapper from ':microservices/platforms-scrapper';
import { YelpCredentialsUseCases } from ':modules/credentials/platforms/yelp/use-cases';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { PlatformUseCases } from ':modules/platforms/platforms.interface';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { YelpMapper } from ':modules/platforms/platforms/yelp/yelp-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class YelpPlatformsUseCases implements PlatformUseCases {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _yelpCredentialsUseCases: YelpCredentialsUseCases
    ) {}

    async getOverviewData({ restaurantId }: { restaurantId: string }) {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.YELP);
        if (!platform || !platform.socialId) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }
        const businessId = platform.socialId;
        const { retries } = Config.services.platformsScrapper;

        const promiseLocationOverview = platformsScrapper.locationOverview(PlatformKey.YELP, restaurantId.toString(), {
            endpoint: platform.socialId,
        });
        const scrapperRes = await retryPromisePlatformsScrapper(promiseLocationOverview, retries, 2000);

        if (scrapperRes.error || !scrapperRes.data) {
            scrapperRes.data = null;
        }

        try {
            const apiRes = await this._yelpCredentialsUseCases.businessDetails(businessId);
            return {
                website: scrapperRes.data?.website,
                description: scrapperRes.data?.description,
                attributes: scrapperRes.data?.attributes,
                ...apiRes,
                socialId: businessId,
            };
        } catch (error: any) {
            const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: restaurantId }, options: { lean: true } });
            if (restaurant) {
                logger.warn('[YELP_OVERVIEW_ERROR] An error occurred while fetching the overview data', restaurant, error);
            } else {
                logger.warn('[YELP_OVERVIEW_ERROR] Restaurant not found in error', { restaurantId, error });
            }
            if (String(error).match(/BUSINESS_UNAVAILABLE/)) {
                return { ...scrapperRes.data, socialId: businessId };
            }
            logger.warn('[YELP_OVERVIEW_ERROR]', error);
            throw error;
        }
    }

    async scrapPlatformEndpoint(slugOrId: string): Promise<string> {
        try {
            const { id } = await this._yelpCredentialsUseCases.businessDetails(slugOrId);
            return id;
        } catch (error) {
            logger.info('[YELP_API_FETCH_FAILED]', error);
            // In case the API did not respond, we will try by scraping the yelp website
            // TODO: check the log above. If it never happens, we can remove this fallback
            const endpoint = `https://www.yelp.com/biz/${slugOrId}`;
            const htmlData = await this._fetchWebsiteData(endpoint); // UNSTABLE
            const socialIdCaptureRegex = /(?<=writeareview\/biz\/)((?:\d|-|_|\w)+)/;
            const socialId = htmlData.match(socialIdCaptureRegex)?.[0];
            if (!socialId) {
                throw new MalouError(MalouErrorCode.FETCH_YELP_WEBSITE_FAILED);
            }
            return socialId;
        }
    }

    mapOverviewDataToMalou(data) {
        const mapper = new YelpMapper();
        const malouData = mapper.toMalouMapper(data);
        return malouData;
    }

    async upsertLinkedComponents({ platform }: { platform?: IPlatform }) {
        const today = DateTime.local();
        const { year, month, day } = today;
        const rating = platform?.rating;
        if (isNotNil(rating)) {
            const filter = {
                socialId: platform?.socialId ?? undefined,
                platformKey: platform?.key,
                metric: StoredInDBInsightsMetric.PLATFORM_RATING,
                year,
                month: month - 1,
                day,
            };
            const insight = {
                ...filter,
                value: rating,
                date: today.toJSDate(),
            };
            try {
                await this._platformInsightsRepository.upsert({ filter, update: insight });
            } catch (e: any) {
                if (e.code !== 11000) {
                    throw e;
                }
            }
        }
    }

    getSocialId(_restaurantId: string): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.YELP,
            },
        });
    }

    getLocationData(_params: { credentialId?: ID; socialId?: string; apiEndpointV2?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.YELP,
            },
        });
    }

    getProfileAndCoverMedia(_params: { credentialId?: ID; socialId?: string }): Promise<any> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.YELP,
            },
        });
    }

    private async _fetchWebsiteData(endpoint: string): Promise<any> {
        const maxRetries = 3;
        let attempt = 0;

        while (attempt < maxRetries) {
            try {
                return await request.get(encodeURI(endpoint), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                });
            } catch (error) {
                attempt++;
                if (attempt >= maxRetries) {
                    logger.error('[YELP_WEBSITE_FETCH_FAILED]', error);
                    throw new MalouError(MalouErrorCode.FETCH_YELP_WEBSITE_FAILED);
                }
                await waitFor(1000 * attempt);
            }
        }
    }
}
