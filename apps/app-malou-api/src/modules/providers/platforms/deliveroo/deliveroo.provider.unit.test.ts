import { container } from 'tsyringe';

import { IDeliverooCredential } from '@malou-io/package-models';

import { DeliverooProvider } from ':modules/providers/platforms/deliveroo/deliveroo.provider';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackService } from ':services/slack.service';

import * as mockApiProvider from './api-provider';

jest.mock('./api-provider');

describe('Deliveroo Provider Use cases', () => {
    class SlackServiceMock {
        async sendAlert(_: Record<string, string>): Promise<void> {
            return;
        }
    }
    class RestaurantsRepositoryMock {
        findOne() {
            return { id: 'res1', name: 'mockRestaurant' };
        }
    }

    container.register(SlackService, { useValue: new SlackServiceMock() as any });
    container.register(RestaurantsRepository, { useValue: new RestaurantsRepositoryMock() as any });
    const deliverooProvider = container.resolve(DeliverooProvider);

    describe('get all reviews', () => {
        beforeEach(() => {
            jest.spyOn(mockApiProvider, '_callDeliverooApi').mockReset();
        });
        const credential = {
            accessToken: 'accessToken',
            tokenType: 'tokenType',
        } as unknown as IDeliverooCredential;
        const reviews = [
            {
                order_uuid: 'order_uuid1',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid2',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid3',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid4',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid5',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid6',
                created_at: 123456789,
            },
            {
                order_uuid: 'order_uuid7',
                created_at: 123456789,
            },
        ];
        it('should return all reviews', async () => {
            jest.spyOn(mockApiProvider, '_callDeliverooApi')
                .mockResolvedValueOnce({
                    reviews: reviews.slice(0, 5),
                    total_reviews: 7,
                })
                .mockResolvedValueOnce({
                    reviews: reviews.slice(4),
                    total_reviews: 7,
                })
                .mockResolvedValueOnce({
                    reviews: [],
                    total_reviews: 7,
                });
            const socialId = 'socialId';
            const result = await deliverooProvider.getAllReviews(credential, socialId);
            expect(result).toEqual(reviews);
        });

        it('should throw if first request fails', async () => {
            jest.spyOn(mockApiProvider, '_callDeliverooApi').mockRejectedValueOnce(new Error('mocked error'));
            const socialId = 'socialId';
            await expect(deliverooProvider.getAllReviews(credential, socialId)).rejects.toEqual(new Error('mocked error'));
        });

        it('should return part of reviews if nth request fails', async () => {
            jest.spyOn(mockApiProvider, '_callDeliverooApi')
                .mockResolvedValueOnce({
                    reviews: reviews.slice(0, 4),
                    total_reviews: 7,
                })
                .mockRejectedValueOnce(new Error('error'));
            const socialId = 'socialId';
            const result = await deliverooProvider.getAllReviews(credential, socialId);
            expect(result).toEqual(reviews.slice(0, 4));
        });
    });

    afterAll(jest.restoreAllMocks);
});
