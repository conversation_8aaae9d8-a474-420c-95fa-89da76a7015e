import { singleton } from 'tsyringe';

import { EntityRepository, IProcessingMedia, ProcessingMediaModel, toDbId } from '@malou-io/package-models';
import { ProcessingMediaErrorCode, ProcessingMediaStatus } from '@malou-io/package-utils';

@singleton()
export class ProcessingMediasRepository extends EntityRepository<IProcessingMedia> {
    constructor() {
        super(ProcessingMediaModel);
    }

    async findById(id: string): Promise<IProcessingMedia | null> {
        return await this.findOne({
            filter: { _id: toDbId(id) },
            options: { lean: true },
        });
    }

    async createInProgressDocument(): Promise<IProcessingMedia> {
        return await this.create({
            data: { status: ProcessingMediaStatus.IN_PROGRESS },
            options: { lean: true },
        });
    }

    async setInError(id: string, errorCode: ProcessingMediaErrorCode): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(id), status: ProcessingMediaStatus.IN_PROGRESS },
            update: { status: ProcessingMediaStatus.ERROR, errorCode },
            options: { lean: true, new: true },
        });
    }

    async setInSuccess(id: string, mediaId: string): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(id), status: ProcessingMediaStatus.IN_PROGRESS },
            update: { status: ProcessingMediaStatus.SUCCESS, mediaId: toDbId(mediaId) },
            options: { lean: true, new: true },
        });
    }
}
