import { singleton } from 'tsyringe';

import { IProcessingMedia } from '@malou-io/package-models';

import { ProcessingMediasRepository } from ':modules/processing-medias/processing-medias.repository';

@singleton()
export class GetProcessingMediaUseCase {
    constructor(private readonly _processingMediasRepository: ProcessingMediasRepository) {}

    async execute(id: string): Promise<IProcessingMedia | null> {
        return await this._processingMediasRepository.findById(id);
    }
}
