import { err, ok, Result } from 'neverthrow';
import { singleton } from 'tsyringe';

import { IProcessingMedia } from '@malou-io/package-models';
import { ProcessingMediaStatus, retry, TimeInMilliseconds } from '@malou-io/package-utils';

import { ProcessingMediasRepository } from ':modules/processing-medias/processing-medias.repository';

const WaitForMediaProcessingUseCaseError = {
    NOT_FOUND: 'NOT_FOUND',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

type WaitForMediaProcessingUseCaseError = (typeof WaitForMediaProcessingUseCaseError)[keyof typeof WaitForMediaProcessingUseCaseError];

@singleton()
export class WaitForMediaProcessingUseCase {
    constructor(private readonly _processingMediasRepository: ProcessingMediasRepository) {}

    async execute(processingMediaId: string): Promise<Result<IProcessingMedia, WaitForMediaProcessingUseCaseError>> {
        const processingMediaResult = await retry(() => this._processingMediasRepository.findById(processingMediaId), {
            attempts: 60,
            isSuccess: (r) => r === null || r.status !== ProcessingMediaStatus.IN_PROGRESS,
            backoffStrategy: (attempt) => 2 ** attempt * 50 * TimeInMilliseconds.MILLISECOND,
            minDelayInMs: 100 * TimeInMilliseconds.MILLISECOND,
            maxDelayInMs: TimeInMilliseconds.SECOND,
        });
        if (processingMediaResult.isOk()) {
            if (processingMediaResult.value) {
                return ok(processingMediaResult.value);
            } else {
                return err(WaitForMediaProcessingUseCaseError.NOT_FOUND);
            }
        }
        return err(WaitForMediaProcessingUseCaseError.UNKNOWN_ERROR);
    }
}
