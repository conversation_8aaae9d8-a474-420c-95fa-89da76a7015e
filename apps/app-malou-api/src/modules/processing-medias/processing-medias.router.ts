import { Router } from 'express';
import { singleton } from 'tsyringe';

import { ProcessingMediasController } from ':modules/processing-medias/processing-medias.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class ProcessingMediasRouter {
    constructor(private _processingMediasController: ProcessingMediasController) {}

    public init(router: Router): void {
        router.get('/processing-medias/:id', authorize(), (req, res, next) =>
            this._processingMediasController.getProcessingMedia(req, res, next)
        );
    }
}
