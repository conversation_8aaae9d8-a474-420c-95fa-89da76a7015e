import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import { GetProcessingMediaParamsDto, getProcessingMediaParamsValidator, GetProcessingMediaResponseDto } from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { Params } from ':helpers/decorators/validators';
import { GetProcessingMediaUseCase } from ':modules/processing-medias/use-cases/get-processing-media.use-case';

@singleton()
export class ProcessingMediasController {
    constructor(private readonly _getProcessingMediaUseCase: GetProcessingMediaUseCase) {}

    @Params(getProcessingMediaParamsValidator)
    async getProcessingMedia(
        req: Request<GetProcessingMediaParamsDto>,
        res: Response<ApiResultV2<GetProcessingMediaResponseDto>>,
        next: NextFunction
    ) {
        try {
            const processingMedia = await this._getProcessingMediaUseCase.execute(req.params.id);

            if (!processingMedia) {
                res.status(404).end();
                return;
            }

            res.json({
                data: {
                    id: processingMedia._id.toString(),
                    status: processingMedia.status,
                    mediaId: processingMedia.mediaId?.toString(),
                    errorCode: processingMedia.errorCode,
                },
            });
        } catch (error) {
            next(error);
        }
    }
}
