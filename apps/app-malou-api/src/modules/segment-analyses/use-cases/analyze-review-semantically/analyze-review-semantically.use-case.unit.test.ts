import { container } from 'tsyringe';

import { DbId, newDbId, toDbId } from '@malou-io/package-models';
import {
    ApplicationLanguage,
    MalouErrorCode,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisSubCategory,
    ReviewAnalysisTag,
    SemanticAnalysisFetchStatus,
    TranslationSource,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import {
    GenerateReviewSemanticAnalysisService,
    SegmentAnalysisWithNewTopic,
} from ':modules/ai/services/generate-review-semantic-analysis.service';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { AnalyzeReviewSemanticallyUseCase } from ':modules/segment-analyses/use-cases/analyze-review-semantically/analyze-review-semantically.use-case';

const reviewSocialId = 'hello';
const segmentAnalysesParentTopicId = newDbId().toString();
const restaurantId = newDbId().toString();
const semanticAnalysis: SegmentAnalysisWithNewTopic[] = [
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId1',
        platformKey: PlatformKey.GMB,
        topic: 'crepes',
        category: ReviewAnalysisTag.HYGIENE,
        sentiment: ReviewAnalysisSentiment.NEGATIVE,
        segment: 'Les crepes étaient bof',
        aiFoundSegment: 'Les crepes étaient bof',
        isRatingTagOrMenuItem: false,
        segmentNewParentTopic: {
            language: ApplicationLanguage.EN,
            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
            fr: 'Mon topic 1',
            en: 'My topic 1',
            es: 'Mi topic 1',
            it: 'Mio topic 1',
        },
    },
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId2',
        platformKey: PlatformKey.GMB,
        topic: 'pizza',
        category: ReviewAnalysisTag.ATMOSPHERE,
        sentiment: ReviewAnalysisSentiment.POSITIVE,
        segment: "J'adore la pizza",
        aiFoundSegment: "J'adore la pizza",
        isRatingTagOrMenuItem: false,
        segmentAnalysisParentTopicId: segmentAnalysesParentTopicId,
    },
    {
        reviewSocialId,
        restaurantId,
        reviewSocialCreatedAt: new Date(),
        platformSocialId: 'socialId3',
        platformKey: PlatformKey.GMB,
        topic: 'ambiance',
        category: ReviewAnalysisTag.FOOD,
        subcategory: ReviewAnalysisSubCategory.STAFF_MEMBERS,
        sentiment: ReviewAnalysisSentiment.POSITIVE,
        segment: 'Ambiance sympa',
        aiFoundSegment: 'Ambiance sympa',
        isRatingTagOrMenuItem: false,
        segmentNewParentTopic: {
            language: ApplicationLanguage.EN,
            source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
            fr: 'Mon topic 2',
            en: 'My topic 2',
            es: 'Mi topic 2',
            it: 'Mio topic 2',
        },
    },
];

let analyzeReviewSemanticallyUseCase: AnalyzeReviewSemanticallyUseCase;
let generateReviewSemanticAnalysisService: GenerateReviewSemanticAnalysisService;
let generateReviewSemanticAnalysisServiceSpy: jest.SpyInstance;

describe('AnalyzeReviewSemanticallyUseCase', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories([
            'RestaurantsRepository',
            'ReviewsRepository',
            'SegmentAnalysesRepository',
            'SegmentAnalysisParentTopicsRepository',
            'PlatformsRepository',
        ]);

        generateReviewSemanticAnalysisService = container.resolve(GenerateReviewSemanticAnalysisService);
        generateReviewSemanticAnalysisService.execute = jest.fn().mockResolvedValue(semanticAnalysis);
        generateReviewSemanticAnalysisServiceSpy = jest.spyOn(generateReviewSemanticAnalysisService, 'execute');

        analyzeReviewSemanticallyUseCase = container.resolve(AnalyzeReviewSemanticallyUseCase);
    });

    describe('execute', () => {
        it('should raise error if review not found', async () => {
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                },
                expectedErrorCode: MalouErrorCode.REVIEW_NOT_FOUND,
            });

            await testCase.build();
            const expectedErrorCode = testCase.getExpectedErrorCode();

            await expect(
                analyzeReviewSemanticallyUseCase.execute({
                    reviewId: newDbId().toString(),
                    restaurantId,
                    isPrivateReview: false,
                })
            ).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: expectedErrorCode,
                })
            );
        });

        it('should not call lambda if review already has segment analyses', async () => {
            const segmentAnalysesRepository = container.resolve(SegmentAnalysesRepository);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .text('Hello')
                                    .socialCreatedAt(new Date())
                                    .socialId(reviewSocialId)
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data() {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(reviewSocialId)
                                    .platformKey(PlatformKey.GMB)
                                    .segment('segment1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(reviewSocialId)
                                    .platformKey(PlatformKey.GMB)
                                    .segment('segment2')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = seededObjects.reviews[0]._id.toString();

            await analyzeReviewSemanticallyUseCase.execute({
                reviewId,
                restaurantId,
                isPrivateReview: false,
            });

            const segmentAnalyses = await segmentAnalysesRepository.find({ filter: { reviewSocialId, platformKey: PlatformKey.GMB } });
            expect(segmentAnalyses).toHaveLength(2);

            expect(generateReviewSemanticAnalysisServiceSpy).not.toHaveBeenCalled();
        });

        it('should create one document for each received segment', async () => {
            const segmentAnalysesRepository = container.resolve(SegmentAnalysesRepository);
            const spy = jest.spyOn(segmentAnalysesRepository, 'upsertManySegmentAnalyses');

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await analyzeReviewSemanticallyUseCase.execute({
                restaurantId,
                reviewId,
                isPrivateReview: false,
            });

            expect(spy).toHaveBeenCalledOnce();

            const segmentAnalyses = await segmentAnalysesRepository.find({ filter: { reviewSocialId, platformKey: PlatformKey.GMB } });

            expect(segmentAnalyses).toHaveLength(semanticAnalysis.length);
        });

        it('should update review status with failed if null result from lambda', async () => {
            const reviewsRepository = container.resolve(ReviewsRepository);
            generateReviewSemanticAnalysisService.execute = jest.fn().mockResolvedValue(null);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await analyzeReviewSemanticallyUseCase.execute({
                restaurantId,
                reviewId,
                isPrivateReview: false,
            });

            const updatedReview = await reviewsRepository.findOne({ filter: { _id: toDbId(reviewId) }, options: { lean: true } });
            expect(updatedReview?.semanticAnalysisFetchStatus).toBe(SemanticAnalysisFetchStatus.FAILED);
        });

        it('should update review status with no results if received empty array from lambda', async () => {
            const reviewsRepository = container.resolve(ReviewsRepository);
            generateReviewSemanticAnalysisService.execute = jest.fn().mockResolvedValue([]);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await analyzeReviewSemanticallyUseCase.execute({
                restaurantId,
                reviewId,
                isPrivateReview: false,
            });

            const updatedReview = await reviewsRepository.findOne({
                filter: { _id: toDbId(reviewId) },
                options: { lean: true },
            });
            expect(updatedReview?.semanticAnalysisFetchStatus).toBe(SemanticAnalysisFetchStatus.NO_RESULTS);
        });

        it('should update review status with done if received array from lambda', async () => {
            const reviewsRepository = container.resolve(ReviewsRepository);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(toDbId(restaurantId)).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(reviewSocialId)
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await analyzeReviewSemanticallyUseCase.execute({
                restaurantId,
                reviewId,
                isPrivateReview: false,
            });

            const updatedReview = await reviewsRepository.findOne({ filter: { _id: toDbId(reviewId) }, options: { lean: true } });
            expect(updatedReview?.semanticAnalysisFetchStatus).toBe(SemanticAnalysisFetchStatus.DONE);
        });
    });
});
