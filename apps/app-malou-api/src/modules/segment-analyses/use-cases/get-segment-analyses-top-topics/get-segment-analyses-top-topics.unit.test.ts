import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import {
    ApplicationLanguage,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisSubCategory,
    ReviewAnalysisTag,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultTranslations } from ':modules/keywords/tests/translations.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { GetSegmentAnalysesTopTopicsUseCase } from ':modules/segment-analyses/use-cases/get-segment-analyses-top-topics/get-segment-analyses-top-topics.use-case';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';

let getSegmentAnalysesTopTopicsUseCase: GetSegmentAnalysesTopTopicsUseCase;

const defaultTranslations = {
    fr: null,
    en: null,
    es: null,
    it: null,
};

describe('GetSegmentAnalysesTopTopicUseCase', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories([
            'ReviewsRepository',
            'SegmentAnalysesRepository',
            'RestaurantsRepository',
            'SegmentAnalysisParentTopicsRepository',
            'TranslationsRepository',
        ]);

        getSegmentAnalysesTopTopicsUseCase = container.resolve(GetSegmentAnalysesTopTopicsUseCase);
    });

    describe('execute', () => {
        it('should return 0 for counts if no parent topics', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey = PlatformKey.GMB;

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data() {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEUTRAL)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: () => {
                    return {
                        positiveTopics: [],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId: restaurantIds[0], // TODO: temp
                startDate,
                endDate,
                keys: [platformKey],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should correctly group topic by sentiment, order by most important and filter topic with no review', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const socialId3 = 'socialId3';
            const socialId4 = 'socialId4';
            const platformKey1 = PlatformKey.GMB;
            const platformKey2 = PlatformKey.TRIPADVISOR;
            const platformKey3 = PlatformKey.FACEBOOK;
            const platformKey4 = PlatformKey.YELP;
            const topic1 = 'topic1';
            const topic2 = 'topic2';
            const topic3 = 'topic3';
            const topic4 = 'topic4';
            const topic5 = 'topic5';
            const topic6 = 'topic6';
            const topic7 = 'topic7';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .key(platformKey2)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId3)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .key(platformKey3)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId4)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .key(platformKey4)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic3)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic4)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic5)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic6)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic7)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .platformKey(platformKey2)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .platformKey(platformKey2)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .platformKey(platformKey2)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.EXPEDITIOUSNESS)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .platformKey(platformKey2)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId3)
                                    .platformKey(platformKey3)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[3]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId3)
                                    .platformKey(platformKey3)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[3]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[5]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[5]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[5]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[2]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[2]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([
                                        dependencies.segmentAnalysisParentTopics()[4]._id,
                                        dependencies.segmentAnalysisParentTopics()[5]._id,
                                    ])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[4]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId4)
                                    .platformKey(platformKey4)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[4]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 4,
                                negativeCount: null,
                            },
                            {
                                name: dependencies.segmentAnalysisParentTopics[3].name,
                                translations: defaultTranslations,
                                positiveCount: 2,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[4].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: 3,
                            },
                            {
                                name: dependencies.segmentAnalysisParentTopics[2].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: 2,
                            },
                            {
                                name: dependencies.segmentAnalysisParentTopics[5].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: 1,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1, platformKey2, platformKey3, platformKey4],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should count reviews and categories, and not segments', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const topic2 = 'topic2';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment1')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment2')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment3')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment4')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[1].name,
                                translations: defaultTranslations,
                                positiveCount: 2,
                                negativeCount: null,
                            },
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should only take reviews within time range', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const topic2 = 'topic2';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const dateIn = now.toJSDate();
            const dateOut = now.plus({ day: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(dateIn)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment1')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(dateOut)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .segment('segment2')
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(dateOut)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment3')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(dateOut)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment4')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should only take reviews with selected keys', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey1 = PlatformKey.GMB;
            const platformKey2 = PlatformKey.TRIPADVISOR;
            const topic1 = 'topic1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey2)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .platformKey(platformKey2)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should remove overall experience category from counts', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.OVERALL_EXPERIENCE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should not count neutral sentiment', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEUTRAL)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should return translations of the topic', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'translations' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },

                    translations: {
                        data() {
                            return [
                                getDefaultTranslations()
                                    .fr('Mon topic')
                                    .en('My topic')
                                    .es('Mi tema')
                                    .it('Il mio argomento')
                                    .language(ApplicationLanguage.EN)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .translationsId(dependencies.translations()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .subcategory(ReviewAnalysisSubCategory.MENU_ITEMS)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: {
                                    fr: 'Mon topic',
                                    en: 'My topic',
                                    es: 'Mi tema',
                                    it: 'Il mio argomento',
                                },
                                positiveCount: 1,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should not display positive top topic if already in negative topics', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [],
                        negativeTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: 2,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should display positive topic that is also in negative if count is above threshold count', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const platformKey2 = PlatformKey.TRIPADVISOR;
            const platformKey3 = PlatformKey.FACEBOOK;
            const platformKey4 = PlatformKey.YELP;
            const socialId2 = 'socialId2';
            const socialId3 = 'socialId3';
            const socialId4 = 'socialId4';
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();
            const categories = Object.values(ReviewAnalysisTag).filter((category) => category !== ReviewAnalysisTag.OVERALL_EXPERIENCE);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey2)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId3)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey3)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId4)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey4)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            const positiveSegments = dependencies
                                .reviews()
                                .map((review) =>
                                    categories.map((category) =>
                                        getDefaultSegmentAnalysis()
                                            .reviewSocialId(review.socialId)
                                            .platformKey(platformKey1)
                                            .reviewSocialCreatedAt(date)
                                            .category(category)
                                            .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                            .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                            .build()
                                    )
                                )
                                .flat();

                            const negativeSegments = dependencies
                                .reviews()
                                .map((review) =>
                                    categories.map((category) =>
                                        getDefaultSegmentAnalysis()
                                            .reviewSocialId(review.socialId)
                                            .platformKey(platformKey1)
                                            .reviewSocialCreatedAt(date)
                                            .category(category)
                                            .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                            .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                            .build()
                                    )
                                )
                                .flat();

                            return [...positiveSegments, ...negativeSegments];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    const count = dependencies.reviews.length * categories.length;
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: count,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: count,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should display positive topic that is also in negative if x times higher than negative count for same topic', async () => {
            const socialId1 = 'socialId1';
            const platformKey1 = PlatformKey.GMB;
            const topic1 = 'topic1';
            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },

                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    .name(topic1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                // Create 4 positive segments for the topic (4x more than negative, above the 3x threshold)
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.PRICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.HYGIENE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                // Create 1 negative segment for the same topic
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .platformKey(platformKey1)
                                    .reviewSocialCreatedAt(date)
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        positiveTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: 4,
                                negativeCount: null,
                            },
                        ],
                        negativeTopics: [
                            {
                                name: dependencies.segmentAnalysisParentTopics[0].name,
                                translations: defaultTranslations,
                                positiveCount: null,
                                negativeCount: 1,
                            },
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = seededObjects.restaurants[0]._id.toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesTopTopicsUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
