import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { PlatformKey, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { DEFAULT_REVIEW_ANALYSES_CHART_DATA } from ':modules/review-analyses/mappers/review-analyses.mapper.constant';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { GetSegmentAnalysesChartDataUseCase } from ':modules/segment-analyses/use-cases/get-segment-analyses-chart-data/get-segment-analyses-chart-data.use-case';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';

let getSegmentAnalysesChartDataUseCase: GetSegmentAnalysesChartDataUseCase;

describe('GetSegmentAnalysesChartDataUseCase', () => {
    beforeEach(() => {
        container.clearInstances();

        registerRepositories([
            'ReviewsRepository',
            'SegmentAnalysesRepository',
            'RestaurantsRepository',
            'PlatformsRepository',
            'SegmentAnalysisParentTopicsRepository',
        ]);

        getSegmentAnalysesChartDataUseCase = container.resolve(GetSegmentAnalysesChartDataUseCase);
    });

    describe('execute', () => {
        it('should group by restaurant, category and sentiment', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const socialId3 = 'socialId3';
            const socialId4 = 'socialId4';
            const platformSocialId1 = 'platformSocialId1';
            const platformSocialId2 = 'platformSocialId2';
            const platformSocialId3 = 'platformSocialId3';
            const platformKey1 = PlatformKey.GMB;
            const platformKey2 = PlatformKey.TRIPADVISOR;
            const platformKey3 = PlatformKey.ZENCHEF;
            const platformKey4 = PlatformKey.RESY;

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build(), getDefaultRestaurant().build(), getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .socialId(platformSocialId1)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .key(platformKey1)
                                    .socialId(platformSocialId2)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .key(platformKey1)
                                    .socialId(platformSocialId3)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey2)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId3)
                                    .key(platformKey3)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId4)
                                    .key(platformKey4)
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[1]._id).build(),
                                getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[2]._id).build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .platformSocialId(platformSocialId1)
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey1)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .platformSocialId(platformSocialId1)
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey1)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .platformSocialId(platformSocialId2)
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey2)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .platformSocialId(platformSocialId3)
                                    .reviewSocialId(socialId3)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey3)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .platformSocialId(platformSocialId3)
                                    .reviewSocialId(socialId4)
                                    .reviewSocialCreatedAt(date)
                                    .platformKey(platformKey4)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[2]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 1,
                                total: 2,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 1,
                                total: 2,
                            },
                        },
                        [dependencies.restaurants[1]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.ATMOSPHERE]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                        },
                        [dependencies.restaurants[2]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.ATMOSPHERE]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 0,
                                [ReviewAnalysisSentiment.NEGATIVE]: 2,
                                total: 2,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 0,
                                [ReviewAnalysisSentiment.NEGATIVE]: 2,
                                total: 2,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey1, platformKey2, platformKey3, platformKey4],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should count reviews and not segments', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey = PlatformKey.GMB;
            const platformSocialId1 = 'platformSocialId1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey)
                                    .socialId(platformSocialId1)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment1')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segment('segment2')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('segment3')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('segment4')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segment('segment5')
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            [ReviewAnalysisTag.ATMOSPHERE]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 0,
                                [ReviewAnalysisSentiment.NEGATIVE]: 1,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 1,
                                total: 2,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should only take reviews within time range', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey = PlatformKey.GMB;
            const platformSocialId1 = 'platformSocialId1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const dateIn = now.toJSDate();
            const dateOut = now.plus({ day: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey)
                                    .socialId(platformSocialId1)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(dateIn)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(dateOut)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should only take reviews with selected keys', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey1 = PlatformKey.GMB;
            const platformKey2 = PlatformKey.TRIPADVISOR;
            const platformSocialId1 = 'platformSocialId1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey1)
                                    .socialId(platformSocialId1)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey1)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey2)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey1)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey2)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey1],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should remove overall experience category from count', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey = PlatformKey.GMB;
            const platformSocialId1 = 'platformSocialId1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey)
                                    .socialId(platformSocialId1)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.OVERALL_EXPERIENCE)
                                    .sentiment(ReviewAnalysisSentiment.NEGATIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey],
            });

            expect(result).toEqual(expectedResult);
        });

        it('should remove neutral sentiment from count', async () => {
            const socialId1 = 'socialId1';
            const socialId2 = 'socialId2';
            const platformKey = PlatformKey.GMB;
            const platformSocialId1 = 'platformSocialId1';

            const now = DateTime.now();
            const startDate = now.minus({ day: 1 }).toJSDate();
            const endDate = now.plus({ day: 1 }).toJSDate();
            const date = now.toJSDate();

            const testCase = new TestCaseBuilderV2<
                'restaurants' | 'platforms' | 'reviews' | 'segmentAnalyses' | 'segmentAnalysisParentTopics'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .key(platformKey)
                                    .socialId(platformSocialId1)
                                    .build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .socialId(socialId1)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultReview()
                                    .socialId(socialId2)
                                    .key(platformKey)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data(dependencies) {
                            return [getDefaultSegmentAnalysisParentTopics().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId1)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.FOOD)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .reviewSocialId(socialId2)
                                    .reviewSocialCreatedAt(date)
                                    .platformSocialId(platformSocialId1)
                                    .platformKey(platformKey)
                                    .category(ReviewAnalysisTag.ATMOSPHERE)
                                    .sentiment(ReviewAnalysisSentiment.NEUTRAL)
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: (dependencies) => {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            ...DEFAULT_REVIEW_ANALYSES_CHART_DATA,
                            [ReviewAnalysisTag.FOOD]: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                            total: {
                                [ReviewAnalysisSentiment.POSITIVE]: 1,
                                [ReviewAnalysisSentiment.NEGATIVE]: 0,
                                total: 1,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((r) => r._id.toString());
            const expectedResult = testCase.getExpectedResult();

            const result = await getSegmentAnalysesChartDataUseCase.execute({
                restaurantIds,
                startDate,
                endDate,
                keys: [platformKey],
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
