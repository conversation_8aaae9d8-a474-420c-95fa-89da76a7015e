import { NextFunction, Request, Response, Router } from 'express';
import { singleton } from 'tsyringe';

import { Role } from '@malou-io/package-utils';

import AbstractRouter from ':helpers/abstracts/abstract-router';
import { authorize } from ':plugins/passport';

import { OrganizationsController } from './organizations.controller';

@singleton()
export default class OrganizationsRouter extends AbstractRouter {
    constructor(private _organizationsController: OrganizationsController) {
        super();
    }

    init(): Router {
        this.router.get('/', authorize(), (req, res, next) => this._organizationsController.handleGetOrganizations(req, res, next));

        this.router.get('/admin/search', authorize([Role.ADMIN]), (req: Request, res: Response, next: NextFunction) =>
            this._organizationsController.adminSearchOrganizations(req, res, next)
        );

        this.router.get('/ping/:organization_id', authorize(), (req, res, next) =>
            this._organizationsController.handleGetOrganizationUsersAndRestaurants(req, res, next)
        );

        this.router.post('/create-organization', authorize(), (req, res, next) =>
            this._organizationsController.handleCreateOrganization(req, res, next)
        );

        this.router.put('/:organization_id', authorize(), (req, res, next) =>
            this._organizationsController.handleUpdateOrganization(req, res, next)
        );

        this.router.get('/:organization_id', authorize(), (req, res, next) =>
            this._organizationsController.handleGetOrganization(req, res, next)
        );

        this.router.post('/delete-organization', authorize(), (req, res, next) =>
            this._organizationsController.handleDeleteOrganization(req, res, next)
        );

        this.router.get('/restaurants/count', authorize(), (req, res, next) =>
            this._organizationsController.handleGetRestaurantsInOrganizationsCountByUser(req, res, next)
        );

        this.router.get('/users/count', authorize(), (req, res, next) =>
            this._organizationsController.handleGetUsersInOrganizationsCountByUser(req, res, next)
        );

        this.router.get('/:organization_id/restaurants/count', authorize(), (req, res, next) =>
            this._organizationsController.handleGetRestaurantsCountByOrganization(req, res, next)
        );

        this.router.post('/link-verified-email/:organizationId', authorize(), (req, res, next) =>
            this._organizationsController.handleLinkVerifiedEmailToOrganization(req, res, next)
        );

        return this.router;
    }
}
