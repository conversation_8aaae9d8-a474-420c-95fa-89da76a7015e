import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';

import {
    AdminSearchOrganizationsQueryDto,
    adminSearchOrganizationsQueryValidator,
    AdminSearchOrganizationsResponseDto,
    CountOrganizationsDto,
    CreateOrganizationRequestBodyDto,
    createOrganizationRequestBodyValidator,
    CreateOrganizationResponseBodyDto,
    DeleteOrganizationRequestBodyDto,
    deleteOrganizationRequestBodyValidator,
    DeleteOrganizationResponseBodyDto,
    GetOrganizationParamsDto,
    getOrganizationParamsValidator,
    LinkVerifiedEmailToOrganizationBodyDto,
    linkVerifiedEmailToOrganizationBodyValidator,
    LinkVerifiedEmailToOrganizationParamsDto,
    linkVerifiedEmailToOrganizationParamsValidator,
    OrganizationDto,
} from '@malou-io/package-dto';
import { IOrganization, toDbId } from '@malou-io/package-models';
import { Api<PERSON><PERSON>ult, ApiResultV2 } from '@malou-io/package-utils';

import { Body, Params, Query } from ':helpers/decorators/validators';
import { AdminSearchOrganizationsUseCase } from ':modules/organizations/use-cases/admin-search-organizations/admin-search-organizations.use-case';
import { CreateOrganizationUseCase } from ':modules/organizations/use-cases/create-organization/create-organization.use-case';
import { DeleteOrganizationUseCase } from ':modules/organizations/use-cases/delete-organization/delete-organization.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';

import { OrganizationsDtoMapper } from './organizations.dto-mapper';
import OrganizationsRepository from './organizations.repository';
import OrganizationsUseCases from './organizations.use-cases';

@singleton()
export class OrganizationsController {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _organizationsUseCases: OrganizationsUseCases,
        private readonly _organizationsDtoMapper: OrganizationsDtoMapper,
        private readonly _createOrganizationUseCase: CreateOrganizationUseCase,
        private readonly _deleteOrganizationUseCase: DeleteOrganizationUseCase,
        private readonly _adminSearchOrganizationsUseCase: AdminSearchOrganizationsUseCase
    ) {}

    async handleGetOrganizations(req: Request, res: Response, next: NextFunction) {
        try {
            const organizations = await this._organizationsRepository.find({ filter: {} });
            return res.json({ msg: 'Success', data: organizations });
        } catch (err) {
            return next(err);
        }
    }

    async handleGetOrganizationUsersAndRestaurants(req: Request, res: Response, next: NextFunction) {
        try {
            const { organization_id: organizationInputId } = req.params;
            const organizationId = toDbId(organizationInputId);

            const [organization, users, restaurants] = await Promise.all([
                this._organizationsRepository.findOne({
                    filter: { _id: organizationId },
                    options: { lean: true },
                }),
                this._usersRepository.find({
                    filter: { organizationIds: organizationId },
                    projection: { _id: 1, email: 1, name: 1, lastname: 1 },
                }),
                this._restaurantsRepository.find({
                    filter: { organizationId },
                    projection: { _id: 1, name: 1 },
                }),
            ]);

            return res.json({ msg: 'Success', data: { organization, users, restaurants } });
        } catch (err) {
            return next(err);
        }
    }

    @Body(createOrganizationRequestBodyValidator)
    async handleCreateOrganization(
        req: Request<any, any, CreateOrganizationRequestBodyDto>,
        res: Response<ApiResultV2<CreateOrganizationResponseBodyDto>>,
        next: NextFunction
    ): Promise<void> {
        try {
            const organization = await this._createOrganizationUseCase.execute(req.body);
            const dto = this._organizationsDtoMapper.toOrganizationDto(organization);
            res.json({ data: dto });
        } catch (err) {
            next(err);
        }
    }

    async handleUpdateOrganization(req: Request, res: Response, next: NextFunction) {
        try {
            const { organization_id: organizationId } = req.params;
            const data = req.body;

            const updated = await this._organizationsRepository.findOneAndUpdate({
                filter: { _id: toDbId(organizationId) },
                update: data,
            });

            return res.json({ msg: 'Success', data: updated });
        } catch (err) {
            return next(err);
        }
    }

    @Body(deleteOrganizationRequestBodyValidator)
    async handleDeleteOrganization(
        req: Request<any, any, DeleteOrganizationRequestBodyDto>,
        res: Response<ApiResultV2<DeleteOrganizationResponseBodyDto>>,
        next: NextFunction
    ): Promise<void> {
        try {
            await this._deleteOrganizationUseCase.execute(req.body);
            res.json({ data: undefined });
        } catch (err) {
            next(err);
        }
    }

    async handleGetRestaurantsInOrganizationsCountByUser(req: Request, res: Response<ApiResult<number>>, next: NextFunction) {
        try {
            const userId = req.user._id;

            const organizationsIds = await this._organizationsUseCases.getUserOrganizationIds(toDbId(userId));

            const usersOrganizationsRestaurantsCount = await this._organizationsUseCases.countRestaurantsInOrganizations(organizationsIds);

            res.json({ data: usersOrganizationsRestaurantsCount });
        } catch (err) {
            next(err);
        }
    }

    async handleGetUsersInOrganizationsCountByUser(req: Request, res: Response<ApiResult<number>>, next: NextFunction) {
        try {
            const userId = req.user._id;

            const organizationsIds = await this._organizationsUseCases.getUserOrganizationIds(toDbId(userId));

            const usersOrganisationsCount = await this._organizationsUseCases.countUsersInOrganizations(organizationsIds);

            res.json({ data: usersOrganisationsCount });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationParamsValidator)
    async handleGetOrganization(req: Request<GetOrganizationParamsDto>, res: Response<ApiResult<IOrganization>>, next: NextFunction) {
        try {
            const { organizationId } = req.params;
            const organization = await this._organizationsRepository.findOneOrFail({
                filter: { _id: toDbId(organizationId) },
                options: { lean: true },
            });
            res.json({ msg: 'Success', data: organization });
        } catch (err) {
            next(err);
        }
    }

    @Params(getOrganizationParamsValidator)
    async handleGetRestaurantsCountByOrganization(
        req: Request<GetOrganizationParamsDto>,
        res: Response<ApiResultV2<CountOrganizationsDto>>,
        next: NextFunction
    ) {
        try {
            const { organizationId } = req.params;
            const count = await this._organizationsUseCases.countRestaurantsInOrganizations([toDbId(organizationId)]);
            res.json({ data: count });
        } catch (err) {
            next(err);
        }
    }

    @Params(linkVerifiedEmailToOrganizationParamsValidator)
    @Body(linkVerifiedEmailToOrganizationBodyValidator)
    async handleLinkVerifiedEmailToOrganization(
        req: Request<LinkVerifiedEmailToOrganizationParamsDto, any, LinkVerifiedEmailToOrganizationBodyDto>,
        res: Response<ApiResultV2<OrganizationDto>>,
        next: NextFunction
    ) {
        try {
            const updated = await this._organizationsUseCases.linkVerifiedEmailToOrganization(
                toDbId(req.params.organizationId),
                req.body.verifiedEmail
            );
            const dto = this._organizationsDtoMapper.toOrganizationDto(updated);
            return res.json({ data: dto });
        } catch (err) {
            return next(err);
        }
    }

    @Query(adminSearchOrganizationsQueryValidator)
    async adminSearchOrganizations(
        req: Request<any, any, any, AdminSearchOrganizationsQueryDto>,
        res: Response<ApiResultV2<AdminSearchOrganizationsResponseDto['data'], { pagination: { total: number } }>>,
        next: NextFunction
    ) {
        try {
            const { text, limit, offset } = req.query;

            const result = await this._adminSearchOrganizationsUseCase.execute({
                text,
                limit,
                offset,
            });

            return res.json({
                data: result.data,
                metadata: { pagination: { total: result.total } },
            });
        } catch (err) {
            next(err);
        }
    }
}
