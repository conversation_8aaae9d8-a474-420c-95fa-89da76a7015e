import { ReadPreference } from 'mongodb';
import { singleton } from 'tsyringe';

import {
    AdminSearchOrganizationsOrganizationDto,
    AdminSearchOrganizationsQueryDto,
    AdminSearchOrganizationsResponseDto,
} from '@malou-io/package-dto';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';
import OrganizationsRepository from ':modules/organizations/organizations.repository';

@singleton()
export class AdminSearchOrganizationsUseCase {
    constructor(private readonly _organizationsRepository: OrganizationsRepository) {}

    async execute(params: AdminSearchOrganizationsQueryDto): Promise<AdminSearchOrganizationsResponseDto> {
        const { text, limit = 20, offset = 0 } = params;

        const pipeline: any[] = [];

        // Lookup users
        pipeline.push({
            $lookup: {
                from: 'users',
                localField: '_id',
                foreignField: 'organizationIds',
                as: 'users',
                pipeline: [
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            lastname: 1,
                            email: 1,
                        },
                    },
                ],
            },
        });

        // Lookup restaurants
        pipeline.push({
            $lookup: {
                from: 'restaurants',
                localField: '_id',
                foreignField: 'organizationId',
                as: 'restaurants',
                pipeline: [
                    {
                        $match: { active: true },
                    },
                    {
                        $project: {
                            _id: 1,
                            name: 1,
                            active: 1,
                        },
                    },
                ],
            },
        });

        // Apply text search filter if provided
        if (text?.trim()) {
            const searchRegex = toDiacriticInsensitiveRegexString(text.trim());

            pipeline.push({
                $match: {
                    name: { $regex: searchRegex, $options: 'i' },
                },
            });
        }

        // Sort by creation date (newest first)
        pipeline.push({
            $sort: {
                createdAt: -1,
                name: 1,
            },
        });

        // Create count pipeline
        const countPipeline = [...pipeline];
        countPipeline.push({ $count: 'total' });

        // Add pagination
        pipeline.push({ $skip: offset });
        pipeline.push({ $limit: limit });

        const [organizations, countResult] = await Promise.all([
            this._organizationsRepository.aggregate(pipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizations',
            }),
            this._organizationsRepository.aggregate(countPipeline, {
                readPreference: ReadPreference.SECONDARY_PREFERRED,
                comment: 'adminSearchOrganizationsCount',
            }),
        ]);

        const total = countResult.length > 0 ? countResult[0].total : 0;

        return {
            data: organizations.map((organization) => this._toDto(organization)),
            total,
        };
    }

    private _toDto(organization: any): AdminSearchOrganizationsOrganizationDto {
        return {
            _id: organization._id.toString(),
            name: organization.name,
            limit: organization.limit,
            verifiedEmailsForCampaigns: organization.verifiedEmailsForCampaigns || [],
            createdAt: organization.createdAt,
            updatedAt: organization.updatedAt,
            users:
                organization.users?.map((user: any) => ({
                    _id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                    email: user.email,
                })) || [],
            restaurants:
                organization.restaurants?.map((restaurant: any) => ({
                    _id: restaurant._id.toString(),
                    name: restaurant.name,
                    active: restaurant.active,
                })) || [],
        };
    }
}
