import { chunk, groupBy } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { MalouErrorCode, MonthAndYear, TimeInMilliseconds, TimeInSeconds, waitFor } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { isFulfilled, isRejected } from ':helpers/utils';
import {
    FailedSearchKeywordsImpressionsResponse,
    SearchKeywordsImpressionsInvocationType,
    SearchKeywordsImpressionsParams,
    SearchKeywordsImpressionsResponse,
    SearchKeywordsImpressionsService,
} from ':microservices/search-keywords-impressions.service';
import { BodyMessageMonthlySaveKeywordSearchImpressions } from ':modules/keyword-search-impressions/queues/types';
import KeywordSearchImpressionsRepository from ':modules/keyword-search-impressions/repositories/keyword-search-impressions.repository';
import { BrandKeywordsIdentificationService } from ':modules/keyword-search-impressions/services/brand-keywords-identification/brand-keywords-identification.service';
import { MonthlyKeywordSearchTimeRangeService } from ':modules/keyword-search-impressions/use-cases/fetch-and-save-keyword-search-impressions/services/monthly-keyword-search-time-range.service';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { GmbSearchKeywordsImpressions } from ':modules/platforms/platforms/gmb/gmb.types';
import { RestaurantKeywordsRepository } from ':modules/restaurant-keywords/restaurant-keywords.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GmbBusinessProfilePerformanceProvider } from ':providers/google/gmb.business-profile-performance.provider';
import { GmbRefreshTokenService } from ':services/credentials/gmb/gmb-refresh-token.service';

// we take only 60% of the max QPM to avoid hitting the limit from other parts of the application
const ARBITRARY_VALUE_OF_MAX_QPM = 0.6;
@singleton()
export class FetchAndSaveKeywordsSearchImpressionsUseCase {
    constructor(
        private readonly _keywordSearchImpressionsRepository: KeywordSearchImpressionsRepository,
        private readonly _gmbBusinessProfilePerformanceProvider: GmbBusinessProfilePerformanceProvider,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _monthlyKeywordSearchTimeRangeService: MonthlyKeywordSearchTimeRangeService,
        private readonly _gmbRefreshTokenService: GmbRefreshTokenService,
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _organizationRepository: OrganizationsRepository,
        private readonly _restaurantKeywordsRepository: RestaurantKeywordsRepository,
        private readonly _searchKeywordsImpressionsService: SearchKeywordsImpressionsService,
        private readonly _brandKeywordsIdentificationService: BrandKeywordsIdentificationService
    ) {}

    async execute({ platformId }: BodyMessageMonthlySaveKeywordSearchImpressions): Promise<void> {
        logger.info('[FetchAndSaveKeywordsSearchImpressionsUseCase] - fetch and save keyword search impressions', { platformId });

        // wait for the time needed to avoid hitting the QPM limit
        await waitFor(
            (TimeInSeconds.MINUTE / (GmbBusinessProfilePerformanceProvider.MAX_QPM * ARBITRARY_VALUE_OF_MAX_QPM)) *
                this._getNbOfWorkers() *
                TimeInMilliseconds.SECOND
        );

        const platform = await this._platformsRepository.findOneOrFail({
            filter: { _id: toDbId(platformId) },
            options: { lean: true },
        });

        const restaurantId = platform.restaurantId.toString();
        const timeRanges: MonthAndYear[] = await this._monthlyKeywordSearchTimeRangeService.getTimeRangeForMonthlySaveData({
            restaurantId,
        });

        const credentialId = platform.credentials?.[0];
        const location = platform.apiEndpoint?.split('/locations/')[1];

        if (!credentialId || !location) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND, {
                message: '[FetchAndSaveKeywordsSearchImpressionsUseCase] - credentialId or location not found',
                metadata: { restaurantId, platformId, location, credentialId },
            });
        }

        const restaurantData = await this._getRestaurantData(restaurantId);

        for (const timeRange of timeRanges) {
            try {
                const searchKeywordsImpressions = await this._fetchKeywordsSearchImpressions({
                    month: timeRange.month,
                    year: timeRange.year,
                    credentialId: credentialId.toString(),
                    location,
                });

                const processedKeywordSearchImpressions = await this._processKeywordSearchImpressions({
                    month: timeRange.month,
                    year: timeRange.year,
                    monthImpressionsData: searchKeywordsImpressions,
                    restaurantData,
                    type: SearchKeywordsImpressionsInvocationType.KEYWORD_SEARCH_PROCESSING,
                });

                await this._saveKeywordSearchImpressions({
                    restaurantId,
                    month: timeRange.month,
                    year: timeRange.year,
                    monthImpressionsData: processedKeywordSearchImpressions.monthImpressionsData,
                });

                await this._saveSelectedKeywordsImpressions({
                    restaurantId,
                    month: timeRange.month,
                    year: timeRange.year,
                    selectedKeywordsImpressions: processedKeywordSearchImpressions.selectedKeywordsImpressions,
                });
            } catch (error) {
                logger.error('[FetchAndSaveKeywordsSearchImpressionsUseCase] - error ', {
                    restaurantId,
                    platformId,
                    timeRange,
                    error: JSON.stringify(error),
                });
            }
        }
    }

    private async _processKeywordSearchImpressions(params: SearchKeywordsImpressionsParams): Promise<SearchKeywordsImpressionsResponse> {
        const CHUNK_SIZE = 1000;
        const monthImpressionsDataChunks = chunk(params.monthImpressionsData, CHUNK_SIZE);

        const promises = monthImpressionsDataChunks.map((monthImpressionsDataChunk) =>
            this._searchKeywordsImpressionsService.processKeywordSearchImpressions({
                month: params.month,
                year: params.year,
                monthImpressionsData: monthImpressionsDataChunk,
                restaurantData: params.restaurantData,
                type: SearchKeywordsImpressionsInvocationType.KEYWORD_SEARCH_PROCESSING,
            })
        );

        const responses = await Promise.allSettled(promises);

        const successfulResponses: SearchKeywordsImpressionsResponse[] = responses
            .filter(isFulfilled)
            .filter((response) => !this._isLambdaResponseError(response.value))
            .map((response) => response.value.data);

        // if not all promises were successful, return false because we need all data to be saved
        if (successfulResponses.length < promises.length) {
            const failedResponses = responses.filter((response) => isRejected(response) || this._isLambdaResponseError(response.value));
            throw new MalouError(MalouErrorCode.FAILED_TO_PROCESS_KEYWORD_SEARCH_IMPRESSIONS, {
                metadata: { failedResponses },
            });
        }

        const aggregatedResponses = successfulResponses.reduce(
            (acc, response) => {
                return {
                    month: response.month,
                    year: response.year,
                    monthImpressionsData: acc.monthImpressionsData.concat(response.monthImpressionsData),
                    selectedKeywordsImpressions: acc.selectedKeywordsImpressions.concat(response.selectedKeywordsImpressions),
                };
            },
            { month: params.month, year: params.year, monthImpressionsData: [], selectedKeywordsImpressions: [] }
        );

        aggregatedResponses.selectedKeywordsImpressions = Object.values(groupBy(aggregatedResponses.selectedKeywordsImpressions, 'id')).map(
            (keywordImpressions) =>
                keywordImpressions.reduce(
                    (acc, next) => {
                        acc.value += next.value;
                        return acc;
                    },
                    { ...keywordImpressions[0], value: 0 }
                )
        );

        return aggregatedResponses;
    }

    private _isLambdaResponseError(
        response: { data: SearchKeywordsImpressionsResponse } | { error: FailedSearchKeywordsImpressionsResponse }
    ): response is { error: FailedSearchKeywordsImpressionsResponse } {
        return 'error' in response;
    }

    private async _fetchKeywordsSearchImpressions({
        month,
        year,
        credentialId,
        location,
    }: {
        month: number;
        year: number;
        credentialId: string;
        location: string;
    }): Promise<SearchKeywordsImpressionsParams['monthImpressionsData']> {
        const credential = await this._gmbRefreshTokenService.getFreshTokenIfNecessary(credentialId.toString());

        const keywordSearchImpressions: GmbSearchKeywordsImpressions['searchKeywordsCounts'] =
            await this._gmbBusinessProfilePerformanceProvider.fetchSearchKeywordsImpressions({
                accessToken: credential.accessToken,
                tokenType: credential.tokenType,
                locationId: location,
                queryParams: { startYear: year, startMonth: month, endYear: year, endMonth: month },
            });

        return keywordSearchImpressions.map((keywordSearchImpression) => {
            const value = keywordSearchImpression.insightsValue.value ?? keywordSearchImpression.insightsValue.threshold ?? '0';
            return {
                keywordSearch: keywordSearchImpression.searchKeyword,
                value: parseInt(value, 10),
            };
        });
    }

    private async _getRestaurantData(restaurantId: string): Promise<SearchKeywordsImpressionsParams['restaurantData']> {
        const restaurant = await this._restaurantRepository.findOneOrFail({
            filter: { _id: toDbId(restaurantId) },
            options: { lean: true },
            projection: { name: 1, organizationId: 1, address: 1, brandKeywords: 1 },
        });
        const organization = await this._organizationRepository.findOneOrFail({
            filter: { _id: restaurant.organizationId },
            options: { lean: true },
            projection: { name: 1 },
        });

        let brandKeywords = restaurant.brandKeywords;
        if (!brandKeywords?.brandGroupKeywords?.length || !brandKeywords?.brandNameKeywords?.length) {
            brandKeywords = await this._brandKeywordsIdentificationService.identifyBrandKeywordsForRestaurant(restaurantId);
        }

        const selectedRestaurantKeywords = await this._restaurantKeywordsRepository.findSelectedRestaurantKeywordsByRestaurantIds([
            restaurantId,
        ]);
        const selectedKeywords = selectedRestaurantKeywords.map((restaurantKeyword) => ({
            id: restaurantKeyword.keywordId,
            text: restaurantKeyword.keyword.text,
        }));
        return {
            formattedAddress: restaurant.address?.formattedAddress ?? '',
            restaurantName: restaurant.name,
            organizationName: organization.name,
            selectedKeywords,
            brandNameKeywords: brandKeywords.brandNameKeywords,
            brandGroupKeywords: brandKeywords.brandGroupKeywords,
        };
    }

    private async _saveKeywordSearchImpressions({
        restaurantId,
        month,
        year,
        monthImpressionsData,
    }: {
        restaurantId: string;
        month: number;
        year: number;
        monthImpressionsData: SearchKeywordsImpressionsResponse['monthImpressionsData'];
    }): Promise<void> {
        await this._keywordSearchImpressionsRepository.upsertManyKeywordSearchImpressions(
            monthImpressionsData.map((keywordImpression) => ({
                restaurantId,
                month,
                year,
                keywordSearch: keywordImpression.keywordSearch,
                type: keywordImpression.type,
                value: keywordImpression.value,
                relatedKeywordIds: keywordImpression.relatedSelectedKeywords,
            }))
        );
    }

    private async _saveSelectedKeywordsImpressions({
        restaurantId,
        month,
        year,
        selectedKeywordsImpressions,
    }: {
        restaurantId: string;
        month: number;
        year: number;
        selectedKeywordsImpressions: SearchKeywordsImpressionsResponse['selectedKeywordsImpressions'];
    }): Promise<void> {
        const date = DateTime.fromObject({ year, month, zone: 'utc' }).toJSDate();
        const updateData = selectedKeywordsImpressions.map((selectedKeywordImpression) => ({
            restaurantId,
            keywordId: selectedKeywordImpression.id,
            value: selectedKeywordImpression.value,
            date,
        }));

        await this._restaurantKeywordsRepository.updateImpressionsHistory(updateData);
    }

    private _getNbOfWorkers() {
        if (process.env.NODE_ENV === 'production') {
            return 3;
        }
        if (['staging', 'development'].includes(process.env.NODE_ENV)) {
            return 2;
        }
        return 1;
    }
}
