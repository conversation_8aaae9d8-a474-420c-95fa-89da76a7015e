import { container } from 'tsyringe';

import { TopKeywordSearchImpressionsDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import { KeywordSearchImpressionsType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultKeywordSearchImpressions } from ':modules/keyword-search-impressions/tests/keyword-search-impressions.builder';
import { GetTopKeywordSearchImpressionsUseCase } from ':modules/keyword-search-impressions/use-cases/get-top-keyword-search-impressions/get-top-keyword-search-impressions.use-case';

beforeAll(() => {
    registerRepositories(['KeywordSearchImpressionsRepository']);
});

describe('GetTopKeywordSearchImpressionsUseCase', () => {
    describe('execute', () => {
        it('should return top keyword search impressions for date range', async () => {
            const getTopKeywordSearchImpressionsUseCase = container.resolve(GetTopKeywordSearchImpressionsUseCase);

            const restaurantId = newDbId();
            const testCase = new TestCaseBuilderV2<'keywordSearchImpressions'>({
                seeds: {
                    keywordSearchImpressions: {
                        data() {
                            return [
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202407)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('keyword1')
                                    .value(100)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202407)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword2')
                                    .value(200)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202408)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('restaurant paris')
                                    .value(300)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202408)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword4')
                                    .value(400)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202409)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('keyword4')
                                    .value(500)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202410)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword4')
                                    .value(600)
                                    .build(),
                                // should be excluded (default keyword)
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202410)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('paris hotels')
                                    .value(700)
                                    .build(),
                                // should be excluded (off limit)
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202411)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword8')
                                    .value(800)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202411)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('keyword9')
                                    .value(900)
                                    .build(),
                                // should be excluded (default keyword)
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202412)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('boulangerie')
                                    .value(1000)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202412)
                                    .type(KeywordSearchImpressionsType.DISCOVERY)
                                    .keywordSearch('keyword9')
                                    .value(1100)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202501)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword9')
                                    .value(1200)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202502)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword9')
                                    .value(1400)
                                    .build(),
                                getDefaultKeywordSearchImpressions()
                                    .restaurantId(restaurantId)
                                    .yearMonthIndex(202502)
                                    .type(KeywordSearchImpressionsType.BRANDING)
                                    .keywordSearch('keyword10')
                                    .value(1400)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(): TopKeywordSearchImpressionsDto {
                    return {
                        [KeywordSearchImpressionsType.BRANDING]: [
                            { keywordSearch: 'keyword9', value: 2600, type: KeywordSearchImpressionsType.BRANDING },
                            { keywordSearch: 'keyword10', value: 1400, type: KeywordSearchImpressionsType.BRANDING },
                            { keywordSearch: 'keyword4', value: 1000, type: KeywordSearchImpressionsType.BRANDING },
                        ],
                        [KeywordSearchImpressionsType.DISCOVERY]: [
                            { keywordSearch: 'keyword9', value: 2000, type: KeywordSearchImpressionsType.DISCOVERY },
                            { keywordSearch: 'keyword4', value: 500, type: KeywordSearchImpressionsType.DISCOVERY },
                        ],
                    };
                },
            });
            await testCase.build();

            const expectedResult = testCase.getExpectedResult();

            const result = await getTopKeywordSearchImpressionsUseCase.execute({
                restaurantId: restaurantId.toString(),
                startMonthYear: { month: 8, year: 2024 },
                endMonthYear: { month: 2, year: 2025 },
                limit: 3,
            });

            expect(result).toEqual(expectedResult);
        });
    });
});
