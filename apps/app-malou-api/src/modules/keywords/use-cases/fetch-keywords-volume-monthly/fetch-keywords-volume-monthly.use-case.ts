import { chunk } from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import {
    errorReplacer,
    isValidKeywordText,
    MalouErrorCode,
    MINIMUM_VOLUME_PROVIDER_CALLS_COUNT_FOR_ONBOARDING,
    MONTHLY_KEYWORDS_VOLUME_UPDATE_DAY,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { KeywordsTempRepository } from ':modules/keywords/keywords-temp.repository';
import { KeywordsVolumePort } from ':modules/keywords/services/keyword-volume-service/keyword-volume.port';
import { AwsScheduler } from ':plugins/scheduler/aws-scheduler';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class FetchKeywordsVolumeMonthlyUseCase {
    constructor(
        private readonly _keywordsRepository: KeywordsTempRepository,
        private readonly _keywordsVolumePort: KeywordsVolumePort,
        private readonly _awsScheduler: AwsScheduler,
        private readonly _slackService: SlackService
    ) {}

    async execute(): Promise<void> {
        try {
            const maxLastFetchDate = this._getMaxLastFetchDate();
            const maxCallsAvailable = await this._getMaxCallsAvailable();

            if (maxCallsAvailable > 0) {
                const keywordsGroupedByApiLocationId = await this._keywordsRepository.getKeywordsToFetchVolumeMonthly(
                    maxLastFetchDate,
                    maxCallsAvailable
                );
                const keywordsGroupedByApiLocationIdWithUniqueTexts = this._removeDuplicateKeywordTexts(keywordsGroupedByApiLocationId);

                let callsCount = 0;
                const updatedKeywordsCountByApiLocationId = new Map<string, number>();
                for (const { apiLocationId, texts } of keywordsGroupedByApiLocationIdWithUniqueTexts) {
                    if (callsCount >= maxCallsAvailable) {
                        break;
                    }
                    const chunkedByApiMaxPerRequest = chunk(texts, this._keywordsVolumePort.getMaxKeywordsPerRequest());
                    const chunkedByApiMaxPerMinute = chunk(chunkedByApiMaxPerRequest, this._keywordsVolumePort.getMaxApiCallsPerMinute());

                    outerLoop: for (const apiMaxPerMinuteChunks of chunkedByApiMaxPerMinute) {
                        for (const keywords of apiMaxPerMinuteChunks) {
                            if (callsCount >= maxCallsAvailable) {
                                break outerLoop;
                            }
                            const cleanKeywordTexts = keywords.filter((keyword) => isValidKeywordText(keyword));
                            const keywordsVolumes = await this._keywordsVolumePort.getKeywordsVolume(cleanKeywordTexts, apiLocationId);
                            await this._keywordsRepository.updateKeywordsVolumes(keywordsVolumes, apiLocationId);
                            updatedKeywordsCountByApiLocationId.set(
                                apiLocationId,
                                (updatedKeywordsCountByApiLocationId.get(apiLocationId) ?? 0) + cleanKeywordTexts.length
                            );
                            callsCount++;
                        }
                        // wait 1 minute between each chunk of 10 requests
                        await waitFor(TimeInMilliseconds.MINUTE);
                    }
                }
                this._sendSlackAlertSuccess(callsCount, updatedKeywordsCountByApiLocationId);
            }

            const needToProgramNextExecution = await this._keywordsRepository.hasKeywordsToFetchVolumeMonthly(maxLastFetchDate);

            if (needToProgramNextExecution) {
                this._programNextExecution();
            }
        } catch (error: any) {
            this._sendSlackAlertError(error);
            throw error;
        }
    }

    private _getMaxLastFetchDate(): Date {
        const now = DateTime.now();

        return now.day < MONTHLY_KEYWORDS_VOLUME_UPDATE_DAY
            ? DateTime.now().minus({ month: 1 }).set({ day: MONTHLY_KEYWORDS_VOLUME_UPDATE_DAY }).toJSDate()
            : now.set({ day: MONTHLY_KEYWORDS_VOLUME_UPDATE_DAY }).toJSDate();
    }

    private async _getMaxCallsAvailable(): Promise<number> {
        const remainingDailyQuota = await this._keywordsVolumePort.getDailyKeywordToolQuotaRemaining();
        return remainingDailyQuota - MINIMUM_VOLUME_PROVIDER_CALLS_COUNT_FOR_ONBOARDING;
    }

    private _programNextExecution(): void {
        const fetchKeywordsVolumeQueueUrl = Config.services.sqs.fetchKeywordsVolumeQueueArn;
        const fetchKeywordsVolumeArnRole = Config.services.sqs.fetchKeywordsVolumeArnRole;

        this._awsScheduler.scheduleTask({
            task: 'FetchKeywordsVolumeMonthly',
            payload: {},
            at: DateTime.now().plus({ day: 1 }).toJSDate(),
            target: { url: fetchKeywordsVolumeQueueUrl, role: fetchKeywordsVolumeArnRole },
            logPrefix: '[FETCH_KEYWORDS_VOLUME_MONTHLY]',
        });
    }

    private _sendSlackAlertSuccess(callsCount: number, updatedKeywordsCountByApiLocationId: Map<string, number>): void {
        const updatedKeywordsCount = Array.from(updatedKeywordsCountByApiLocationId.values()).reduce((acc, count) => acc + count, 0);

        this._slackService.sendMessage({
            channel: SlackChannel.APP_ALERTS,
            text: `:white_check_mark: [FETCH_KEYWORDS_VOLUME_MONTHLY] Job completed - Updated ${updatedKeywordsCount} 
            keywords in ${updatedKeywordsCountByApiLocationId.size} API locations with ${callsCount} calls`,
        });
    }

    private _sendSlackAlertError(error: Error): void {
        this._slackService.sendAlert({
            channel: SlackChannel.APP_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.FETCH_KEYWORDS_VOLUME_MONTHLY_ERROR, {
                    message: error.message ?? JSON.stringify(error, errorReplacer),
                    metadata: { rawError: error },
                }),
                metadata: { description: `[FETCH_KEYWORDS_VOLUME_MONTHLY] Job failed` },
            },
        });
    }

    private _removeDuplicateKeywordTexts(
        keywordsGroupedByApiLocationId: { apiLocationId: string; texts: string[] }[]
    ): { apiLocationId: string; texts: string[] }[] {
        return keywordsGroupedByApiLocationId.map((keywordsWithApiLocationId) => {
            const uniqueTexts = Array.from(new Set(keywordsWithApiLocationId.texts));
            return { apiLocationId: keywordsWithApiLocationId.apiLocationId, texts: uniqueTexts };
        });
    }
}
