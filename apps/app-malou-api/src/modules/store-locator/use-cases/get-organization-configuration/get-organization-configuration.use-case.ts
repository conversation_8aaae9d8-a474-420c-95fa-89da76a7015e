import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';
import { toDbId } from '@malou-io/package-models';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
export class GetStoreLocatorOrganizationConfigurationUseCase {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(organizationId: string): Promise<GetStoreLocatorOrganizationConfigurationDto> {
        const storeLocatorOrganizationConfig = await this._storeLocatorOrganizationConfigRepository.findOne({
            filter: { organizationId: toDbId(organizationId) },
            options: { lean: true, populate: [{ path: 'organization' }] },
        });
        assert(storeLocatorOrganizationConfig?.organization);
        const tailwindConfig = this._generateTailwindConfigurationService.execute({
            storeLocatorOrganizationConfig,
        });

        return {
            cloudfrontDistributionId: storeLocatorOrganizationConfig.cloudfrontDistributionId,
            organizationName: storeLocatorOrganizationConfig.organization.name,
            baseUrl: storeLocatorOrganizationConfig.baseUrl,
            tailwindConfig,
        };
    }
}
