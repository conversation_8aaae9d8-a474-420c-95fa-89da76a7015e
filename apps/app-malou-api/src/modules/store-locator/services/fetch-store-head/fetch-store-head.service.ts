import lodash from 'lodash';
import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { GetStoreLocatorStorePageDto, storeLocatorStorePageHeadBlockValidator } from '@malou-io/package-dto';
import {
    IRestaurant,
    IRestaurantAttribute,
    IStoreLocatorOrganizationConfigWithOrganization,
    IStoreLocatorRestaurantPage,
    PopulateBuilderHelper,
    toDbId,
} from '@malou-io/package-models';
import {
    Day,
    formatPhoneForDisplay,
    getXUserName,
    RestaurantAttributeValue,
    SocialNetworkKey,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { filterByRequiredKeys } from ':helpers/validators/filter-by-required-keys';
import { MalouAttributesEnum } from ':modules/attributes/mappings/attributes.malou';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import { FetchLatestGmbRatingService } from ':modules/store-locator/services/fetch-latest-gmb-rating/fetch-latest-gmb-rating.service';
import { RestaurantPopulatedForHeadBlock } from ':modules/store-locator/services/fetch-store-head/interfaces';
import { GetPaymentMethodsService } from ':modules/store-locator/services/get-payment-methods/get-payment-methods.service';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';
import { AwsS3 } from ':plugins/cloud-storage/s3';

@singleton()
export class FetchStoreLocatorHeadBlockService {
    private readonly _DAY_MAPPING: Record<Day, string> = {
        [Day.MONDAY]: 'Mo',
        [Day.TUESDAY]: 'Tu',
        [Day.WEDNESDAY]: 'We',
        [Day.THURSDAY]: 'Th',
        [Day.FRIDAY]: 'Fr',
        [Day.SATURDAY]: 'Sa',
        [Day.SUNDAY]: 'Su',
    };
    private readonly _LOCALE_MAPPING: Record<StoreLocatorLanguage, string> = {
        [StoreLocatorLanguage.FR]: 'fr_FR',
        [StoreLocatorLanguage.EN]: 'en_US',
    };

    constructor(
        private readonly _getPaymentMethodsService: GetPaymentMethodsService,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _platformRepository: PlatformsRepository,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository,
        private readonly _fetchLatestGmbRatingService: FetchLatestGmbRatingService,
        private readonly _cloudStorageService: AwsS3
    ) {}

    async execute({
        restaurant,
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        restaurant: RestaurantPopulatedForHeadBlock;
        storeLocatorOrganizationConfig: IStoreLocatorOrganizationConfigWithOrganization;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['headBlock'] | undefined }> {
        try {
            const url = storeLocatorRestaurantPage.fullUrl;
            const snippetImageUrl = storeLocatorRestaurantPage.blocks.head.snippetImageUrl;
            const facebookImageUrl = storeLocatorRestaurantPage.blocks.head.facebookImageUrl;
            const twitterImageUrl = storeLocatorRestaurantPage.blocks.head.twitterImageUrl;
            const title = storeLocatorRestaurantPage.blocks.head.title;
            const description = storeLocatorRestaurantPage.blocks.head.description;
            const keywords = storeLocatorRestaurantPage.blocks.head.keywords;
            const xUserName = this._getXUserName(restaurant);
            const locale = this._LOCALE_MAPPING[storeLocatorRestaurantPage.lang];
            const organizationId = storeLocatorOrganizationConfig.organizationId.toString();
            const googleAnalyticsId = storeLocatorOrganizationConfig.plugins?.googleAnalytics?.trackingId;

            const microdata = await this._getMicroData({
                restaurant,
                storeLocatorRestaurantPage,
                description,
                url,
            });

            const headBlock = {
                title,
                description,
                twitterDescription: storeLocatorRestaurantPage.blocks.head.twitterDescription,
                keywords,
                url,
                snippetImageUrl,
                facebookImageUrl,
                twitterImageUrl,
                locale,
                xUserName,
                ...(googleAnalyticsId && { googleAnalyticsId }),
                organizationName: storeLocatorOrganizationConfig.organization.name,
                favIconUrl: `${this._cloudStorageService.getBucketBaseUrl()}/store-locator/organization/${organizationId}/favicons/favicon.png`,
                microdata,
            };

            const parsedHeadBlock = await storeLocatorStorePageHeadBlockValidator.parseAsync(headBlock);

            logger.info('[STORE_LOCATOR] [Head block] Head block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.head.backup': parsedHeadBlock },
            });

            return { success: true, data: parsedHeadBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Head block] Failed to fetch store information, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.head?.backup) {
                try {
                    const headBlock = storeLocatorRestaurantPage.blocks.head.backup;
                    const parsedHeadBlock = await storeLocatorStorePageHeadBlockValidator.parseAsync(headBlock);

                    return { success: false, data: parsedHeadBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Head block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    private _getXUserName(restaurant: RestaurantPopulatedForHeadBlock): string | undefined {
        const xProfileUrl = restaurant.socialNetworkUrls?.find(({ key }) => key === SocialNetworkKey.X)?.url;
        return getXUserName(xProfileUrl) ?? undefined;
    }

    private async _getMicroData({
        restaurant,
        storeLocatorRestaurantPage,
        description,
        url,
    }: {
        restaurant: RestaurantPopulatedForHeadBlock;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        description: string;
        url: string;
    }): Promise<string> {
        const cuisineType = storeLocatorRestaurantPage.blocks.head.schemaOrgCuisineType;
        const priceRange = '$$'; // todo store-locator
        const openingHours = this._mapOpeningHours(restaurant.regularHours);
        const openingHoursSpecification = this._mapSpecialHours(restaurant.specialHours);
        const phone = formatPhoneForDisplay({
            prefix: restaurant.phone?.prefix ?? undefined,
            digits: restaurant.phone?.digits ?? undefined,
        });

        const [attributes, platforms, gmbReviewsData] = await Promise.all([
            this._restaurantAttributesRepository.find({
                filter: {
                    restaurantId: toDbId(restaurant._id),
                    attributeValue: RestaurantAttributeValue.YES,
                },
                options: {
                    lean: true,
                    populate: [
                        {
                            path: 'attribute',
                        },
                    ],
                },
            }),
            this._platformRepository.find({
                filter: {
                    restaurantId: toDbId(restaurant._id),
                },
                projection: {
                    socialLink: 1,
                },
                options: {
                    lean: true,
                },
            }),
            this._fetchLatestGmbRatingService.execute({
                restaurantId: restaurant._id.toString(),
            }),
        ]);

        const paymentMethodAttributes = this._getPaymentMethodsService.execute(attributes);
        const paymentAccepted = [...paymentMethodAttributes.map((pM) => pM.attributeName[storeLocatorRestaurantPage.lang]), 'Espèces'].join(
            ', '
        );
        const acceptsReservations = this._getReservationValue(restaurant, attributes);

        const microdata = {
            '@context': 'https://schema.org',
            '@type': 'Restaurant',
            name: restaurant.name,
            description,
            url,
            ...(phone && { telephone: phone }),
            ...(restaurant.address && {
                address: {
                    '@type': 'PostalAddress',
                    ...(restaurant.address?.route && { streetAddress: restaurant.address?.route }),
                    ...(restaurant.address?.locality && { addressLocality: restaurant.address?.locality }),
                    ...(restaurant.address?.regionCode && { addressRegion: restaurant.address?.regionCode }),
                    ...(restaurant.address?.postalCode && { postalCode: restaurant.address?.postalCode }),
                    ...(restaurant.address?.country && { addressCountry: restaurant.address?.country }),
                },
            }),
            ...(restaurant.latlng && {
                geo: {
                    '@type': 'GeoCoordinates',
                    latitude: restaurant.latlng.lat,
                    longitude: restaurant.latlng.lng,
                },
            }),
            ...(restaurant.menuUrl && { menu: restaurant.menuUrl }),
            servesCuisine: cuisineType,
            priceRange,
            ...(openingHours.length > 0 && { openingHours }),
            openingHoursSpecification,
            ...(gmbReviewsData && {
                aggregateRating: {
                    '@type': 'AggregateRating',
                    ratingValue: gmbReviewsData.rating,
                    reviewCount: gmbReviewsData.reviewsCount,
                },
            }),
            paymentAccepted,
            acceptsReservations,
            ...(restaurant.latlng && {
                hasMap: `https://www.google.com/maps/place/${restaurant.latlng.lat},${restaurant.latlng.lng}`,
            }),
            sameAs: lodash.uniq(
                [
                    ...(restaurant.socialNetworkUrls?.map((socialNetworkUrl) => socialNetworkUrl.url) ?? []),
                    ...platforms.map((platform) => platform.socialLink),
                    restaurant.website,
                ].filter(Boolean)
            ),
        };

        return JSON.stringify(microdata);
    }

    private _mapOpeningHours(regularHours: IRestaurant['regularHours']): string[] {
        // Group the hours by openDay
        const groupedByDay = {} as Record<Day, IRestaurant['regularHours']>;

        (regularHours ?? []).forEach(({ openDay, openTime, closeTime, isClosed }) => {
            if (!isClosed) {
                let hoursForDay = groupedByDay[openDay];
                if (!hoursForDay) {
                    hoursForDay = [];
                }
                hoursForDay.push({ openDay, openTime, closeDay: openDay, closeTime, isClosed: false });
            }
        });

        // Create the openingHours array by formatting each day's hours
        const openingHours: string[] = [];

        Object.keys(groupedByDay).forEach((day) => {
            const dayAbbr = this._DAY_MAPPING[day];
            const times = groupedByDay[day];

            // If there are multiple time ranges for the same day, add them separately
            times?.forEach(({ openTime, closeTime }) => {
                openingHours.push(`${dayAbbr} ${openTime}-${closeTime}`);
            });
        });

        // Return the final schema.org formatted data
        return openingHours;
    }

    private _mapSpecialHours(specialHours: IRestaurant['specialHours']) {
        const onlyFutureSpecialHours = filterByRequiredKeys(specialHours, ['startDate', 'endDate'])
            .filter((sH) => {
                const startDate = DateTime.fromObject({
                    day: sH.startDate.day,
                    month: sH.startDate.month + 1, // because month is 0 indexed
                    year: sH.startDate.year,
                }).toJSDate();
                const now = DateTime.now().startOf('day').toJSDate();
                return startDate >= now;
            })
            .map((sH) => {
                return {
                    startDate: DateTime.fromObject({
                        day: sH.startDate.day,
                        month: sH.startDate.month + 1, // because month is 0 indexed
                        year: sH.startDate.year,
                    }).toJSDate(),
                    endDate: DateTime.fromObject({
                        day: sH.endDate.day,
                        month: sH.endDate.month + 1, // because month is 0 indexed
                        year: sH.endDate.year,
                    }).toJSDate(),
                    isClosed: sH.isClosed,
                    openTime: sH.openTime,
                    closeTime: sH.closeTime,
                };
            });
        return onlyFutureSpecialHours.map((sH) => {
            if (sH.isClosed) {
                return {
                    '@type': 'OpeningHoursSpecification',
                    validFrom: sH.startDate.toISOString().split('T')[0],
                    validThrough: sH.endDate.toISOString().split('T')[0],
                };
            }
            return {
                '@type': 'OpeningHoursSpecification',
                validFrom: sH.startDate.toISOString().split('T')[0],
                validThrough: sH.endDate.toISOString().split('T')[0],
                opens: sH.openTime,
                closes: sH.closeTime,
            };
        });
    }

    private _getReservationValue(
        restaurant: RestaurantPopulatedForHeadBlock,
        attributes: Pick<
            PopulateBuilderHelper<
                IRestaurantAttribute,
                [
                    {
                        path: 'attribute';
                    },
                ]
            >,
            'attribute'
        >[]
    ): boolean | string {
        if (restaurant.reservationUrl) {
            return restaurant.reservationUrl;
        }
        const reservation = attributes.find((a) => a.attribute.attributeId === MalouAttributesEnum.ACCEPTS_RESERVATIONS);
        if (reservation) {
            return true;
        }
        return false;
    }
}
