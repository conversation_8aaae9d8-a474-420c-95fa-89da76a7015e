import { singleton } from 'tsyringe';

import { IStoreLocatorOrganizationConfig } from '@malou-io/package-models';

@singleton()
export class GenerateTailwindConfigurationService {
    execute({ storeLocatorOrganizationConfig }: { storeLocatorOrganizationConfig: IStoreLocatorOrganizationConfig }): string {
        const tailwindConfig = `
@import 'tailwindcss';

/* Register your custom font family and tell the browser where to find it. */
${storeLocatorOrganizationConfig.styles.fonts
    .map((font) => {
        const extension = font.src.split('.').pop();
        return `@font-face {
    font-family: '${font.class}';
    src: url('${font.src}') format('${extension}');
    font-weight: ${font?.weight || 'normal'};
    font-style: ${font?.style || 'normal'};
    font-display: swap;
}`;
    })
    .join('\n')}

@theme {
${storeLocatorOrganizationConfig.styles.colors
    .map((color) => {
        return `--color-${color.class}: ${color.value};`;
    })
    .join('\n')}
    /* Define custom fonts */
    /* Font-sans is used to define default font family */
    --font-sans: '${storeLocatorOrganizationConfig.styles.fonts[0].class}', sans-serif;
${storeLocatorOrganizationConfig.styles.fonts
    .map((font) => {
        return `--font-${font.class}: '${font.class}', sans-serif;`;
    })
    .join('\n')}
}
    
@layer utilities {
    /* Hide scrollbar for Chrome, Safari and Opera */
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    /* Hide scrollbar for IE, Edge and Firefox */
    .no-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
}
`;

        return tailwindConfig;
    }
}
