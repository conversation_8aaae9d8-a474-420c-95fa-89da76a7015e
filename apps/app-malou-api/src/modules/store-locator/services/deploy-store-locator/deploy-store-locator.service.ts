import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';
import { GithubWorkflowName } from ':providers/github/github.interfaces';
import { GithubProvider } from ':providers/github/github.provider';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class DeployStoreLocatorService {
    constructor(
        private readonly _githubProvider: GithubProvider,
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute({ organizationId }: { organizationId: string }): Promise<void> {
        try {
            const organizationStoreLocatorConfiguration = await this._storeLocatorOrganizationConfigRepository.findOne({
                filter: { organizationId },
                options: { lean: true },
            });

            if (!organizationStoreLocatorConfiguration) {
                logger.warn('[STORE_LOCATOR] No configuration found for this organization', {
                    organizationId,
                });
                return;
            }

            logger.info('[STORE_LOCATOR] About to trigger deployment', {
                organizationId,
            });

            await this._githubProvider.triggerWorkflow({
                workflowName: GithubWorkflowName.DEPLOY_STORE_LOCATOR,
                inputs: {
                    organizationId,
                    environment: Config.env === 'production' ? 'production' : 'test',
                },
            });

            logger.info('[STORE_LOCATOR] Deployment triggered', {
                organizationId,
                branchName: Config.branchName,
            });
        } catch (err) {
            logger.error('[STORE_LOCATOR] Error triggering deployment', {
                organizationId,
                err,
            });

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.STORE_LOCATOR_ALERTS });
        }
    }
}
