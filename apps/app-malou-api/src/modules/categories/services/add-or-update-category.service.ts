import { singleton } from 'tsyringe';

import { ICategory } from '@malou-io/package-models';
import { ApplicationLanguage, PlatformKey } from '@malou-io/package-utils';

import { Config } from ':config';
import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { logger } from ':helpers/logger';
import CategoriesRepository from ':modules/categories/categories.repository';
import { GetCategoryTranslationsService } from ':modules/categories/services/get-category-translations.service';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class AddOrUpdateCategoriesService {
    constructor(
        private readonly _categoriesRepository: CategoriesRepository,
        private readonly _slackService: SlackService,
        private readonly _asyncLocalStorageService: AsyncLocalStorageService,
        private readonly _getCategoryTranslationsService: GetCategoryTranslationsService
    ) {}

    async execute({
        categories,
        platformKey,
    }: {
        categories: { categoryId: string; categoryName: string }[];
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK;
    }): Promise<void> {
        const categoryIds = categories.map((category) => category.categoryId);

        // Get existing categories in DB
        const categoriesInDb = await this._categoriesRepository.find({
            filter: {
                platformKey,
                categoryId: { $in: categoryIds },
            },
            projection: { _id: 1, categoryId: 1, canBeUsed: 1, isFood: 1, categoryName: 1 },
            options: { lean: true },
        });

        const existingDbCategoryIds = categoriesInDb.map((category) => category.categoryId);
        const categoriesToUpsert = categories.filter(({ categoryId }) => !existingDbCategoryIds.includes(categoryId));
        const categoriesToActivate = categoriesInDb.filter((category) => categoryIds.includes(category.categoryId) && !category.canBeUsed);

        await Promise.all([
            this._addCategories({ categories: categoriesToUpsert, platformKey }),
            this._activateCategories({ categories: categoriesToActivate, platformKey }),
        ]);
    }

    private async _addCategories({
        categories,
        platformKey,
    }: {
        categories: { categoryId: string; categoryName: string }[];
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK;
    }): Promise<void> {
        if (categories.length === 0) {
            logger.info('[CATEGORIES] No new categories to add', {
                platformKey,
                categories,
            });
            return;
        }

        logger.info('[CATEGORIES] Adding categories', {
            categories,
            platformKey,
            count: categories.length,
        });

        const categoriesToCreate = await Promise.all(
            categories.map(async ({ categoryId, categoryName }) => {
                const categoryNameWithTranslations = await this._getCategoryTranslationsService.execute({
                    categoryName: {
                        backup: categoryName,
                        [ApplicationLanguage.FR]: categoryName,
                    },
                });

                return {
                    categoryId,
                    platformKey,
                    canBeUsed: true,
                    isFood: false,
                    categoryName: categoryNameWithTranslations,
                };
            })
        );

        await this._categoriesRepository.createMany({
            data: categoriesToCreate,
        });

        this._slackService.sendMessage({
            text: this._formatAddedCategoriesMessageForSlack(categories, platformKey),
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
        });

        logger.info('[CATEGORIES] Added categories', {
            categories,
            platformKey,
            count: categories.length,
        });
    }

    private _formatAddedCategoriesMessageForSlack(
        categories: { categoryId: string; categoryName: string }[],
        platformKey: PlatformKey
    ): string {
        const restaurantId = this._asyncLocalStorageService.getStore()?.restaurant?.id;
        const newCategories = categories
            .map((category) => `\n>*name:* ${category.categoryName}, *categoryId:* ${category.categoryId}`)
            .join('');
        const restaurantLink = restaurantId ? `\nAdded by <${Config.baseAppUrl}/restaurants/${restaurantId}|this restaurant>` : '';

        const message = `🧐 *Added ${categories.length} new ${platformKey} categories*${newCategories}${restaurantLink}`;

        return message;
    }

    private async _activateCategories({
        categories,
        platformKey,
    }: {
        categories: Pick<ICategory, '_id' | 'canBeUsed' | 'isFood' | 'categoryId' | 'categoryName'>[];
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK;
    }): Promise<void> {
        if (categories.length === 0) {
            logger.info('[CATEGORIES] No categories to activate');
            return;
        }

        logger.info('[CATEGORIES] Activating categories', {
            categories: categories.map((category) => ({
                id: category._id,
                categoryId: category.categoryId,
                name: category.categoryName.backup,
            })),
            count: categories.length,
        });

        await this._categoriesRepository.activateCategories({
            ids: categories.map((category) => category._id.toString()),
        });

        this._slackService.sendMessage({
            text: this._formatUpdatedCategoriesMessageForSlack(categories, platformKey),
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
        });

        logger.info('[CATEGORIES] Activated categories', {
            categories: categories.map((category) => ({
                id: category._id,
                categoryId: category.categoryId,
                name: category.categoryName.backup,
            })),
            count: categories.length,
        });
    }

    private _formatUpdatedCategoriesMessageForSlack(
        categories: Pick<ICategory, '_id' | 'canBeUsed' | 'isFood' | 'categoryId' | 'categoryName'>[],
        platformKey: PlatformKey.GMB | PlatformKey.FACEBOOK
    ): string {
        const restaurantId = this._asyncLocalStorageService.getStore()?.restaurant?.id;

        const activatedCategories = categories
            .map((category) => `\n>*name:* ${category.categoryName.backup}, *categoryId:* ${category.categoryId}`)
            .join();
        const restaurantLink = restaurantId ? `\nAdded by <${Config.baseAppUrl}/restaurants/${restaurantId}|this restaurant>` : '';

        const message = `🧐 *Activated ${categories.length} new ${platformKey} categories*${activatedCategories}${restaurantLink}`;

        return message;
    }
}
