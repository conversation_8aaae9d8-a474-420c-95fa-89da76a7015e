import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { toDbId, toDbIds } from '@malou-io/package-models';
import { errorReplacer, getLastXMonthsBeforeCurrentMonth, MalouErrorCode, MonthAndYear } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { Restaurant } from ':modules/restaurants/entities/restaurant.entity';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { BodyMessageMonthlySaveRoiInsights } from ':modules/roi-insights/queues/types';
import { RoiInsightsPayload, RoiInsightsRepository } from ':modules/roi-insights/roi-insights.repository';
import { RoiSettingsRepository } from ':modules/roi-settings/roi-settings.repository';
import { ComputeAdditionalCustomersByMonthUseCase } from ':modules/roi/use-cases/compute-additional-customers-by-month/compute-additional-customers-by-month.use-case';
import { ComputePerformanceScoreByMonthUseCase } from ':modules/roi/use-cases/compute-performance-score-by-month/compute-performance-score-by-month.use-case';
import { ComputeSavedTimeByMonthUseCase } from ':modules/roi/use-cases/compute-saved-time-by-month/compute-saved-time-by-month.use-case';
import { ComputeRoiTipsUseCase } from ':modules/roi/use-cases/compute-tips/compute-tips.use-case';
import { SimilarRestaurantsRepository } from ':modules/similar-restaurants/similar-restaurants.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

enum LoggerEvents {
    START = '[MONTHLY SAVE ROI INSIGHTS] START',
    COMPUTE_ROI_SETTINGS = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_ROI_SETTINGS',
    COMPUTE_SIMILAR_RESTAURANTS = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_SIMILAR_RESTAURANTS',
    COMPUTE_SAVED_TIME = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_SAVED_TIME',
    COMPUTE_PERFORMANCE_SCORE = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_PERFORMANCE_SCORE',
    COMPUTE_ADDITIONAL_CUSTOMERS = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_ADDITIONAL_CUSTOMERS',
    COMPUTE_TIPS = '[MONTHLY SAVE ROI INSIGHTS] COMPUTE_TIPS',
    CREATE_ROI_INSIGHTS = '[MONTHLY SAVE ROI INSIGHTS] CREATE_ROI_INSIGHTS',
    SAVING_ROI_INSIGHTS = '[MONTHLY SAVE ROI INSIGHTS] SAVING_ROI_INSIGHTS',
    END_SUCCESS = '[MONTHLY SAVE ROI INSIGHTS] END_SUCCESS',
    END_ERROR = '[MONTHLY SAVE ROI INSIGHTS] END_ERROR',
    RESTAURANT_NOT_FOUND = '[MONTHLY SAVE ROI INSIGHTS] RESTAURANT_NOT_FOUND',
}

@singleton()
export class MonthlySaveRoiInsightsUseCase {
    constructor(
        private readonly _roiSettingsRepository: RoiSettingsRepository,
        private readonly _similarRestaurantsRepository: SimilarRestaurantsRepository,
        private readonly _computeSavedTimeByMonthUseCase: ComputeSavedTimeByMonthUseCase,
        private readonly _computePerformanceScore: ComputePerformanceScoreByMonthUseCase,
        private readonly _computeAdditionalCustomersByMonthUseCase: ComputeAdditionalCustomersByMonthUseCase,
        private readonly _computeRoiTips: ComputeRoiTipsUseCase,
        private readonly _roiInsightsRepository: RoiInsightsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute(body: BodyMessageMonthlySaveRoiInsights) {
        const now = DateTime.now();
        const previousMonth = now.minus({ days: 5 });

        const month: number = body?.month ?? previousMonth.month;
        const year: number = body?.year ?? previousMonth.year;

        logger.info(LoggerEvents.START, { body });

        const restaurantId = body.restaurantId;

        try {
            const foundRestaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);
            if (!foundRestaurant) {
                logger.error(LoggerEvents.RESTAURANT_NOT_FOUND, { restaurantId });
                return;
            }
            const restaurant = new Restaurant(foundRestaurant);
            const monthStartDate = DateTime.utc(year, month, 1).startOf('month').startOf('day').toJSDate();
            if (restaurant.isCreatedAfterDate(monthStartDate)) {
                logger.info(LoggerEvents.END_SUCCESS, { computationTime: DateTime.now().diff(now).as('milliseconds') });
                return;
            }

            logger.info(LoggerEvents.COMPUTE_ROI_SETTINGS);
            const roiSettings = await this._roiSettingsRepository.getRoiSettingByRestaurantId(restaurantId);
            if (!roiSettings?.isComplete()) {
                logger.info(LoggerEvents.END_SUCCESS, { computationTime: DateTime.now().diff(now).as('milliseconds') });
                return;
            }

            logger.info(LoggerEvents.COMPUTE_SIMILAR_RESTAURANTS);
            const similarRestaurants = await this._similarRestaurantsRepository.findByRestaurantId({
                restaurantId,
            });

            logger.info(LoggerEvents.COMPUTE_SAVED_TIME);
            const savedTime = await this._computeSavedTimeByMonthUseCase.execute({ restaurantId, month, year });

            logger.info(LoggerEvents.COMPUTE_PERFORMANCE_SCORE);
            const performanceScore = await this._computePerformanceScore.execute({
                restaurantId,
                month,
                year,
            });

            logger.info(LoggerEvents.COMPUTE_ADDITIONAL_CUSTOMERS);
            const additionalCustomers = await this._computeAdditionalCustomersByMonthUseCase.execute({
                restaurantId,
                month,
                year,
                currentMonthPerformanceScore: performanceScore.performanceScore,
            });

            logger.info(LoggerEvents.COMPUTE_TIPS);
            const months: MonthAndYear[] = getLastXMonthsBeforeCurrentMonth(3, { currentMonth: month, currentYear: year });
            const tips = (await this._computeRoiTips.execute(restaurantId, months)).map((tip) => tip.id);

            logger.info(LoggerEvents.CREATE_ROI_INSIGHTS);
            const roiInsightsToSave: RoiInsightsPayload = {
                restaurantId: toDbId(restaurantId),
                savedTime,
                roiSettingsSnapshot: roiSettings
                    ? {
                          currency: roiSettings.currency,
                          averageTicket: roiSettings.averageTicket,
                          minRevenue: roiSettings.minRevenue,
                          maxRevenue: roiSettings.maxRevenue,
                          duplicatedFromRestaurantId: roiSettings.duplicatedFromRestaurantId
                              ? toDbId(roiSettings.duplicatedFromRestaurantId)
                              : undefined,
                      }
                    : null,
                similarRestaurantsSnapshot: similarRestaurants
                    ? {
                          clusterId: similarRestaurants.clusterId,
                          similarRestaurantIds: toDbIds(similarRestaurants.similarRestaurantIds),
                          restaurantCategory: similarRestaurants.restaurantCategory,
                      }
                    : null,
                performanceScore,
                additionalCustomers,
                tips,
                month,
                year,
            };

            logger.info(LoggerEvents.SAVING_ROI_INSIGHTS);
            await this._roiInsightsRepository.upsertByRestaurantId({ restaurantId, month, year, data: roiInsightsToSave });

            logger.info(LoggerEvents.END_SUCCESS, { computationTime: DateTime.now().diff(now).as('milliseconds') });
        } catch (err: any) {
            logger.error(LoggerEvents.END_ERROR, { restaurantId, error: err });
            void this._sendSlackAlertError(err, restaurantId);
        }
    }

    private _sendSlackAlertError(error: Error, restaurantId: string): void {
        this._slackService.sendAlert({
            channel: SlackChannel.APP_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.MONTHLY_SAVE_ROI_INSIGHTS_ERROR, {
                    message: error.message ?? JSON.stringify(error, errorReplacer),
                    metadata: { rawError: error, restaurantId },
                }),
                metadata: { description: `[MONTHLY SAVE ROI INSIGHTS] Job failed`, restaurantName: restaurantId },
            },
        });
    }
}
