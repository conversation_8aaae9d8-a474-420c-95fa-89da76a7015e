import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import { WheelOfFortuneDto } from '@malou-io/package-dto';
import { DbId, toDbId } from '@malou-io/package-models';
import { PlatformKey, platformsKeys } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import NfcsRepository from ':modules/nfc/nfcs.repository';
import { getDefaultTotem } from ':modules/nfc/tests/nfc.builder';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';

import { WheelsOfFortuneRepository } from '../wheels-of-fortune.repository';
import { WheelsOfFortuneUseCases } from '../wheels-of-fortune.use-cases';
import { getDefaultAggregatedWheelsOfFortune, getDefaultGiftDraw, getDefaultRestaurantWheelsOfFortune } from './wheels-of-fortune.builder';

describe('WheelsOfFortuneUseCases', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantWheelsOfFortuneRepository',
            'AggregatedWheelsOfFortuneRepository',
            'WheelsOfFortuneRepository',
            'RestaurantsRepository',
            'GiftsRepository',
            'GiftStocksRepository',
            'ClientsRepository',
            'GiftDrawsRepository',
            'NfcsRepository',
            'PlatformsRepository',
            'UsersRepository',
            'UserRestaurantsRepository',
        ]);
    });

    describe('getRestaurantsWithoutActiveWheel', () => {
        it('should return empty array if user has no restaurants', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithoutActiveWheel([]);

            expect(result).toEqual([]);
        });

        it('should return restaurantIds that have inactive restaurant wheels of fortune without aggregated wheels of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').internalName('Internal Name 1').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').internalName('Internal Name 2').build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[1]._id)).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [dependencies.restaurants[1]._id.toString()];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithoutActiveWheel(restaurantIds);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toIncludeSameMembers(expectedResult);
        });

        it('should return restaurantIds that have inactive restaurant wheels of fortune with aggregated wheels of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'aggregatedWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234409').build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [
                                getDefaultAggregatedWheelsOfFortune().startDate(startDate).endDate(endDate).build(),
                                getDefaultAggregatedWheelsOfFortune().build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[1]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [dependencies.restaurants[2]._id.toString()];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithoutActiveWheel(restaurantIds);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toIncludeSameMembers(expectedResult);
        });

        it("should return restaurantIds that don't have any wheels of fortune", async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return [dependencies.restaurants[0]._id.toString()];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithoutActiveWheel(restaurantIds);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toIncludeSameMembers(expectedResult);
        });
    });

    describe('getRestaurantsWithActiveOrProgrammedWheel', () => {
        it('should return empty arrays if user has no restaurants', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithActiveOrProgrammedWheel([]);

            expect(result).toEqual({
                restaurantsWithActiveAggregatedWheel: [],
                restaurantsWithActiveWheel: [],
            });
        });

        it('should return restaurantIds with active aggregated wheels of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [
                                getDefaultAggregatedWheelsOfFortune().startDate(startDate).endDate(endDate).build(),
                                getDefaultAggregatedWheelsOfFortune().startDate(startDate).build(),
                                getDefaultAggregatedWheelsOfFortune().endDate(endDate).build(),
                                getDefaultAggregatedWheelsOfFortune().build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[1]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[2]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[3]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        restaurantsWithActiveAggregatedWheel: [
                            dependencies.restaurants[0]._id.toString(),
                            dependencies.restaurants[1]._id.toString(),
                        ],
                        restaurantsWithActiveWheel: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userRestaurants = seededObjects.restaurants.map((restaurant) => ({
                restaurantId: (restaurant._id as DbId).toString(),
            }));

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithActiveOrProgrammedWheel(
                userRestaurants.map(({ restaurantId }) => restaurantId)
            );

            const expectedResult = testCase.getExpectedResult();
            expect(result.restaurantsWithActiveAggregatedWheel).toIncludeSameMembers(expectedResult.restaurantsWithActiveAggregatedWheel);
            expect(result.restaurantsWithActiveWheel).toEqual([]);
        });

        it('should return restaurantIds with active restaurant wheels of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const startDate = DateTime.now().minus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .startDate(startDate)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .endDate(endDate)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[3]._id)).build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        restaurantsWithActiveAggregatedWheel: [],
                        restaurantsWithActiveWheel: [
                            dependencies.restaurants[0]._id.toString(),
                            dependencies.restaurants[1]._id.toString(),
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userRestaurants = seededObjects.restaurants.map((restaurant) => ({
                restaurantId: (restaurant._id as DbId).toString(),
            }));

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithActiveOrProgrammedWheel(
                userRestaurants.map(({ restaurantId }) => restaurantId)
            );

            const expectedResult = testCase.getExpectedResult();
            expect(result.restaurantsWithActiveAggregatedWheel).toEqual([]);
            expect(result.restaurantsWithActiveWheel).toIncludeSameMembers(expectedResult.restaurantsWithActiveWheel);
        });

        it('should return restaurantIds with future active restaurant or aggregated wheels of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const startDate = DateTime.now().plus({ days: 1 }).toJSDate();
            const endDate = DateTime.now().plus({ days: 2 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [
                                getDefaultAggregatedWheelsOfFortune().startDate(startDate).endDate(endDate).build(),
                                getDefaultAggregatedWheelsOfFortune().build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(startDate)
                                    .endDate(endDate)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[1]._id)).build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[1]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        restaurantsWithActiveAggregatedWheel: [dependencies.restaurants[2]._id.toString()],
                        restaurantsWithActiveWheel: [dependencies.restaurants[0]._id.toString()],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userRestaurants = seededObjects.restaurants.map((restaurant) => ({
                restaurantId: (restaurant._id as DbId).toString(),
            }));

            const result = await wheelsOfFortuneUseCases.getRestaurantsWithActiveOrProgrammedWheel(
                userRestaurants.map(({ restaurantId }) => restaurantId)
            );

            const expectedResult = testCase.getExpectedResult();
            expect(result.restaurantsWithActiveAggregatedWheel).toIncludeSameMembers(expectedResult.restaurantsWithActiveAggregatedWheel);
            expect(result.restaurantsWithActiveWheel).toIncludeSameMembers(expectedResult.restaurantsWithActiveWheel);
        });
    });

    describe('getWheelOfFortunePlayerEmails', () => {
        it('should return existing players email for restaurant wheel of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const email1 = '<EMAIL>';
            const email2 = '<EMAIL>';
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants' | 'giftDraws' | 'clients'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build()];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().email(email1).build(), getDefaultClient().email(email2).build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[0]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[1]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.restaurantWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [email1, email2];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();

            const result = await wheelsOfFortuneUseCases.getWheelOfFortunePlayerEmails(wheelOfFortuneId, restaurantId);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toIncludeAllMembers(expectedResult);
        });

        it('should return existing players email for aggregated wheel of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const email1 = '<EMAIL>';
            const email2 = '<EMAIL>';
            const testCase = new TestCaseBuilderV2<
                'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants' | 'giftDraws' | 'clients'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                    clients: {
                        data() {
                            return [getDefaultClient().email(email1).build(), getDefaultClient().email(email2).build()];
                        },
                    },
                    giftDraws: {
                        data(dependencies) {
                            return [
                                getDefaultGiftDraw()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[0]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                                getDefaultGiftDraw()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .clientId(toDbId(dependencies.clients()[1]._id))
                                    .wheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [email1, email2];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();

            const result = await wheelsOfFortuneUseCases.getWheelOfFortunePlayerEmails(wheelOfFortuneId, restaurantId);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toIncludeAllMembers(expectedResult);
        });
    });

    describe('deleteWheelOfFortuneById', () => {
        it('should change restaurant wheel of fortune end date to yesterday', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .endDate(tomorrow)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();

            await wheelsOfFortuneUseCases.deleteWheelOfFortuneById(wheelOfFortuneId);
            const deletedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(deletedWheelOfFortune);
            assert(deletedWheelOfFortune.endDate);
            deletedWheelOfFortune.endDate.setHours(0, 0, 0, 0);
            yesterday.setHours(0, 0, 0, 0);
            expect(deletedWheelOfFortune.endDate).toEqual(yesterday);
        });

        it('should change aggregated wheel of fortune end date to yesterday', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().endDate(tomorrow).build()];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();

            await wheelsOfFortuneUseCases.deleteWheelOfFortuneById(wheelOfFortuneId);
            const deletedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(deletedWheelOfFortune);
            assert(deletedWheelOfFortune.endDate);
            deletedWheelOfFortune.endDate.setHours(0, 0, 0, 0);
            yesterday.setHours(0, 0, 0, 0);
            expect(deletedWheelOfFortune.endDate).toEqual(yesterday);
        });

        it('should change restaurant wheel of fortune start date and end date to yesterday', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const in10days = DateTime.now().plus({ day: 10 }).toJSDate();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(tomorrow)
                                    .endDate(in10days)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();

            await wheelsOfFortuneUseCases.deleteWheelOfFortuneById(wheelOfFortuneId);
            const deletedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(deletedWheelOfFortune);
            assert(deletedWheelOfFortune.startDate);
            assert(deletedWheelOfFortune.endDate);
            deletedWheelOfFortune.startDate.setHours(0, 0, 0, 0);
            deletedWheelOfFortune.endDate.setHours(0, 0, 0, 0);
            yesterday.setHours(0, 0, 0, 0);
            expect(deletedWheelOfFortune.startDate).toEqual(yesterday);
            expect(deletedWheelOfFortune.endDate).toEqual(yesterday);
        });

        it('should change aggregated wheel of fortune start date and end date to yesterday', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const in10days = DateTime.now().plus({ day: 10 }).toJSDate();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(tomorrow).endDate(in10days).build()];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();

            await wheelsOfFortuneUseCases.deleteWheelOfFortuneById(wheelOfFortuneId);
            const deletedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(deletedWheelOfFortune);
            assert(deletedWheelOfFortune.startDate);
            assert(deletedWheelOfFortune.endDate);
            deletedWheelOfFortune.startDate.setHours(0, 0, 0, 0);
            deletedWheelOfFortune.endDate.setHours(0, 0, 0, 0);
            yesterday.setHours(0, 0, 0, 0);
            expect(deletedWheelOfFortune.startDate).toEqual(yesterday);
            expect(deletedWheelOfFortune.endDate).toEqual(yesterday);
        });

        it('should change totems redirection when deleting wheel', async () => {
            const nfcsRepository = new NfcsRepository(new PlatformsRepository());
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const instaSocialLink = 'https://instagram-social-link.com/';
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurants' | 'nfcs' | 'platforms'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialLink(instaSocialLink)
                                    .build(),
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .redirectionLink('https://wheeloffortuneredirectionlink.com')
                                    .build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultAggregatedWheelsOfFortune()
                                    .endDate(tomorrow)
                                    .totemIds(dependencies.nfcs().map((nfc) => nfc._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();
            const nfcId = (seededObjects.nfcs[0]._id as DbId).toString();

            await wheelsOfFortuneUseCases.deleteWheelOfFortuneById(wheelOfFortuneId);
            const updatedNfc = await nfcsRepository.findOne({ filter: { _id: nfcId } });
            assert(updatedNfc);
            expect(updatedNfc.redirectionLink).toEqual(instaSocialLink);
        });
    });

    describe('getWheelsOfFortuneGoingLiveTomorrow', () => {
        it('should return an empty array if no wheel of fortune goes live tomorrow', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const in2days = DateTime.now().plus({ day: 2 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(yesterday)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .startDate(in2days)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await wheelsOfFortuneUseCases.getWheelsOfFortuneGoingLiveTomorrow();
            expect(result).toEqual(expectedResult);
        });

        it('should return the wheels of fortune that go live tomorrow', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);

            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234400').build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(tomorrow).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .startDate(tomorrow)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[2]._id)
                                    .startDate(yesterday)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): WheelOfFortuneDto[] {
                    return [
                        {
                            id: dependencies.aggregatedWheelsOfFortune[0]._id.toString(),
                            startDate: tomorrow.toISOString(),
                            endDate: dependencies.aggregatedWheelsOfFortune[0].endDate?.toISOString() || null,
                            restaurants: [
                                {
                                    id: dependencies.restaurants[0]._id.toString(),
                                    name: dependencies.restaurants[0].name,
                                    internalName: dependencies.restaurants[0].internalName,
                                    logo: null,
                                    address: dependencies.restaurants[0].address ?? undefined,
                                    boosterPack: dependencies.restaurants[0].boosterPack
                                        ? {
                                              activated: dependencies.restaurants[0].boosterPack.activated,
                                              activationDate: dependencies.restaurants[0].boosterPack.activationDate?.toISOString() ?? null,
                                          }
                                        : undefined,
                                },
                            ],
                            totemIds: [],
                            gifts: [],
                            parameters: {
                                primaryColor: dependencies.aggregatedWheelsOfFortune[0].parameters.primaryColor,
                                secondaryColor: dependencies.aggregatedWheelsOfFortune[0].parameters.secondaryColor,
                                media: null,
                                giftClaimDurationInDays: dependencies.aggregatedWheelsOfFortune[0].parameters.giftClaimDurationInDays,
                                giftClaimStartDateOption: dependencies.aggregatedWheelsOfFortune[0].parameters.giftClaimStartDateOption,
                                redirectionSettings: dependencies.aggregatedWheelsOfFortune[0].parameters.redirectionSettings,
                            },
                        },
                        {
                            id: dependencies.restaurantWheelsOfFortune[1]._id.toString(),
                            startDate: tomorrow.toISOString(),
                            endDate: dependencies.restaurantWheelsOfFortune[1].endDate?.toISOString() || null,
                            restaurants: [
                                {
                                    id: dependencies.restaurants[1]._id.toString(),
                                    name: dependencies.restaurants[1].name,
                                    internalName: dependencies.restaurants[1].internalName,
                                    logo: null,
                                    address: dependencies.restaurants[1].address ?? undefined,
                                    boosterPack: dependencies.restaurants[1].boosterPack
                                        ? {
                                              activated: dependencies.restaurants[1].boosterPack.activated,
                                              activationDate: dependencies.restaurants[1].boosterPack.activationDate?.toISOString() ?? null,
                                          }
                                        : undefined,
                                },
                            ],
                            totemIds: [],
                            gifts: [],
                            parameters: {
                                primaryColor: dependencies.restaurantWheelsOfFortune[1].parameters.primaryColor,
                                secondaryColor: dependencies.restaurantWheelsOfFortune[1].parameters.secondaryColor,
                                media: null,
                                giftClaimDurationInDays: dependencies.restaurantWheelsOfFortune[1].parameters.giftClaimDurationInDays,
                                giftClaimStartDateOption: dependencies.restaurantWheelsOfFortune[1].parameters.giftClaimStartDateOption,
                                redirectionSettings: dependencies.restaurantWheelsOfFortune[1].parameters.redirectionSettings,
                            },
                        },
                    ];
                },
            });

            await testCase.build();
            const expectedResult = testCase.getExpectedResult();

            const result = await wheelsOfFortuneUseCases.getWheelsOfFortuneGoingLiveTomorrow();
            expect(result).toIncludeAllMembers(expectedResult);
        });
    });

    describe('updateTotemsRedirectionLinks', () => {
        it('should update redirection link to wof for wheels starting in the next hour', async () => {
            const nfcsRepository = new NfcsRepository(new PlatformsRepository());
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const yesterday = DateTime.now().minus({ hours: 1 }).toJSDate();
            const in30minutes = DateTime.now().plus({ minutes: 30 }).toJSDate();
            const nonWofLink = 'https://link.com';

            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants' | 'nfcs'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234409').build(),
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .redirectionLink(nonWofLink)
                                    .chipName('chip1')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .platformKey(PlatformKey.GMB)
                                    .redirectionLink(nonWofLink)
                                    .chipName('chip2')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .redirectionLink(nonWofLink)
                                    .chipName('chip3')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .platformKey(PlatformKey.WEBSITE)
                                    .redirectionLink(nonWofLink)
                                    .chipName('chip4')
                                    .build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultAggregatedWheelsOfFortune().startDate(in30minutes).totemIds([dependencies.nfcs()[0]._id]).build(),
                                getDefaultAggregatedWheelsOfFortune().startDate(yesterday).totemIds([dependencies.nfcs()[2]._id]).build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .startDate(in30minutes)
                                    .totemIds([dependencies.nfcs()[1]._id])
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[1]._id)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .startDate(yesterday)
                                    .totemIds([dependencies.nfcs()[3]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): {
                    updatedWheelsStartingNextHourIds: string[];
                    updateWheelsEndingLastHourIds: string[];
                } {
                    return {
                        updatedWheelsStartingNextHourIds: [
                            dependencies.restaurantWheelsOfFortune[1]._id.toString(),
                            dependencies.aggregatedWheelsOfFortune[0]._id.toString(),
                        ],
                        updateWheelsEndingLastHourIds: [],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();

            const result = await wheelsOfFortuneUseCases.updateTotemsRedirectionLinks();
            expect(result.updatedWheelsStartingNextHourIds.sort()).toEqual(expectedResult.updatedWheelsStartingNextHourIds.sort());
            expect(result.updateWheelsEndingLastHourIds).toEqual(expectedResult.updateWheelsEndingLastHourIds);

            // updated totems
            const restaurantId0 = (seededObjects.restaurants[0]._id as DbId).toString();
            const wheelOfFortune0 = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();
            // eslint-disable-next-line max-len
            const expectedRedirectionLink0 = `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wheelOfFortune0}&restaurantId=${restaurantId0}&isFromTotem=true`;
            const firstUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[0]._id as DbId).toString() },
            });
            assert(firstUpdatedTotem);
            expect(firstUpdatedTotem.redirectionLink).toEqual(expectedRedirectionLink0);

            const restaurantId1 = (seededObjects.restaurants[1]._id as DbId).toString();
            const wheelOfFortune1 = (seededObjects.restaurantWheelsOfFortune[1]._id as DbId).toString();
            // eslint-disable-next-line max-len
            const expectedRedirectionLink1 = `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wheelOfFortune1}&restaurantId=${restaurantId1}&isFromTotem=true`;
            const secondUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[1]._id as DbId).toString() },
            });
            assert(secondUpdatedTotem);
            expect(secondUpdatedTotem.redirectionLink).toEqual(expectedRedirectionLink1);

            // not updated totems
            const thirdUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[2]._id as DbId).toString() },
            });
            assert(thirdUpdatedTotem);
            expect(thirdUpdatedTotem.redirectionLink).toEqual(nonWofLink);

            const fourthUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[3]._id as DbId).toString() },
            });
            assert(fourthUpdatedTotem);
            expect(fourthUpdatedTotem.redirectionLink).toEqual(nonWofLink);
        });

        it('should update redirection link to previous value for wheels that ended last hour', async () => {
            const nfcsRepository = new NfcsRepository(new PlatformsRepository());
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tenMinutesAgo = DateTime.now().minus({ minutes: 10 }).toJSDate();
            const in10minutes = DateTime.now().plus({ minutes: 10 }).toJSDate();
            const wofLink = 'https://wheeloffortunelink.com';
            const gmbSocialId = 'GMB_SOCIAL_ID';
            const instaSocialLink = 'https://instagram-social-link.com/';

            const testCase = new TestCaseBuilderV2<
                'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants' | 'nfcs' | 'platforms'
            >({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234407').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234408').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234409').build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialLink(instaSocialLink)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .key(PlatformKey.GMB)
                                    .socialId(gmbSocialId)
                                    .build(),
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .redirectionLink(wofLink)
                                    .chipName('chip1')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .platformKey(PlatformKey.GMB)
                                    .redirectionLink(wofLink)
                                    .chipName('chip2')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .redirectionLink(wofLink)
                                    .chipName('chip3')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .platformKey(PlatformKey.WEBSITE)
                                    .redirectionLink(wofLink)
                                    .chipName('chip4')
                                    .build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultAggregatedWheelsOfFortune().endDate(tenMinutesAgo).totemIds([dependencies.nfcs()[0]._id]).build(),
                                getDefaultAggregatedWheelsOfFortune().endDate(in10minutes).totemIds([dependencies.nfcs()[2]._id]).build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .endDate(tenMinutesAgo)
                                    .totemIds([dependencies.nfcs()[1]._id])
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[2]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[1]._id)
                                    .endDate(in10minutes)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[3]._id))
                                    .endDate(in10minutes)
                                    .totemIds([dependencies.nfcs()[3]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    return {
                        updatedWheelsStartingNextHourIds: [],
                        updateWheelsEndingLastHourIds: [
                            dependencies.restaurantWheelsOfFortune[1]._id.toString(),
                            dependencies.aggregatedWheelsOfFortune[0]._id.toString(),
                        ],
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();

            const result = await wheelsOfFortuneUseCases.updateTotemsRedirectionLinks();
            expect(result.updatedWheelsStartingNextHourIds).toEqual(expectedResult.updatedWheelsStartingNextHourIds);
            expect(result.updateWheelsEndingLastHourIds.sort()).toEqual(expectedResult.updateWheelsEndingLastHourIds.sort());

            // updated totems
            const firstUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[0]._id as DbId).toString() },
            });
            assert(firstUpdatedTotem);
            expect(firstUpdatedTotem.redirectionLink).toEqual(instaSocialLink);

            const gmbLink = platformsKeys.GMB.externalReviewLink + gmbSocialId;
            const secondUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[1]._id as DbId).toString() },
            });
            assert(secondUpdatedTotem);
            expect(secondUpdatedTotem.redirectionLink).toEqual(gmbLink);

            // not updated totems
            const thirdUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[2]._id as DbId).toString() },
            });
            assert(thirdUpdatedTotem);
            expect(thirdUpdatedTotem.redirectionLink).toEqual(wofLink);

            const fourthUpdatedTotem = await nfcsRepository.findOne({
                filter: { _id: (seededObjects.nfcs[3]._id as DbId).toString() },
            });
            assert(fourthUpdatedTotem);
            expect(fourthUpdatedTotem.redirectionLink).toEqual(wofLink);
        });
    });

    describe('getActiveWheelForRestaurant', () => {
        it('should return null if there is no active wheel', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(tomorrow).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(tomorrow)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return null;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();

            const result = await wheelsOfFortuneUseCases.getActiveWheelForRestaurant(restaurantId);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return restaurant wheel of fortune if active', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(tomorrow).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .startDate(yesterday)
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): WheelOfFortuneDto {
                    return {
                        id: dependencies.restaurantWheelsOfFortune[0]._id.toString(),
                        startDate: yesterday.toISOString(),
                        endDate: dependencies.restaurantWheelsOfFortune[0].endDate?.toISOString() || null,
                        restaurants: [
                            {
                                id: dependencies.restaurants[0]._id.toString(),
                                name: dependencies.restaurants[0].name,
                                internalName: dependencies.restaurants[0].internalName,
                                logo: null,
                                address: dependencies.restaurants[0].address ?? undefined,
                                boosterPack: dependencies.restaurants[0].boosterPack
                                    ? {
                                          activated: dependencies.restaurants[0].boosterPack.activated,
                                          activationDate: dependencies.restaurants[0].boosterPack.activationDate?.toISOString() ?? null,
                                      }
                                    : undefined,
                            },
                        ],
                        totemIds: dependencies.restaurantWheelsOfFortune[0].totemIds.map((id) => id.toString()),
                        gifts: [],
                        parameters: {
                            primaryColor: dependencies.restaurantWheelsOfFortune[0].parameters.primaryColor,
                            secondaryColor: dependencies.restaurantWheelsOfFortune[0].parameters.secondaryColor,
                            media: null,
                            giftClaimDurationInDays: dependencies.restaurantWheelsOfFortune[0].parameters.giftClaimDurationInDays,
                            giftClaimStartDateOption: dependencies.restaurantWheelsOfFortune[0].parameters.giftClaimStartDateOption,
                            redirectionSettings: dependencies.restaurantWheelsOfFortune[0].parameters.redirectionSettings,
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();

            const result = await wheelsOfFortuneUseCases.getActiveWheelForRestaurant(restaurantId);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return aggregated wheel of fortune if active and restaurant wheel of fortune is inactive', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234338').build()];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(yesterday).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): WheelOfFortuneDto {
                    return {
                        id: dependencies.aggregatedWheelsOfFortune[0]._id.toString(),
                        startDate: yesterday.toISOString(),
                        endDate: dependencies.aggregatedWheelsOfFortune[0].endDate?.toISOString() || null,
                        restaurants: [
                            {
                                id: dependencies.restaurants[0]._id.toString(),
                                name: dependencies.restaurants[0].name,
                                internalName: dependencies.restaurants[0].internalName,
                                logo: null,
                                address: dependencies.restaurants[0].address ?? undefined,
                                boosterPack: dependencies.restaurants[0].boosterPack
                                    ? {
                                          activated: dependencies.restaurants[0].boosterPack.activated,
                                          activationDate: dependencies.restaurants[0].boosterPack.activationDate?.toISOString() ?? null,
                                      }
                                    : undefined,
                            },
                        ],
                        totemIds: dependencies.aggregatedWheelsOfFortune[0].totemIds.map((id) => id.toString()),
                        gifts: [],
                        parameters: {
                            primaryColor: dependencies.aggregatedWheelsOfFortune[0].parameters.primaryColor,
                            secondaryColor: dependencies.aggregatedWheelsOfFortune[0].parameters.secondaryColor,
                            media: null,
                            giftClaimDurationInDays: dependencies.aggregatedWheelsOfFortune[0].parameters.giftClaimDurationInDays,
                            giftClaimStartDateOption: dependencies.aggregatedWheelsOfFortune[0].parameters.giftClaimStartDateOption,
                            redirectionSettings: dependencies.aggregatedWheelsOfFortune[0].parameters.redirectionSettings,
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();

            const result = await wheelsOfFortuneUseCases.getActiveWheelForRestaurant(restaurantId);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });
    });

    describe('updateTotemsForWheelOfFortune', () => {
        it('should update totemIds for restaurant wheel of fortune', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'restaurantWheelsOfFortune' | 'restaurants' | 'nfcs'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234338').build()];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .totemIds([dependencies.nfcs()[0]._id])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.restaurantWheelsOfFortune[0]._id as DbId).toString();
            const totemIds = [
                (seededObjects.nfcs[0]._id as DbId).toString(),
                (seededObjects.nfcs[1]._id as DbId).toString(),
                (seededObjects.nfcs[2]._id as DbId).toString(),
            ];

            await wheelsOfFortuneUseCases.updateTotemsForWheelOfFortune(wheelOfFortuneId, totemIds);

            const updatedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(updatedWheelOfFortune);
            expect(updatedWheelOfFortune.totemIds.map((id) => id.toString())).toEqual(totemIds);
        });

        it('should update totemIds for aggregated wheel of fortune', async () => {
            const wheelsOfFortuneRepository = new WheelsOfFortuneRepository();
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants' | 'nfcs'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234338').build()];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                                getDefaultTotem().restaurantId(toDbId(dependencies.restaurants()[0]._id)).build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultAggregatedWheelsOfFortune().totemIds([dependencies.nfcs()[0]._id]).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(toDbId(dependencies.aggregatedWheelsOfFortune()[0]._id))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();
            const totemIds: string[] = [];

            await wheelsOfFortuneUseCases.updateTotemsForWheelOfFortune(wheelOfFortuneId, totemIds);

            const updatedWheelOfFortune = await wheelsOfFortuneRepository.findOne({ filter: { _id: wheelOfFortuneId } });
            assert(updatedWheelOfFortune);
            expect(updatedWheelOfFortune.totemIds.map((id) => id.toString())).toEqual(totemIds);
        });
    });

    describe('addWheelOfFortuneTotemsRedirection', () => {
        it('should create and change totems redirection link to wheel of fortune links', async () => {
            const nfcsRepository = new NfcsRepository(new PlatformsRepository());
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'aggregatedWheelsOfFortune' | 'restaurantWheelsOfFortune' | 'restaurants' | 'nfcs'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.GMB)
                                    .redirectionLink('https://gmbredirectionlink.com')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .redirectionLink('https://instagramredirectionlink.com')
                                    .build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const wheelOfFortuneId = (seededObjects.aggregatedWheelsOfFortune[0]._id as DbId).toString();
            const totemIds = seededObjects.nfcs.map((nfc) => (nfc._id as DbId).toString());

            await wheelsOfFortuneUseCases.addWheelOfFortuneTotemsRedirection(wheelOfFortuneId, totemIds);

            const restaurantId0 = (seededObjects.nfcs[0].restaurantId as DbId).toString();
            // eslint-disable-next-line max-len
            const expectedRedirectionLink0 = `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wheelOfFortuneId}&restaurantId=${restaurantId0}&isFromTotem=true`;
            const firstUpdatedTotem = await nfcsRepository.findOne({ filter: { _id: totemIds[0] } });
            assert(firstUpdatedTotem);
            expect(firstUpdatedTotem.redirectionLink).toEqual(expectedRedirectionLink0);

            const restaurantId1 = (seededObjects.nfcs[1].restaurantId as DbId).toString();
            // eslint-disable-next-line max-len
            const expectedRedirectionLink1 = `${process.env.BASE_URL}/wheel-of-fortune?wofId=${wheelOfFortuneId}&restaurantId=${restaurantId1}&isFromTotem=true`;
            const secondUpdatedTotem = await nfcsRepository.findOne({ filter: { _id: totemIds[1] } });
            assert(secondUpdatedTotem);
            expect(secondUpdatedTotem.redirectionLink).toEqual(expectedRedirectionLink1);
        });
    });

    describe('removeWheelOfFortuneTotemsRedirection', () => {
        it('should create and change totems redirection link to nfc links depending on platforms', async () => {
            const nfcsRepository = new NfcsRepository(new PlatformsRepository());
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const websiteLink = 'https://website.com';
            const gmbSocialId = 'GMB_SOCIAL_ID';
            const instaSocialLink = 'https://instagram-social-link.com/';
            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'nfcs'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                getDefaultPlatform()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .key(PlatformKey.GMB)
                                    .website(websiteLink)
                                    .socialId(gmbSocialId)
                                    .build(),
                                getDefaultPlatform()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .key(PlatformKey.INSTAGRAM)
                                    .socialLink(instaSocialLink)
                                    .build(),
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return [
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.GMB)
                                    .redirectionLink('https://wheeloffortuneredirectionlink.com')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[1]._id))
                                    .platformKey(PlatformKey.INSTAGRAM)
                                    .redirectionLink('https://wheeloffortuneredirectionlink.com')
                                    .build(),
                                getDefaultTotem()
                                    .restaurantId(toDbId(dependencies.restaurants()[0]._id))
                                    .platformKey(PlatformKey.WEBSITE)
                                    .redirectionLink('https://wheeloffortuneredirectionlink.com')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const totemIds = seededObjects.nfcs.map((nfc) => (nfc._id as DbId).toString());

            await wheelsOfFortuneUseCases.removeWheelOfFortuneTotemsRedirection(totemIds);

            const gmbLink = platformsKeys.GMB.externalReviewLink + gmbSocialId;
            const firstUpdatedTotem = await nfcsRepository.findOne({ filter: { _id: totemIds[0] } });
            expect(firstUpdatedTotem?.redirectionLink).toEqual(gmbLink);

            const secondUpdatedTotem = await nfcsRepository.findOne({ filter: { _id: totemIds[1] } });
            expect(secondUpdatedTotem?.redirectionLink).toEqual(instaSocialLink);

            const thirdUpdatedTotem = await nfcsRepository.findOne({ filter: { _id: totemIds[2] } });
            expect(thirdUpdatedTotem?.redirectionLink).toEqual(websiteLink);
        });
    });

    describe('haveAtLeastOneWheelOfFortune', () => {
        it('should return true if one of the restaurants has a wheel of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [getDefaultRestaurantWheelsOfFortune().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
                expectedResult() {
                    return true;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.haveAtLeastOneWheelOfFortune(restaurantIds);
            expect(result).toEqual(expectedResult);
        });

        it('should return false if none of the restaurants have a wheel of fortune', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return false;
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.haveAtLeastOneWheelOfFortune(restaurantIds);
            expect(result).toEqual(expectedResult);
        });
    });

    describe('getWheelOfFortuneStatusForUser', () => {
        it('should return correct status if user has no wheel', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'userRestaurants'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        hasAtLeastOneActiveOrProgrammedWheel: false,
                        hasCreatedAWheel: false,
                        firstWheelCreatedAt: null,
                        latestEndDateForActiveWheels: null,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getWheelOfFortuneStatusForUser(restaurantIds);
            expect(result).toEqual(expectedResult);
        });

        it('should return correct status if user has no active wheel', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const today = new Date();
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'userRestaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(today)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        hasAtLeastOneActiveOrProgrammedWheel: false,
                        hasCreatedAWheel: true,
                        firstWheelCreatedAt: today,
                        latestEndDateForActiveWheels: null,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getWheelOfFortuneStatusForUser(restaurantIds);
            expect(result).toEqual(expectedResult);
        });

        it('should return correct status if user has active wheels with set end date', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const today = new Date();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'userRestaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(today)
                                    .startDate(yesterday)
                                    .endDate(tomorrow)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        hasAtLeastOneActiveOrProgrammedWheel: true,
                        hasCreatedAWheel: true,
                        firstWheelCreatedAt: today,
                        latestEndDateForActiveWheels: tomorrow,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getWheelOfFortuneStatusForUser(restaurantIds);
            expect(result).toEqual(expectedResult);
        });

        it('should return correct status if user has active wheels with no end date', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const today = new Date();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'users' | 'restaurants' | 'userRestaurants' | 'restaurantWheelsOfFortune'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(today)
                                    .startDate(yesterday)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        hasAtLeastOneActiveOrProgrammedWheel: true,
                        hasCreatedAWheel: true,
                        firstWheelCreatedAt: today,
                        latestEndDateForActiveWheels: new Date('12/12/2124'),
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getWheelOfFortuneStatusForUser(restaurantIds);
            expect(result).toEqual(expectedResult);
        });

        it('should return correct status if user has active aggregated wheels', async () => {
            const wheelsOfFortuneUseCases = container.resolve(WheelsOfFortuneUseCases);
            const today = new Date();
            const yesterday = DateTime.now().minus({ day: 1 }).toJSDate();
            const tomorrow = DateTime.now().plus({ day: 1 }).toJSDate();
            const testCase = new TestCaseBuilderV2<
                'users' | 'restaurants' | 'userRestaurants' | 'restaurantWheelsOfFortune' | 'aggregatedWheelsOfFortune'
            >({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234338').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234339').build(),
                            ];
                        },
                    },
                    userRestaurants: {
                        data(dependencies) {
                            return [
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                                getDefaultUserRestaurant()
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .userId(dependencies.users()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    aggregatedWheelsOfFortune: {
                        data() {
                            return [getDefaultAggregatedWheelsOfFortune().startDate(yesterday).endDate(tomorrow).build()];
                        },
                    },
                    restaurantWheelsOfFortune: {
                        data(dependencies) {
                            return [
                                getDefaultRestaurantWheelsOfFortune()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(today)
                                    .aggregatedWheelOfFortuneId(dependencies.aggregatedWheelsOfFortune()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        hasAtLeastOneActiveOrProgrammedWheel: true,
                        hasCreatedAWheel: true,
                        firstWheelCreatedAt: today,
                        latestEndDateForActiveWheels: tomorrow,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const expectedResult = testCase.getExpectedResult();
            const restaurantIds = seededObjects.restaurants.map((restaurant) => (restaurant._id as DbId).toString());

            const result = await wheelsOfFortuneUseCases.getWheelOfFortuneStatusForUser(restaurantIds);
            expect(result).toEqual(expectedResult);
        });
    });
});
