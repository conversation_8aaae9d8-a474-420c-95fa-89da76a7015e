import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { IPlatform, IReview, IReviewComment } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, platformsKeys, TimeInMilliseconds, UbereatsPromotionValue, waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { getDateRange } from ':helpers/utils';
import { fetchUntilWorks } from ':microservices/node-crawler';
import UberEatsCredentialsRepository from ':modules/credentials/platforms/ubereats/ubereats.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewMapper } from ':modules/reviews/reviews.mapper';
import { reviewsFetchCounter, reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import {
    PlatformReplyPayload,
    PlatformReviewsUseCases,
    ReviewInputWithRestaurantAndPlatformIds,
    UbereatsReplyPayload,
} from ':modules/reviews/reviews.types';
import { UberEatsGraphQLHeaders } from ':providers/ubereats/ubereats.manager-api.provider.interfaces';

import { UbereatsAppVariant, UbereatsGetReviewsRequest, UbereatsOperationName, UbereatsReplyReviewRequest } from './interface';

@singleton()
export default class UbereatsReviewsUseCases implements PlatformReviewsUseCases {
    private readonly TIMEOUT_BEFORE_RETRY = 60 * TimeInMilliseconds.SECOND;
    private readonly MAX_RETRY_COUNT = 2;

    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _ubereatsCredentialsRepository: UberEatsCredentialsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async getReviewsData(
        { socialId }: { socialId?: string },
        recentOnly?: boolean
    ): Promise<any[] | { error: boolean; message: MalouErrorCode; errorData: any }> {
        assert(socialId, 'Missing socialId');
        let reviews = [];
        try {
            // max limit for ubereats gql api is 50 reviews
            const reviewsCountToFetch = recentOnly ? 100 : 200;
            const chunkCount = reviewsCountToFetch / 50;
            const dateRange = getDateRange(new Date(), platformsKeys.UBEREATS.maxReviewsDaysInterval);
            const platformsSocialIds = [socialId];
            let lastWorkflowUuid = null;
            let lastTimestamp = null;
            const headers = await this._getGqlHeaders();
            for (let i = 0; i < chunkCount; i++) {
                const body = this._getGqlReviewsRequest(platformsSocialIds, dateRange, lastWorkflowUuid, lastTimestamp);

                const response = await this._makeGqlRequest({
                    headers,
                    body,
                    handleResponse: (res) => res?.data,
                });

                const {
                    data: { eaterReviews },
                } = response;

                if (eaterReviews === undefined) {
                    throw new Error(JSON.stringify(response));
                }

                if (!eaterReviews?.length) {
                    reviewsFetchCounter.add(1, { source: PlatformKey.UBEREATS, status: 'success' });
                    return reviews;
                }

                reviews = reviews.concat(eaterReviews);

                const lastChunkReview = eaterReviews[eaterReviews.length - 1];
                lastWorkflowUuid = lastChunkReview.order.workflowUUID;
                lastTimestamp = lastChunkReview.timestamp;
            }

            reviewsFetchCounter.add(1, { source: PlatformKey.UBEREATS, status: 'success' });
            return reviews;
        } catch (error) {
            if (reviews.length > 0) {
                reviewsFetchCounter.add(1, { source: PlatformKey.UBEREATS, status: 'success' });
                return reviews;
            }
            logger.warn('[UBEREATS_REVIEWS_FETCH_ERROR] : ', { error, socialId });
            reviewsFetchCounter.add(1, { source: PlatformKey.UBEREATS, status: 'failure' });
            return { error: true, message: MalouErrorCode.PLATFORM_UBEREATS_REVIEWS_FETCH_ERROR, errorData: error };
        }
    }

    async reply({ review, comment }: { review: IReview; comment: PlatformReplyPayload }): Promise<any> {
        try {
            const ubereatsComment = comment as UbereatsReplyPayload;
            const platform = await this._platformsRepository.findOne({
                filter: { _id: review.platformId },
                projection: { socialId: 1 },
                options: { lean: true },
            });

            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: {
                        platformId: review.platformId,
                    },
                });
            }

            const reviewerSocialId = review.reviewer?.socialId;
            if (!reviewerSocialId) {
                throw new MalouError(MalouErrorCode.NOT_FOUND, {
                    metadata: {
                        platformId: review.platformId,
                        review,
                    },
                });
            }

            const reviewSocialId = review.socialId;
            const { socialId: platformSocialId } = platform;

            const headers = await this._getGqlHeaders(reviewSocialId);
            const body = this._getGqlReviewReplyRequest(
                platformSocialId!,
                reviewSocialId,
                ubereatsComment.comment,
                reviewerSocialId,
                ubereatsComment.ubereatsPromotionValue
            );

            const response = await this._makeGqlRequest({
                headers,
                body,
                handleResponse: (res) => res?.data?.submitEaterReviewReply,
            });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.UBEREATS,
                status: 'success',
            });
            return response?.data?.submitEaterReviewReply;
        } catch (error) {
            logger.warn('[UBEREATS_REPLY_ERROR] : ', { error, platformId: review.platformId, reviewId: review._id });
            // UberEats replies have retries in place, so we don't need to send an alert here
            throw error;
        }
    }

    pushReviewComment = ({ socialId, key, comment }: { socialId: string; key: string; comment: IReviewComment }) =>
        this._reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    updateComment() {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, { message: 'UbereatsReviewsUseCases does not implement updateComment' });
    }

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'UbereatsReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    private _getGqlHeaders = async (reviewSocialId: string | null = null): Promise<UberEatsGraphQLHeaders> => {
        const credentials = await this._ubereatsCredentialsRepository.findOne({
            filter: { key: 'ubereats-malou' },
            options: { lean: true },
        });
        if (!credentials || !credentials.cookie) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                metadata: { reviewSocialId, platform: PlatformKey.UBEREATS },
            });
        }

        const referer = reviewSocialId
            ? `https://merchants.ubereats.com/manager/home/<USER>/feedback/reviews/${reviewSocialId}`
            : `https://merchants.ubereats.com/manager/home/<USER>/feedback/reviews`;

        return {
            ...Config.platforms.ubereats.api.headers,
            cookie: credentials.cookie,
            referer,
        };
    };

    private _getGqlReviewsRequest = (
        platformsSocialIds: string[],
        dateRange: { start: string; end: string },
        lastWorkflowUuid: string | null,
        lastTimestamp: string | null,
        limit: number = 50
    ): UbereatsGetReviewsRequest => ({
        operationName: UbereatsOperationName.EATER_REVIEWS,
        query: `
            fragment eaterReview on EaterReview {
                uuid
                timestamp
                rating
                comment
                tags
                menuItemReviews {
                    id
                    rating
                    name
                    comment
                    tags
                    __typename
                }
                eater {
                    uuid
                    name
                    profileURL
                    __typename
                }
                eaterTotalOrders
                order {
                    workflowUUID
                    deliveredAt
                    orderTotal
                    currencyCode
                    appVariant
                    restaurant {
                        uuid
                        name
                        __typename
                    }
                    __typename
                }
                isReplyScheduled
                reply {
                    comment
                    timestamp
                    userUUID
                    uuid
                    promotion {
                        uuid
                        flatValue
                        __typename
                    }
                    __typename
                }
                __typename
            }

            query EaterReviews( $restaurantUUIDs: [ID!]!, $limit: Int, $lastTimestamp: String, $lastWorkflowUUID: String, $filters: EaterReviewFilterInput) {
                eaterReviews( restaurantUUIDs: $restaurantUUIDs, limit: $limit, lastTimestamp: $lastTimestamp, lastWorkflowUUID: $lastWorkflowUUID, filters: $filters) {
                    ...eaterReview
                    __typename
                }
            }
        `,
        variables: {
            restaurantUUIDs: platformsSocialIds,
            lastTimestamp,
            lastWorkflowUUID: lastWorkflowUuid,
            filters: {
                starRating: null,
                tagsValue: null,
                dateRange,
            },
            limit,
        },
    });

    private _getGqlReviewReplyRequest = (
        restaurantUUID: string,
        workflowUUID: string,
        comment: string,
        eaterUUID: string,
        ubereatsPromotionValue?: UbereatsPromotionValue
    ): UbereatsReplyReviewRequest => ({
        operationName: UbereatsOperationName.SUBMIT_REPLY,
        variables: {
            workflowUUID,
            restaurantUUID,
            comment,
            promotionValue: ubereatsPromotionValue ?? UbereatsPromotionValue.NONE,
            eaterUUID,
            appVariant: UbereatsAppVariant.UBEREATS,
        },
        query: `
        mutation SubmitReply($workflowUUID: String!, $restaurantUUID: String!, $comment: String!, $promotionValue: PromotionValue, $eaterUUID: String!, $appVariant: String!) {
            submitEaterReviewReply(
                reply: {
                    workflowUUID: $workflowUUID, restaurantUUID: $restaurantUUID, comment: $comment, promotionValue: $promotionValue, eaterUUID: $eaterUUID, appVariant: $appVariant
                }) {
                    uuid
                    userUUID
                    timestamp
                    comment
                    promotion {
                        uuid
                        flatValue
                        formattedValue
                        __typename
                    }
                    __typename
                }
            }
    `,
    });

    private async _makeGqlRequest({
        headers,
        body,
        handleResponse,
        tries = 0,
    }: {
        headers: Object;
        body: Object;
        handleResponse: (res: any) => boolean;
        tries?: number;
    }) {
        const response = await fetchUntilWorks({
            params: {
                url: Config.platforms.ubereats.api.graphQlUrl,
                method: 'POST',
                headers,
                body,
            },
            isResponseValid: handleResponse,
            retries: 1,
        });

        if (response?.status === 'failure' && response.data?.message === 'error.too_many_requests') {
            if (tries >= this.MAX_RETRY_COUNT) {
                return response;
            }

            logger.warn('[UBEREATS_TOO_MANY_REQUESTS] - Waiting before retrying');
            await waitFor(this.TIMEOUT_BEFORE_RETRY);
            return this._makeGqlRequest({ headers, body, handleResponse, tries: tries + 1 });
        }

        return response;
    }
}
