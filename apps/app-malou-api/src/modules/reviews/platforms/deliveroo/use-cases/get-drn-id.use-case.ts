import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GetDeliverooSessionService } from ':modules/credentials/platforms/deliveroo/services/get-deliveroo-session.service';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class GetDeliverooDrnIdUseCase {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _getDeliverooSessionService: GetDeliverooSessionService,
        private readonly _slackService: SlackService
    ) {}

    async execute(restaurantId: string): Promise<string> {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.DELIVEROO);
        if (!platform) {
            logger.error('Deliveroo platform not found', { restaurantId });
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, { metadata: { restaurantId } });
        }
        const platformDrnId = platform.drnId;
        if (platformDrnId) {
            return platformDrnId;
        }

        const { credentials, socialId } = platform;
        if (!credentials?.length || !socialId) {
            logger.error('Deliveroo platform has no credentials or socialId', { restaurantId, platformId: platform._id.toString() });
            throw new MalouError(MalouErrorCode.CREDENTIALS_DELIVEROO_NOT_FOUND, { metadata: { restaurantId } });
        }
        const deliverooSession = await this._getDeliverooSessionService.execute(credentials);
        if (!deliverooSession.isSessionValid(socialId)) {
            logger.error('Deliveroo session is not valid', { restaurantId, socialId });
            throw new MalouError(MalouErrorCode.DELIVEROO_CREDENTIALS_EXPIRED, { metadata: { restaurantId } });
        }
        const deliverooRestaurant = deliverooSession.getRestaurantById(socialId);
        const drnId = deliverooRestaurant?.drnId;
        if (!drnId) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_DELIVEROO_DRN_ID_NOT_FOUND, { metadata: { restaurantId } });

            await this._sendErrorToSlack({ restaurantId });
            throw malouError;
        }
        await this._platformsRepository.findOneAndUpdate({
            filter: { restaurantId, platformKey: PlatformKey.DELIVEROO },
            update: { drnId },
        });
        return drnId;
    }

    private async _sendErrorToSlack({ restaurantId }: { restaurantId: string }): Promise<void> {
        const text = await this._formatSlackMessage({ restaurantId });
        this._slackService.sendMessage({
            text,
            // todo: add another channel for this one
            channel: SlackChannel.PLATFORM_UPDATES_ALERTS,
        });
    }

    private async _formatSlackMessage({ restaurantId }: { restaurantId: string }): Promise<string> {
        const context = await this._slackService.createContextForSlack({ restaurantId });

        return `:deliveroo: Cannot retrieve drnId to fetch *Deliveroo* public reviews${context}`;
    }
}
