import { sampleSize } from 'lodash';
import assert from 'node:assert';
import { autoInjectable, delay, inject } from 'tsyringe';

import { IPlatform, IReview } from '@malou-io/package-models';
import { MalouErrorCode, PlatformKey, PostedStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import * as platformsScrapper from ':microservices/platforms-scrapper';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformHeaderConfigOptions, replyToReview } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { SlackChannel, SlackService } from ':services/slack.service';

import { ReviewMapper } from '../../reviews.mapper';
import { ReviewsRepository } from '../../reviews.repository';
import { PlatformReplyPayload, PlatformReviewsUseCases, ReviewInputWithRestaurantAndPlatformIds } from '../../reviews.types';
import { extractBusinessId, TripadvisorReply, TripadvisorReplyPayload } from './tripadvisor-review-mapper';

export const MAX_TRIPADVISOR_REPLY_RETRIES = 20;

@autoInjectable()
export default class TripadvisorReviewsUseCases implements PlatformReviewsUseCases {
    constructor(
        @inject(delay(() => ReviewsRepository)) private readonly reviewsRepository: ReviewsRepository,
        @inject(SlackService) private readonly _slackService: SlackService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async getReviewsData({ restaurantId }: { restaurantId?: string }, recentOnly?: boolean) {
        assert(restaurantId, 'Missing restaurantId');
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.TRIPADVISOR);

        if (!platform) {
            return { error: true, message: MalouErrorCode.PLATFORM_NOT_FOUND };
        }
        const validUrlRegexes = [
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Restaurant_Review-/,
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Hotel_Review-/,
            /https:\/\/www\.tripadvisor\.[a-z]{2,3}\/Attraction_Review-/, // Used for activities such as spa, etc.
        ];
        if (!platform.socialLink || validUrlRegexes.every((regex) => !platform.socialLink?.match(regex))) {
            return { error: true, message: MalouErrorCode.REVIEW_INCORRECT_SOCIAL_LINK, errorData: platform.socialLink };
        }
        const params = {
            endpoint: platform.socialLink.replace(/https:\/\/www\.tripadvisor\.[a-z]{2,3}/, ''),
            recentOnly: !!recentOnly,
        };

        await platformsScrapper.reviews(PlatformKey.TRIPADVISOR, restaurantId.toString(), params, false);
        return { sentToSQS: true, socialId: platform.socialId };
    }

    mapReviewsDataToMalou = function (platform: IPlatform, reviewsData): ReviewInputWithRestaurantAndPlatformIds[] {
        return reviewsData.map((review) => ReviewMapper.mapToMalouReview(platform, review));
    };

    /**
     * Update Reviews in malou database with new review reply
     * @param {Object} comment
     * @param {Object} comment.review
     * @param {string} comment.comment
     */

    async reply({
        review,
        comment,
        headerConfig,
        retryReplyingToOtherReviews = true,
    }: {
        review: IReview;
        comment: PlatformReplyPayload;
        headerConfig?: PlatformHeaderConfigOptions;
        retryReplyingToOtherReviews?: boolean;
    }): Promise<TripadvisorReply & ({} | { error: any; review: IReview })> {
        const tripadvisorComment = comment as TripadvisorReplyPayload;
        let locationId: number | null = null;
        try {
            assert(review.socialLink, 'Missing socialLink on review');
            locationId = extractBusinessId(review.socialLink);
            await replyToReview(PlatformKey.TRIPADVISOR, { review, comment, locationId }, { headerConfig });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'success',
            });
        } catch (error) {
            logger.warn('[ERROR_REPLY_REVIEW_TRIPADVISOR] Error when replying to review, set comment to RETRY', {
                review,
                comment,
                locationId,
                error,
            });
            return { comment: tripadvisorComment.comment, posted: PostedStatus.RETRY, error, review };
        }
        if (retryReplyingToOtherReviews) {
            await this.retryReplyingSomeReviews(headerConfig);
        }
        return { comment: tripadvisorComment.comment, posted: PostedStatus.PENDING };
    }

    pushReviewComment = ({ socialId, key, comment }) => this.reviewsRepository.updateUniqueReviewComment({ socialId, key, comment });

    updateComment = async function () {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.TRIPADVISOR,
            },
        });
    };

    async fetchTotalReviewCount(_restaurantId: string): Promise<number> {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            message: 'TripadvisorReviewsUseCases does not implement fetchTotalReviewCount',
        });
    }

    retryReplyingSomeReviews = async (headerConfig?: PlatformHeaderConfigOptions, reviewsSampleSize = 10): Promise<void> => {
        const reviews = await this.reviewsRepository.getReviewsWithCommentsToRetryReplying(PlatformKey.TRIPADVISOR);
        if (!reviews.length) {
            return;
        }
        const reviewsSample = sampleSize(reviews, reviewsSampleSize);
        const promises = reviewsSample.map(async (review) => this._updateReviewComment(review, headerConfig)).filter(Boolean);
        const results = await Promise.all(promises);
        if (results.length) {
            logger.info(`[RETRY_REPLYING_REVIEWS_TRIPADVISOR] ${results.length} reviews updated`, { results });
        }
        return;
    };

    private _updateReviewComment = async (review: IReview, headerConfig?: PlatformHeaderConfigOptions): Promise<IReview | undefined> => {
        const commentToRetryPosting = review.comments.find((comment) => comment.posted === PostedStatus.RETRY);
        if (!commentToRetryPosting) {
            return;
        }
        const result = await this.reply({
            review,
            comment: { comment: commentToRetryPosting.text },
            headerConfig,
            retryReplyingToOtherReviews: false,
        });
        const currentRetries = commentToRetryPosting.retries ?? 0;
        const retries = result.posted === PostedStatus.RETRY ? currentRetries + 1 : currentRetries;
        const posted = retries > MAX_TRIPADVISOR_REPLY_RETRIES ? PostedStatus.REJECTED : result.posted;

        if (result.posted === PostedStatus.RETRY && posted === PostedStatus.REJECTED) {
            const { name } = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: review.restaurantId },
                options: { lean: true },
                projection: { name: 1 },
            });
            this._slackService.sendAlert({
                channel: SlackChannel.REVIEWS_ALERTS,
                data: {
                    err: new MalouError(MalouErrorCode.PLATFORM_PUBLISH_ERROR, {
                        message: 'Could not publish reply to Tripadvisor review',
                        metadata: { socialId: review.socialId },
                    }),
                    endpoint: `restaurants/${review.restaurantId.toString()}/reputation/reviews?reviewId=${review._id.toString()}`,
                    metadata: {
                        description: `Reply could not be published to Tripadvisor for review with 
                        socialId ${review.socialId}`,
                        restaurantName: name,
                    },
                },
            });
            reviewsReplyCounter.add(1, {
                source: PlatformKey.TRIPADVISOR,
                status: 'failure',
            });
        }

        return this.reviewsRepository.updateReviewCommentStatus({
            commentId: commentToRetryPosting._id,
            posted,
            retries,
        });
    };
}
