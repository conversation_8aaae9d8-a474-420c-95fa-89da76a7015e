import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { IReviewComment, IUserWithProfilePicture } from '@malou-io/package-models';
import { errorReplacer, MalouErrorCode, PostedStatus, RETRY_REPLY_MAX_FAILED_ATTEMPTS } from '@malou-io/package-utils';

import { defaultComputeRetryDelayInMinutes, GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { retryReplyToReviewValidator } from ':helpers/validators/jobs/reviews-jobs.validator';
import { PlatformHeaderConfigOptions } from ':modules/providers/use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { SlackChannel, SlackService } from ':services/slack.service';

type DataAttributes = z.infer<typeof retryReplyToReviewValidator>;

@singleton()
export class RetryReplyToReviewJob extends GenericJobDefinition {
    constructor(
        private readonly _reviewsUseCases: ReviewsUseCases,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.RETRY_REPLY_TO_REVIEW,
            retryStrategy: {
                maxAttemptsCount: RETRY_REPLY_MAX_FAILED_ATTEMPTS,
                computeRetryDelayInMinutes: defaultComputeRetryDelayInMinutes,
                executeAfterMaxAttemptsCount: async (err: Error, job: Job<DataAttributes>) => {
                    logger.error('[RETRY_REPLY_TO_REVIEW_MAX_ATTEMPTS_REACHED] ', { err, job: job.attrs });
                    const {
                        commentId,
                        args: { reviewId },
                    } = job.attrs.data!;
                    await this._reviewsRepository.updateReviewCommentStatus({ commentId, posted: PostedStatus.REJECTED });
                    const review = await this._reviewsRepository.findOne({
                        filter: { _id: reviewId },
                        projection: { key: 1, restaurantId: 1 },
                        options: { lean: true },
                    });
                    if (!review) {
                        return;
                    }
                    reviewsReplyCounter.add(1, {
                        source: review?.key,
                        status: 'failure',
                    });
                    const restaurant = await this._restaurantsRepository.findOne({
                        filter: { _id: review.restaurantId.toString() },
                        projection: { name: 1 },
                        options: { lean: true },
                    });
                    if (!restaurant) {
                        return;
                    }
                    this._slackService.sendAlert({
                        channel: SlackChannel.REVIEWS_ALERTS,
                        data: {
                            err: new MalouError(MalouErrorCode.PUBLISH_REVIEW_ERROR, {
                                message: err.message ?? JSON.stringify(err, errorReplacer),
                                metadata: { rawError: err },
                            }),
                            metadata: {
                                description: `[RETRY_REPLY] Last retry reply to review failed for review ${reviewId}, 
                                restaurant ${restaurant._id}, platformKey: ${review.key}`,
                                restaurantName: restaurant.name,
                            },
                        },
                    });
                },
            },
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const data = retryReplyToReviewValidator.parse(job.attrs.data);
        const commentId = data.commentId;
        const reviewId = data.args.reviewId;
        const restaurantId = data.args.restaurantId;
        const args = { ...data.args, reviewId, restaurantId } as {
            user: IUserWithProfilePicture;
            reviewId: string;
            comment: Partial<IReviewComment>;
            restaurantId: string;
            headerConfig: PlatformHeaderConfigOptions;
        };

        const review = await this._reviewsRepository.getReviewById(reviewId);

        if (!review) {
            logger.warn('Review does not exists', { reviewId });
            return;
        }

        if (!review.comments?.find((comment) => comment._id.toString() === commentId.toString())) {
            logger.warn('Comment does not exist', { commentId });
            return;
        }

        await this._reviewsUseCases.reply({ ...args, isFromJob: true });
        // remove the pending comment
        await this._reviewsUseCases.removeComment(reviewId, commentId);
    }
}
