import { Job } from 'agenda';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { AUTO_REPLY_MAX_FAILED_ATTEMPTS, errorReplacer, MalouErrorCode } from '@malou-io/package-utils';

import { defaultComputeRetryDelayInMinutes, GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { autoReplyToReviewValidator } from ':helpers/validators/jobs/reviews-jobs.validator';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { reviewsReplyCounter } from ':modules/reviews/reviews.metrics';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { SlackChannel, SlackService } from ':services/slack.service';

type DataAttributes = z.infer<typeof autoReplyToReviewValidator>;

@singleton()
export class AutoReplyToReviewJob extends GenericJobDefinition {
    constructor(
        private readonly _reviewsUseCases: ReviewsUseCases,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.AUTO_REPLY_TO_REVIEW,
            shouldDeleteJobOnSuccess: true,
            retryStrategy: {
                maxAttemptsCount: AUTO_REPLY_MAX_FAILED_ATTEMPTS,
                executeAfterMaxAttemptsCount: async (err: Error, job: Job<DataAttributes>) => {
                    return this._sendSlackAlertForAutoReplyToReviewAndStoreMetric(err, job.attrs!.data!.reviewId);
                },
                computeRetryDelayInMinutes: defaultComputeRetryDelayInMinutes,
            },
        });
    }

    async executeJob(job: Job<DataAttributes>): Promise<void> {
        const data = autoReplyToReviewValidator.parse(job.attrs.data);
        const { reviewId, replyText, templateId, interactionId, replyLang } = data;

        await this._reviewsUseCases.handleAutoReply({ reviewId, replyText, templateId, interactionId, replyLang });
    }

    async _sendSlackAlertForAutoReplyToReviewAndStoreMetric(error: Error, reviewId: string): Promise<void> {
        const review = await this._reviewsRepository.getReviewById(reviewId);
        if (!review) {
            return;
        }
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: review.restaurantId },
            projection: { name: 1 },
            options: { lean: true },
        });
        if (!review || !restaurant) {
            return;
        }
        reviewsReplyCounter.add(1, {
            source: review.key,
            status: 'failure',
        });
        this._slackService.sendAlert({
            channel: SlackChannel.REVIEWS_ALERTS,
            data: {
                err: new MalouError(MalouErrorCode.PUBLISH_REVIEW_ERROR, {
                    message: error.message ?? JSON.stringify(error, errorReplacer),
                    metadata: { rawError: error },
                }),
                metadata: {
                    description: `[AUTO_REPLY] Last auto reply to review failed for review ${review._id} and 
                    for restaurant ${review.restaurantId}`,
                    restaurantName: restaurant?.name,
                },
            },
        });
    }
}
