import 'reflect-metadata';

import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { PlatformPresenceStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { GetReviewByIdUseCase } from ':modules/reviews/use-cases/get-review-by-id/get-review-by-id.use-case';
import * as experimentationService from ':services/experimentations-service/experimentation.service';

describe('GetReviewByIdUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);
    });

    describe('execute', () => {
        jest.spyOn(experimentationService, 'isFeatureAvailableForRestaurant').mockResolvedValue(false);

        it('should return the review by id', async () => {
            const getReviewByIdUseCase = container.resolve(GetReviewByIdUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .platformPresenceStatus(PlatformPresenceStatus.FOUND)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testCase.build();
            const seeds = testCase.getSeededObjects();

            const result = await getReviewByIdUseCase.execute(seeds.reviews[0]._id.toString());
            expect(result).toBeDefined();
        });

        it('should throw an error if the review does not exist', async () => {
            const getReviewByIdUseCase = container.resolve(GetReviewByIdUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult: () => null,
            });

            await testCase.build();

            await expect(getReviewByIdUseCase.execute(newDbId().toString())).rejects.toThrow('Review not found');
        });
    });
});
