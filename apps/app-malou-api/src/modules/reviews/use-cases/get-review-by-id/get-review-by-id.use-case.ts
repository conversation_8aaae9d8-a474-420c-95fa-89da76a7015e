import { singleton } from 'tsyringe';

import { ReviewWithTranslationsResponseDto } from '@malou-io/package-dto';

import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class GetReviewByIdUseCase {
    constructor(
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _reviewsMapper: ReviewsDtoMapper
    ) {}

    async execute(reviewId: string): Promise<ReviewWithTranslationsResponseDto> {
        const review = await this._reviewsRepository.getReviewsWithAnalysisById(reviewId);
        return this._reviewsMapper.toReviewWithTranslationsResponseDto(review);
    }
}
