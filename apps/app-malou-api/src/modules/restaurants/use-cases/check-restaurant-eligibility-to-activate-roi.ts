import { DateTime } from 'luxon';
import { singleton } from 'tsyringe';

import { IRestaurant } from '@malou-io/package-models';
import { FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER, getNumberOfMonthsSinceMostRecentDate, isNotNil } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CreateRoiActivatedNotificationUseCase } from ':modules/notifications/use-cases/create-roi-activated-notification/create-roi-activated-notification.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export default class CheckRestaurantEligibilityToActivateRoi {
    private readonly MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI = FULL_ROI_HIDDEN_FIRST_MONTHS_NUMBER + 1;
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _slackService: SlackService,
        private readonly _createRoiActivatedNotificationUseCase: CreateRoiActivatedNotificationUseCase
    ) {}

    async execute(): Promise<void> {
        const now = DateTime.now();
        const createdAtStartMonth = now
            .minus({ month: this.MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI + 1 })
            .startOf('month')
            .startOf('day');
        const createdAtEndMonth = createdAtStartMonth.endOf('month').endOf('day');

        const allActiveRestaurantsCreated = await this._restaurantsRepository.getAllActiveRestaurantsToCheckRoiEligibility({
            startDate: createdAtStartMonth.toJSDate(),
            endDate: createdAtEndMonth.toJSDate(),
        });
        const eligibleRestaurantsToActivateRoi: string[] = [];

        for (const restaurant of allActiveRestaurantsCreated) {
            if (
                getNumberOfMonthsSinceMostRecentDate([restaurant.createdAt, restaurant.openingDate].filter(isNotNil)) >=
                    this.MINIMUM_NUMBER_OF_MONTHS_TO_ACTIVATE_ROI &&
                !restaurant.roiActivated
            ) {
                eligibleRestaurantsToActivateRoi.push(restaurant._id.toString());
            }
        }

        if (!eligibleRestaurantsToActivateRoi.length) {
            logger.info('No restaurant is eligible to activate Roi');
        } else {
            await this._restaurantsRepository.upsertManyRestaurantsRoiStatus({
                restaurantsIds: eligibleRestaurantsToActivateRoi,
                activateRoi: true,
            });

            logger.info(`Saving ${eligibleRestaurantsToActivateRoi.length} restaurants...`);
            logger.info(`Restaurants saved ${eligibleRestaurantsToActivateRoi.join(', ')}`);

            await this._createRoiActivatedNotificationUseCase.execute(eligibleRestaurantsToActivateRoi);
        }

        this._sendSlackAlert(eligibleRestaurantsToActivateRoi, allActiveRestaurantsCreated);
    }

    private _sendSlackAlert(restaurantIds: string[], allRestaurants: Pick<IRestaurant, '_id' | 'name'>[]): void {
        const title = `ROI ACTIVATION JOB ==> ${restaurantIds.length} restaurants activated (finished at ${new Date().toISOString()})`;
        const details = restaurantIds
            .map((restaurantId) => {
                const associatedRestaurant = allRestaurants.find(({ _id }) => _id.toString() === restaurantId?.toString());
                if (!associatedRestaurant) {
                    return restaurantId;
                }
                return `- ${associatedRestaurant.name} (${associatedRestaurant._id?.toString()})`;
            })
            .join(', \n');
        this._slackService.sendMessage({
            channel: SlackChannel.APP_ALERTS,
            text: `${title}\n${details}`,
        });
    }
}
