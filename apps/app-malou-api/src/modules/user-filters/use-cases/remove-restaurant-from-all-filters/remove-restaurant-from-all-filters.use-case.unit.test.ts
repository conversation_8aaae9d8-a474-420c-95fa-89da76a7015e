import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultNfc } from ':modules/nfc/nfc.builder';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';
import { getDefaultUserFilters } from ':modules/user-filters/tests/user-filters.builder';
import { RemoveRestaurantFromAllFiltersUseCase } from ':modules/user-filters/use-cases/remove-restaurant-from-all-filters/remove-restaurant-from-all-filters.use-case';

describe('RemoveRestaurantFromAllFiltersUseCase', () => {
    beforeAll(() => {
        registerRepositories(['UserFiltersRepository', 'NfcsRepository']);
    });

    it('should remove restaurant from all filters and remove its associated nfcs', async () => {
        const restaurantIdToRemove = newDbId();
        const restaurantIdToKeep = newDbId();
        const totemIdToRemove = newDbId();
        const totemIdToKeep = newDbId();
        const userFilterId1 = newDbId();
        const userFilterId2 = newDbId();
        const defaultUserFilters = getDefaultUserFilters().build();

        const testCases = new TestCaseBuilderV2<'userFilters' | 'nfcs'>({
            seeds: {
                nfcs: {
                    data() {
                        return [
                            getDefaultNfc()._id(totemIdToRemove).chipName('chipname1').restaurantId(restaurantIdToRemove).build(),
                            getDefaultNfc()._id(totemIdToKeep).chipName('chipname2').build(),
                        ];
                    },
                },
                userFilters: {
                    data() {
                        return [
                            getDefaultUserFilters()
                                ._id(userFilterId1)
                                .aggregatedStatistics({
                                    ...defaultUserFilters.aggregatedStatistics,
                                    restaurantIds: [restaurantIdToRemove, restaurantIdToKeep],
                                    roiRestaurantIds: [restaurantIdToRemove, restaurantIdToKeep],
                                    totemIds: [totemIdToKeep, totemIdToRemove],
                                })
                                .aggregatedReviews({
                                    ...defaultUserFilters.aggregatedReviews,
                                    restaurantIds: [restaurantIdToRemove, restaurantIdToKeep],
                                })
                                .build(),
                            getDefaultUserFilters()
                                ._id(userFilterId2)
                                .aggregatedStatistics({
                                    ...defaultUserFilters.aggregatedStatistics,
                                    totemIds: [totemIdToRemove],
                                })
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCases.build();

        const removeRestaurantFromAllFiltersUseCase = container.resolve(RemoveRestaurantFromAllFiltersUseCase);
        await removeRestaurantFromAllFiltersUseCase.execute(restaurantIdToRemove.toString());

        const userFiltersRepository = container.resolve(UserFiltersRepository);
        const updatedUserFilter1 = await userFiltersRepository.findOneOrFail({ filter: { _id: userFilterId1 }, options: { lean: true } });
        const updatedUserFilter2 = await userFiltersRepository.findOneOrFail({ filter: { _id: userFilterId2 }, options: { lean: true } });

        expect(updatedUserFilter1.aggregatedStatistics.restaurantIds).toEqual([restaurantIdToKeep]);
        expect(updatedUserFilter1.aggregatedStatistics.roiRestaurantIds).toEqual([restaurantIdToKeep]);
        expect(updatedUserFilter1.aggregatedStatistics.totemIds).toEqual([totemIdToKeep]);
        expect(updatedUserFilter1.aggregatedReviews.restaurantIds).toEqual([restaurantIdToKeep]);

        expect(updatedUserFilter2.aggregatedStatistics.totemIds).toEqual([]);
    });
});
