import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { fullPlanGoalAndRatioByCriteria, PlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { PositiveReviewsCriteria } from ':modules/roi/use-cases/compute-performance-score-by-month/criteria/get-positive-reviews-criteria';

describe('PositiveReviewsCriteria', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);
    });

    describe('execute', () => {
        it('should return score of zero if no reviews', async () => {
            const positiveReviewsCriteria = container.resolve(PositiveReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.positiveReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await positiveReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return score of zero if no positive reviews', async () => {
            const positiveReviewsCriteria = container.resolve(PositiveReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(1)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.positiveReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await positiveReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return correct score and ratio', async () => {
            const positiveReviewsCriteria = container.resolve(PositiveReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(4)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(4)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(4)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const rate = (3 * 100) / dependencies.reviews.length;
                    return {
                        score: scoresReference.positiveReviewsRatio.goal
                            ? (rate * scoresReference.positiveReviewsRatio.weight) / scoresReference.positiveReviewsRatio.goal
                            : 0,
                        value: rate,
                        goal: scoresReference.positiveReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await positiveReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return correctly handle platforms with rating out of ten', async () => {
            const positiveReviewsCriteria = container.resolve(PositiveReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .text('text')
                                    .rating(8)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .text('text')
                                    .rating(8)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(4)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const rate = (2 * 100) / dependencies.reviews.length;
                    return {
                        score: scoresReference.positiveReviewsRatio.goal
                            ? (rate * scoresReference.positiveReviewsRatio.weight) / scoresReference.positiveReviewsRatio.goal
                            : 0,
                        value: rate,
                        goal: scoresReference.positiveReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await positiveReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should count positive reviews updated in the month even if created before', async () => {
            const positiveReviewsCriteria = container.resolve(PositiveReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();
            const lastMonth = now.minus({ month: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(lastMonth)
                                    .socialUpdatedAt(now.toJSDate())
                                    .text('text')
                                    .rating(5)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: scoresReference.positiveReviewsRatio.weight,
                        value: 100,
                        goal: scoresReference.positiveReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await positiveReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });
    });
});
