import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { fullPlanGoalAndRatioByCriteria } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import GmbReviewsUseCases from ':modules/reviews/platforms/gmb/use-cases';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { ReceivedReviewsCriteria } from ':modules/roi/use-cases/compute-performance-score-by-month/criteria/get-received-reviews-criteria';

describe('ReceivedReviewsCriteria', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);

        class GmbReviewsUseCasesMock {
            async fetchTotalReviewCount(): Promise<number> {
                return 100;
            }
        }

        container.register(GmbReviewsUseCases, {
            useValue: new GmbReviewsUseCasesMock() as unknown as GmbReviewsUseCases,
        });
    });

    describe('execute', () => {
        it('should return score of zero if no reviews', async () => {
            const receivedReviewsCriteria = container.resolve(ReceivedReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await receivedReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return correct score and ratio', async () => {
            const receivedReviewsCriteria = container.resolve(ReceivedReviewsCriteria);

            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .build(),
                                ...Array.from({ length: 11 }).map((_, i: number) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]?._id)
                                        .socialCreatedAt(now.minus({ month: 1 }).toJSDate())
                                        .text(`text-${i}`)
                                        .build()
                                ),
                                ...Array.from({ length: 5 }).map((_, i: number) =>
                                    getDefaultReview()
                                        .restaurantId(dependencies.restaurants()[0]?._id)
                                        .socialCreatedAt(now.minus({ month: 2 }).toJSDate())
                                        .text(`text-${i}`)
                                        .build()
                                ),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: (5 * scoresReference.receivedReviewsCount.weight) / 12,
                        value: 5,
                        goal: 12,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();

            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await receivedReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should count reviews updated in the month even if created before', async () => {
            const receivedReviewsCriteria = container.resolve(ReceivedReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();
            const lastMonth = now.minus({ month: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(lastMonth)
                                    .socialUpdatedAt(now.toJSDate())
                                    .text('text')
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 1 * scoresReference.receivedReviewsCount.weight,
                        value: 1,
                        goal: 1,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await receivedReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });
    });
});
