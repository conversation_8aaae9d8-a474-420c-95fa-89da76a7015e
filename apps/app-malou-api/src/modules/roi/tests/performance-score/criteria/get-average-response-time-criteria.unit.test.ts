import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { fullPlanGoalAndRatioByCriteria, PostedStatus, TimeInMilliseconds } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { AverageResponseTimeCriteria } from ':modules/roi/use-cases/compute-performance-score-by-month/criteria/get-average-response-time-criteria';

describe('AverageResponseTimeCriteria', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);
    });

    describe('execute', () => {
        it('should return score of zero if no reviews', async () => {
            const averageResponseTimeCriteria = container.resolve(AverageResponseTimeCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.averageAnswerTime.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await averageResponseTimeCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return score of zero if time is superior to goal', async () => {
            const averageResponseTimeCriteria = container.resolve(AverageResponseTimeCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const todayMinusFourDays = now.minus({ day: 4 }).toJSDate();
            const startDate = DateTime.fromJSDate(todayMinusFourDays).startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(todayMinusFourDays)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            posted: PostedStatus.POSTED,
                                            text: 'answered',
                                            socialUpdatedAt: today,
                                        },
                                    ])
                                    .text('text')
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 4 * TimeInMilliseconds.DAY,
                        goal: scoresReference.averageAnswerTime.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await averageResponseTimeCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return maximum score  if time is inferior to goal', async () => {
            const averageResponseTimeCriteria = container.resolve(AverageResponseTimeCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now().startOf('day');
            const today = now.toJSDate();
            const todayMinusTwoDays = now.minus({ day: 2 }).startOf('day').toJSDate();
            const startDate = DateTime.fromJSDate(todayMinusTwoDays).startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(todayMinusTwoDays)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            posted: PostedStatus.POSTED,
                                            text: 'answered',
                                            socialUpdatedAt: today,
                                        },
                                    ])
                                    .text('text')
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: scoresReference.averageAnswerTime.weight,
                        value: 2 * TimeInMilliseconds.DAY,
                        goal: scoresReference.averageAnswerTime.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await averageResponseTimeCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should not count reviews updated in the month for average response time', async () => {
            const averageResponseTimeCriteria = container.resolve(AverageResponseTimeCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const lastMonth = now.minus({ month: 1 }).toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(lastMonth)
                                    .socialUpdatedAt(today)
                                    .comments([
                                        {
                                            _id: newDbId(),
                                            posted: PostedStatus.POSTED,
                                            text: 'answered',
                                            socialUpdatedAt: today,
                                        },
                                    ])
                                    .text('text')
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.averageAnswerTime.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await averageResponseTimeCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result.score).toBe(expectedResult.score);
            expect(result.goal).toBe(expectedResult.goal);
        });
    });
});
