import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { fullPlanGoalAndRatioByCriteria, PostedStatus } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { AnsweredReviewsCriteria } from ':modules/roi/use-cases/compute-performance-score-by-month/criteria/get-answered-reviews-criteria';

describe('AnsweredReviewsCriteria', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository']);
    });

    describe('execute', () => {
        it('should return score of zero if no reviews', async () => {
            const answeredReviewsCriteria = container.resolve(AnsweredReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.answeredReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await answeredReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return score of zero if no answered reviews', async () => {
            const answeredReviewsCriteria = container.resolve(AnsweredReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .comments([])
                                    .text('text')
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: 0,
                        value: 0,
                        goal: scoresReference.answeredReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await answeredReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should return correct score and ratio', async () => {
            const answeredReviewsCriteria = container.resolve(AnsweredReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const today = now.toJSDate();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .comments([])
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(today)
                                    .text('text')
                                    .rating(2)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const rate = (3 / dependencies.reviews.length) * 100;
                    return {
                        score: scoresReference.answeredReviewsRatio.goal
                            ? (rate * scoresReference.answeredReviewsRatio.weight) / scoresReference.answeredReviewsRatio.goal
                            : 0,
                        value: rate,
                        goal: scoresReference.answeredReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await answeredReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });

        it('should count answered reviews updated in the month even if created before', async () => {
            const answeredReviewsCriteria = container.resolve(AnsweredReviewsCriteria);
            const scoresReference = fullPlanGoalAndRatioByCriteria;
            const now = DateTime.now();
            const startDate = now.startOf('month').startOf('day').toJSDate();
            const endDate = now.endOf('month').endOf('day').toJSDate();
            const lastMonth = now.minus({ month: 1 }).toJSDate();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]?._id)
                                    .socialCreatedAt(lastMonth)
                                    .socialUpdatedAt(now.toJSDate())
                                    .comments([
                                        { _id: newDbId(), socialUpdatedAt: now.toJSDate(), posted: PostedStatus.POSTED, text: 'answer' },
                                    ])
                                    .text('text')
                                    .rating(5)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        score: scoresReference.answeredReviewsRatio.weight,
                        value: 100,
                        goal: scoresReference.answeredReviewsRatio.goal,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await answeredReviewsCriteria.execute({ restaurantId, startDate, endDate, scoresReference });

            expect(result).toEqual(expectedResult);
        });
    });
});
