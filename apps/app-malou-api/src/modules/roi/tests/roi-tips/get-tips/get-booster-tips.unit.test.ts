import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { DateFilter, RoiTip } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCampaign } from ':modules/campaigns/tests/campaigns.builder';
import { getDefaultTotem } from ':modules/nfc/tests/nfc.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetBoosterTips } from ':modules/roi/use-cases/compute-tips/get-tips-by-category/get-booster-tips';
import { getTipDataFromId } from ':modules/roi/use-cases/get-roi-tips/utils';
import { getDefaultRestaurantWheelsOfFortune } from ':modules/wheels-of-fortune/tests/wheels-of-fortune.builder';

describe('GetBoosterTips', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'WheelsOfFortuneRepository', 'NfcsRepository', 'CampaignsRepository']);
    });

    describe('execute', () => {
        it('should return all booster tips', async () => {
            const getBoosterTips = container.resolve(GetBoosterTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const threeMonthsDateFilter: DateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'wheelsOfFortune' | 'nfcs' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    wheelsOfFortune: {
                        data() {
                            return [];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return Array.from(Array(5).keys()).map(() =>
                                getDefaultTotem().restaurantId(dependencies.restaurants()[0]._id).active(false).build()
                            );
                        },
                    },
                    campaigns: {
                        data(dependencies) {
                            return [
                                getDefaultCampaign()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .startDate(new Date('2022/05/01'))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [
                        getTipDataFromId(RoiTip.BOOSTER_NO_TOTEMS),
                        getTipDataFromId(RoiTip.BOOSTER_NO_WHEEL_OF_FORTUNE),
                        getTipDataFromId(RoiTip.BOOSTER_NO_CAMPAIGNS),
                    ];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getBoosterTips.execute(restaurantId, threeMonthsDateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should not return BOOSTER_NO_TOTEMS tip because there is at least 1 active totem', async () => {
            const getBoosterTips = container.resolve(GetBoosterTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const threeMonthsDateFilter: DateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'wheelsOfFortune' | 'nfcs' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    wheelsOfFortune: {
                        data() {
                            return [];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return Array.from(Array(5).keys()).map(() =>
                                getDefaultTotem().restaurantId(dependencies.restaurants()[0]._id).active(true).build()
                            );
                        },
                    },
                    campaigns: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    return [getTipDataFromId(RoiTip.BOOSTER_NO_WHEEL_OF_FORTUNE), getTipDataFromId(RoiTip.BOOSTER_NO_CAMPAIGNS)];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getBoosterTips.execute(restaurantId, threeMonthsDateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should not return BOOSTER_NO_WHEEL_OF_FORTUNE tip because there is an active wheel of fortunes', async () => {
            const getBoosterTips = container.resolve(GetBoosterTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const threeMonthsDateFilter: DateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'wheelsOfFortune' | 'nfcs' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    wheelsOfFortune: {
                        data(dependencies) {
                            return [
                                {
                                    ...getDefaultRestaurantWheelsOfFortune().restaurantId(dependencies.restaurants()[0]._id).build(),
                                    __t: 'RestaurantWheelOfFortune',
                                },
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return Array.from(Array(5).keys()).map(() =>
                                getDefaultTotem().restaurantId(dependencies.restaurants()[0]._id).active(true).build()
                            );
                        },
                    },
                    campaigns: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult() {
                    return [getTipDataFromId(RoiTip.BOOSTER_NO_CAMPAIGNS)];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getBoosterTips.execute(restaurantId, threeMonthsDateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should not return BOOSTER_NO_CAMPAIGNS tip because there is at least 1 active campaign', async () => {
            const getBoosterTips = container.resolve(GetBoosterTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const threeMonthsAgo = now.minus({ months: 3 }).toJSDate();
            const threeMonthsDateFilter: DateFilter = { $gte: threeMonthsAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'wheelsOfFortune' | 'nfcs' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().build()];
                        },
                    },
                    wheelsOfFortune: {
                        data(dependencies) {
                            return [
                                {
                                    ...getDefaultRestaurantWheelsOfFortune().restaurantId(dependencies.restaurants()[0]._id).build(),
                                    __t: 'RestaurantWheelOfFortune',
                                },
                            ];
                        },
                    },
                    nfcs: {
                        data(dependencies) {
                            return Array.from(Array(5).keys()).map(() =>
                                getDefaultTotem().restaurantId(dependencies.restaurants()[0]._id).active(true).build()
                            );
                        },
                    },
                    campaigns: {
                        data(dependencies) {
                            return Array.from(Array(5).keys()).map((_, i) =>
                                getDefaultCampaign()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .startDate(yesterday)
                                    .name(`Campaign${i}`)
                                    .build()
                            );
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getBoosterTips.execute(restaurantId, threeMonthsDateFilter);

            expect(result).toEqual(expectedResult);
        });
    });
});
