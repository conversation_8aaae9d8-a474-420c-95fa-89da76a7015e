import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId } from '@malou-io/package-models';
import { DateFilter, RoiTip } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultComment } from ':modules/comments/tests/comment.builder';
import { getDefaultMention } from ':modules/mentions/tests/mention.builder';
import { getDefaultConversation } from ':modules/messages/tests/conversation.builder';
import { getDefaultMessage } from ':modules/messages/tests/message.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetInteractionTips } from ':modules/roi/use-cases/compute-tips/get-tips-by-category/get-interaction-tips';
import { getTipDataFromId } from ':modules/roi/use-cases/get-roi-tips/utils';

describe('GetInteractionTips', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'ConversationsRepository',
            'MessagesRepository',
            'CommentsRepository',
            'MentionsRepository',
        ]);
    });

    describe('execute', () => {
        it('should return all interaction tips', async () => {
            const getInteractionTips = container.resolve(GetInteractionTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const oneMonthAgo = now.minus({ months: 1 }).toJSDate();
            const oneMonthDateFilter: DateFilter = { $gte: oneMonthAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'conversations' | 'messages' | 'comments' | 'mentions'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                        },
                    },
                    conversations: {
                        data(dependencies) {
                            return [
                                getDefaultConversation().latestMessageAt(yesterday).restaurantId(dependencies.restaurants()[0]._id).build(),
                            ];
                        },
                    },
                    messages: {
                        data(dependencies) {
                            return Array.from(Array(3).keys()).map((i) =>
                                getDefaultMessage()
                                    .text(`${i}`)
                                    .socialMessageId(`${i}`)
                                    .conversationId(dependencies.conversations()[0]._id)
                                    .isFromRestaurant(false)
                                    .build()
                            );
                        },
                    },
                    comments: {
                        data(dependencies) {
                            return [
                                getDefaultComment()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialCreatedAt(yesterday)
                                    .hasBeenAnswered(false)
                                    .build(),
                            ];
                        },
                    },
                    mentions: {
                        data(dependencies) {
                            return [
                                getDefaultMention()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialCreatedAt(yesterday)
                                    .hasBeenAnswered(false)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [
                        getTipDataFromId(RoiTip.INTERACTION_UNANSWERED_MESSAGE_COUNT_TOO_HIGH),
                        getTipDataFromId(RoiTip.INTERACTION_UNANSWERED_COMMENT_AND_MENTION_COUNT_TOO_HIGH),
                    ];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getInteractionTips.execute(restaurantId, oneMonthDateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should not return INTERACTION_UNANSWERED_MESSAGE_COUNT_TOO_HIGH tip because user answers at least 20% of the messages', async () => {
            const getInteractionTips = container.resolve(GetInteractionTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const oneMonthAgo = now.minus({ months: 1 }).toJSDate();
            const oneMonthDateFilter: DateFilter = { $gte: oneMonthAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'conversations' | 'messages' | 'comments' | 'mentions'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                        },
                    },
                    conversations: {
                        data(dependencies) {
                            return Array.from(Array(10).keys()).map((_, i) =>
                                getDefaultConversation()
                                    .createdAt(yesterday)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialConversationId(`socialId${i}`)
                                    .build()
                            );
                        },
                    },
                    messages: {
                        data(dependencies) {
                            return [
                                ...Array.from(Array(2).keys()).map((i) =>
                                    getDefaultMessage()
                                        .text(`${i}`)
                                        .socialMessageId(`${i}`)
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .isFromRestaurant(true)
                                        .build()
                                ),
                                getDefaultMessage()
                                    .text('text')
                                    .socialMessageId('random_social_id')
                                    .conversationId(dependencies.conversations()[1]._id)
                                    .isFromRestaurant(true)
                                    .build(),
                                getDefaultMessage()
                                    .text('text')
                                    .socialMessageId('random_social_id')
                                    .conversationId(dependencies.conversations()[2]._id)
                                    .isFromRestaurant(true)
                                    .build(),
                            ];
                        },
                    },
                    comments: {
                        data(dependencies) {
                            return [
                                getDefaultComment()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(yesterday)
                                    .hasBeenAnswered(false)
                                    .build(),
                            ];
                        },
                    },
                    mentions: {
                        data(dependencies) {
                            return [
                                getDefaultMention()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialCreatedAt(yesterday)
                                    .hasBeenAnswered(false)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [getTipDataFromId(RoiTip.INTERACTION_UNANSWERED_COMMENT_AND_MENTION_COUNT_TOO_HIGH)];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getInteractionTips.execute(restaurantId, oneMonthDateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should return empty tips array because comments and mentions are answered', async () => {
            const getInteractionTips = container.resolve(GetInteractionTips);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const oneMonthAgo = now.minus({ months: 1 }).toJSDate();
            const oneMonthDateFilter: DateFilter = { $gte: oneMonthAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'conversations' | 'messages' | 'comments' | 'mentions'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().ai({ monthlyCallCount: 0 }).build()];
                        },
                    },
                    conversations: {
                        data(dependencies) {
                            return Array.from(Array(10).keys()).map((_, i) =>
                                getDefaultConversation()
                                    .createdAt(yesterday)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialConversationId(`socialId${i}`)
                                    .build()
                            );
                        },
                    },
                    messages: {
                        data(dependencies) {
                            return [
                                ...Array.from(Array(2).keys()).map((i) =>
                                    getDefaultMessage()
                                        .text(`${i}`)
                                        .socialMessageId(`${i}`)
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .isFromRestaurant(true)
                                        .build()
                                ),
                                getDefaultMessage()
                                    .text('text')
                                    .socialMessageId('random_social_id')
                                    .conversationId(dependencies.conversations()[1]._id)
                                    .isFromRestaurant(true)
                                    .build(),
                                getDefaultMessage()
                                    .text('text')
                                    .socialMessageId('random_social_id')
                                    .conversationId(dependencies.conversations()[2]._id)
                                    .isFromRestaurant(true)
                                    .build(),
                            ];
                        },
                    },
                    comments: {
                        data(dependencies) {
                            return [
                                getDefaultComment()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(yesterday)
                                    .hasBeenAnswered(true)
                                    .build(),
                            ];
                        },
                    },
                    mentions: {
                        data(dependencies) {
                            return [
                                getDefaultMention()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialCreatedAt(yesterday)
                                    .hasBeenAnswered(true)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return [];
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getInteractionTips.execute(restaurantId, oneMonthDateFilter);

            expect(result).toEqual(expectedResult);
        });
    });
});
