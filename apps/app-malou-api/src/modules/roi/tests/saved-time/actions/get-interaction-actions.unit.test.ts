import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { DateFilter } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultComment } from ':modules/comments/tests/comment.builder';
import { getDefaultCommentMention } from ':modules/mentions/tests/comment-mention.builder';
import { getDefaultMention } from ':modules/mentions/tests/mention.builder';
import { getDefaultConversation } from ':modules/messages/tests/conversation.builder';
import { getDefaultMessage } from ':modules/messages/tests/message.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetInteractionActions } from ':modules/roi/use-cases/compute-saved-time-by-month/actions/get-interaction-actions';

describe('GetInteractionActions', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'CommentsRepository',
            'MentionsRepository',
            'CommentMentionsRepository',
            'MessagesRepository',
            'ConversationsRepository',
        ]);
    });

    describe('execute', () => {
        it('should return object with zeros if no message, comment or mention', async () => {
            const getInteractionActions = container.resolve(GetInteractionActions);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        answeredComments: 0,
                        answeredMentions: 0,
                        usedMessageTemplates: 0,
                        sentMessages: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getInteractionActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        describe('message interactions', () => {
            it('should return correct number of sent messages and used message templates', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'conversations' | 'messages'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        conversations: {
                            data(dependencies) {
                                return [getDefaultConversation().restaurantId(dependencies.restaurants()[0]._id).build()];
                            },
                        },
                        messages: {
                            data(dependencies) {
                                return [
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(newDbId())
                                        .socialCreatedAt(yesterday)
                                        .build(),
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(newDbId())
                                        .templateIdUsed(newDbId())
                                        .socialCreatedAt(yesterday)
                                        .build(),
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(undefined)
                                        .templateIdUsed(newDbId())
                                        .socialCreatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 0,
                            usedMessageTemplates: 1,
                            sentMessages: 2,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for messages', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const today = DateTime.now().toJSDate();
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'conversations' | 'messages'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        conversations: {
                            data(dependencies) {
                                return [getDefaultConversation().restaurantId(dependencies.restaurants()[0]._id).build()];
                            },
                        },
                        messages: {
                            data(dependencies) {
                                return [
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(newDbId())
                                        .templateIdUsed(newDbId())
                                        .socialCreatedAt(today)
                                        .build(),
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(newDbId())
                                        .templateIdUsed(newDbId())
                                        .socialCreatedAt(yesterday)
                                        .build(),
                                    getDefaultMessage()
                                        .conversationId(dependencies.conversations()[0]._id)
                                        .malouAuthorId(newDbId())
                                        .socialCreatedAt(today)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 0,
                            usedMessageTemplates: 1,
                            sentMessages: 1,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('comments interactions', () => {
            it('should return correct number of replied comments', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'comments'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        comments: {
                            data(dependencies) {
                                return [
                                    getDefaultComment()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id-2',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultComment().restaurantId(dependencies.restaurants()[0]._id).replies([]).build(),
                                    getDefaultComment()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultComment()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 2,
                            answeredMentions: 0,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for replies comments', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const today = DateTime.now().toJSDate();
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'comments'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        comments: {
                            data(dependencies) {
                                return [
                                    getDefaultComment()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: today,
                                            },
                                        ])
                                        .build(),
                                    getDefaultComment()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                reviewer: {
                                                    displayName: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultComment().restaurantId(dependencies.restaurants()[0]._id).replies([]).build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 1,
                            answeredMentions: 0,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('comment mentions interactions', () => {
            it('should return correct number of replied comments mentions', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'commentMentions'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        commentMentions: {
                            data(dependencies) {
                                return [
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId1')
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id-2',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId2')
                                        .replies([])
                                        .build(),
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId3')
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId4')
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 2,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for replies comment mentions', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const today = DateTime.now().toJSDate();
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'commentMentions'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        commentMentions: {
                            data(dependencies) {
                                return [
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId1')
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: today,
                                            },
                                        ])
                                        .build(),
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId2')
                                        .replies([
                                            {
                                                _id: newDbId(),
                                                text: 'My comment reply',
                                                socialId: 'reply-social-id',
                                                author: {
                                                    _id: newDbId(),
                                                    name: 'Malou Reviewer',
                                                },
                                                malouAuthorId: newDbId(),
                                                socialCreatedAt: yesterday,
                                            },
                                        ])
                                        .build(),
                                    getDefaultCommentMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('socialId3')
                                        .replies([])
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 1,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('post mentions interactions', () => {
            it('should return correct number of replied post mentions', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'mentions'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        mentions: {
                            data(dependencies) {
                                return [
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    malouAuthorId: newDbId(),
                                                    socialCreatedAt: yesterday,
                                                },
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id-2',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    malouAuthorId: newDbId(),
                                                    socialCreatedAt: yesterday,
                                                },
                                            ],
                                        })
                                        .build(),
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [],
                                        })
                                        .build(),
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    malouAuthorId: newDbId(),
                                                    socialCreatedAt: yesterday,
                                                },
                                            ],
                                        })
                                        .build(),
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    socialCreatedAt: yesterday,
                                                },
                                            ],
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 2,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for replies post mentions', async () => {
                const getInteractionActions = container.resolve(GetInteractionActions);
                const today = DateTime.now().toJSDate();
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'mentions'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        mentions: {
                            data(dependencies) {
                                return [
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    malouAuthorId: newDbId(),
                                                    socialCreatedAt: today,
                                                },
                                            ],
                                        })
                                        .build(),
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [
                                                {
                                                    _id: newDbId(),
                                                    text: 'My comment reply',
                                                    socialId: 'reply-social-id',
                                                    author: {
                                                        _id: newDbId(),
                                                        name: 'Malou Reviewer',
                                                    },
                                                    malouAuthorId: newDbId(),
                                                    socialCreatedAt: yesterday,
                                                },
                                            ],
                                        })
                                        .build(),
                                    getDefaultMention()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .post({
                                            ...getDefaultMention().build().post,
                                            replies: [],
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            answeredComments: 0,
                            answeredMentions: 1,
                            usedMessageTemplates: 0,
                            sentMessages: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getInteractionActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });
    });
});
