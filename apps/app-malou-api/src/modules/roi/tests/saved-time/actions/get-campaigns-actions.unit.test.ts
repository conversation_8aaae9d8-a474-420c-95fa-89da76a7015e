import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { DateFilter } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCampaign } from ':modules/campaigns/tests/campaigns.builder';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetCampaignsActions } from ':modules/roi/use-cases/compute-saved-time-by-month/actions/get-campaigns-actions';

describe('GetCampaignsActions', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'CampaignsRepository', 'ClientsRepository']);
    });

    describe('execute', () => {
        it('should return object with zeros if no campaigns', async () => {
            const getCampaignsActions = container.resolve(GetCampaignsActions);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        launchedCampaigns: 0,
                        launchedCampaignsClients: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getCampaignsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should return correct number of clients and campaigns', async () => {
            const getCampaignsActions = container.resolve(GetCampaignsActions);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'clients' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    clients: {
                        data(dependencies) {
                            return [
                                getDefaultClient()
                                    .phone({ prefix: 33, digits: 611111111 })
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultClient()
                                    .phone({ prefix: 33, digits: 622222222 })
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultClient()
                                    .phone({ prefix: 33, digits: 633333333 })
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                                getDefaultClient()
                                    .phone({ prefix: 33, digits: 644444444 })
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .build(),
                            ];
                        },
                    },
                    campaigns: {
                        data(dependencies) {
                            return [
                                getDefaultCampaign()
                                    .name('Campaign 1')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(yesterday)
                                    .contactInteractions([
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[0]._id,
                                        },
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[1]._id,
                                        },
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[2]._id,
                                        },
                                    ])
                                    .build(),
                                getDefaultCampaign()
                                    .name('Campaign 2')
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(yesterday)
                                    .contactInteractions([
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[3]._id,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        launchedCampaigns: 2,
                        launchedCampaignsClients: 4,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getCampaignsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should only return data within date filter', async () => {
            const getCampaignsActions = container.resolve(GetCampaignsActions);
            const now = DateTime.now();
            const today = now.toJSDate();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const threeDaysAgo = DateTime.now().minus({ days: 3 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants' | 'clients' | 'campaigns'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                    clients: {
                        data(dependencies) {
                            return [getDefaultClient().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                    campaigns: {
                        data(dependencies) {
                            return [
                                getDefaultCampaign()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(today)
                                    .name('Campaign1')
                                    .contactInteractions([
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[0]._id,
                                        },
                                    ])
                                    .build(),
                                getDefaultCampaign()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(threeDaysAgo)
                                    .name('Campaign2')
                                    .contactInteractions([
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[0]._id,
                                        },
                                    ])
                                    .build(),
                                getDefaultCampaign()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .createdAt(yesterday)
                                    .name('Campaign3')
                                    .contactInteractions([
                                        {
                                            _id: newDbId(),
                                            clientId: dependencies.clients()[0]._id,
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult() {
                    return {
                        launchedCampaigns: 1,
                        launchedCampaignsClients: 1,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getCampaignsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });
    });
});
