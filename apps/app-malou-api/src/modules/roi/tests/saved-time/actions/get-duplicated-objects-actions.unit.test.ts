import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { ApplicationLanguage, DateFilter, descriptionSize, PostSource, TemplateType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurantAttributeWithAttribute } from ':modules/attributes/tests/restaurant-attribute.builder';
import { getDefaultClient } from ':modules/clients/tests/clients.builder';
import { getDefaultKeywordTemp } from ':modules/keywords/tests/keyword.builder';
import { getDefaultMedia } from ':modules/media/tests/media.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { getDefaultRestaurantKeyword } from ':modules/restaurant-keywords/tests/restaurant-keywords.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetDuplicatedObjectsActions } from ':modules/roi/use-cases/compute-saved-time-by-month/actions/get-duplicated-objects-actions';
import { getDefaultTemplate } from ':modules/templates/tests/template.builder';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';

describe('GetDuplicatedObjectsActions', () => {
    beforeAll(() => {
        registerRepositories([
            'RestaurantsRepository',
            'RestaurantAttributesRepository',
            'KeywordsTempRepository',
            'RestaurantKeywordsRepository',
            'PostsRepository',
            'MediasRepository',
            'TemplatesRepository',
            'ClientsRepository',
        ]);
    });

    describe('execute', () => {
        it('should return object with zeros if no duplicated objects', async () => {
            class ExperimentationServiceMock {
                isFeatureAvailable = jest.fn().mockResolvedValue(false);
            }
            container.register(ExperimentationService, { useValue: new ExperimentationServiceMock() as unknown as ExperimentationService });
            const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        duplicatedDescriptions: 0,
                        duplicatedAttributes: 0,
                        duplicatedKeywords: 0,
                        duplicatedSeoPosts: 0,
                        duplicatedSocialPosts: 0,
                        duplicatedStories: 0,
                        duplicatedMedia: 0,
                        duplicatedMessageTemplates: 0,
                        duplicatedReviewTemplates: 0,
                        duplicatedClients: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        describe('description duplication', () => {
            it('should return 1 if the restaurant description was from duplication', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [
                                    getDefaultRestaurant()
                                        .descriptions([
                                            {
                                                _id: newDbId(),
                                                duplicatedFromRestaurantId: newDbId(),
                                                size: descriptionSize.LONG.key,
                                                text: 'description',
                                                language: ApplicationLanguage.FR,
                                                createdAt: new Date(),
                                                updatedAt: yesterday,
                                            },
                                            {
                                                _id: newDbId(),
                                                duplicatedFromRestaurantId: newDbId(),
                                                text: 'description',
                                                size: descriptionSize.SHORT.key,
                                                language: ApplicationLanguage.FR,
                                                createdAt: new Date(),
                                                updatedAt: yesterday,
                                            },
                                        ])
                                        .uniqueKey('facebook_101185912234405')
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 1,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for description', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [
                                    getDefaultRestaurant()
                                        .descriptions([
                                            {
                                                _id: newDbId(),
                                                duplicatedFromRestaurantId: newDbId(),
                                                size: descriptionSize.LONG.key,
                                                text: 'description',
                                                language: ApplicationLanguage.FR,
                                                createdAt: new Date(),
                                                updatedAt: today,
                                            },
                                            {
                                                _id: newDbId(),
                                                duplicatedFromRestaurantId: newDbId(),
                                                size: descriptionSize.SHORT.key,
                                                text: 'description',
                                                language: ApplicationLanguage.FR,
                                                createdAt: new Date(),
                                                updatedAt: today,
                                            },
                                        ])
                                        .uniqueKey('facebook_101185912234405')
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('attributes duplication', () => {
            it('should return correct number of duplicated attributes', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantAttributes'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        restaurantAttributes: {
                            data(dependencies) {
                                return [
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .updatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 2,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for attributes', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'restaurantAttributes'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        restaurantAttributes: {
                            data(dependencies) {
                                return [
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .build(),
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantAttributeWithAttribute()
                                        .attributeId(newDbId())
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 1,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('keywords duplication', () => {
            it('should return correct number of duplicated keywords', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        keywordsTemp: {
                            data() {
                                return [
                                    getDefaultKeywordTemp().text('keyword1').updatedAt(yesterday).build(),
                                    getDefaultKeywordTemp().text('keyword2').updatedAt(yesterday).build(),
                                    getDefaultKeywordTemp().text('keyword3').updatedAt(yesterday).build(),
                                    getDefaultKeywordTemp().text('keyword4').updatedAt(yesterday).build(),
                                ];
                            },
                        },
                        restaurantKeywords: {
                            data(dependencies) {
                                return [
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[0]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[1]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[2]._id)
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[3]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .updatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 2,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for keywords', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'keywordsTemp' | 'restaurantKeywords'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        keywordsTemp: {
                            data() {
                                return [
                                    getDefaultKeywordTemp().text('keyword1').updatedAt(today).build(),
                                    getDefaultKeywordTemp().text('keyword2').updatedAt(yesterday).build(),
                                    getDefaultKeywordTemp().text('keyword3').updatedAt(today).build(),
                                ];
                            },
                        },
                        restaurantKeywords: {
                            data(dependencies) {
                                return [
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[0]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .build(),
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[1]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultRestaurantKeyword()
                                        .keywordId(dependencies.keywordsTemp()[2]._id)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 1,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('posts duplication', () => {
            it('should return correct number of duplicated posts', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SEO)
                                        .createdAt(yesterday)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SOCIAL)
                                        .createdAt(yesterday)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SOCIAL)
                                        .isStory(true)
                                        .createdAt(yesterday)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .source(PostSource.SOCIAL)
                                        .isStory(true)
                                        .createdAt(yesterday)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .source(PostSource.SOCIAL)
                                        .createdAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 1,
                            duplicatedSocialPosts: 1,
                            duplicatedStories: 1,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for posts', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'posts'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SEO)
                                        .createdAt(today)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SEO)
                                        .createdAt(yesterday)
                                        .build(),
                                    getDefaultPost()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .source(PostSource.SEO)
                                        .createdAt(today)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 1,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('medias duplication', () => {
            it('should return correct number of duplicated medias', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'medias'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        medias: {
                            data(dependencies) {
                                return [
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .socialId('media1')
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .socialId('media2')
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultMedia()
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .socialId('media3')
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .socialId('media4')
                                        .updatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 2,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for medias', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'medias'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        medias: {
                            data(dependencies) {
                                return [
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .socialId('media1')
                                        .updatedAt(today)
                                        .build(),
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .socialId('media2')
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultMedia()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .socialId('media3')
                                        .updatedAt(today)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 1,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('templates duplication', () => {
            it('should return correct number of duplicated templates', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'templates'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        templates: {
                            data(dependencies) {
                                return [
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 1')
                                        .type(TemplateType.MESSAGE)
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 2')
                                        .type(TemplateType.MESSAGE)
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 3')
                                        .updatedAt(yesterday)
                                        .type(TemplateType.REVIEW)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 4')
                                        .type(TemplateType.REVIEW)
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .name('Template 5')
                                        .updatedAt(yesterday)
                                        .type(TemplateType.MESSAGE)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .name('Template 6')
                                        .type(TemplateType.REVIEW)
                                        .updatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 2,
                            duplicatedReviewTemplates: 2,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for templates', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'templates'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        templates: {
                            data(dependencies) {
                                return [
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 1')
                                        .type(TemplateType.MESSAGE)
                                        .updatedAt(today)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 2')
                                        .type(TemplateType.MESSAGE)
                                        .updatedAt(yesterday)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 3')
                                        .type(TemplateType.REVIEW)
                                        .updatedAt(today)
                                        .build(),
                                    getDefaultTemplate()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .name('Template 4')
                                        .type(TemplateType.REVIEW)
                                        .updatedAt(yesterday)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 1,
                            duplicatedReviewTemplates: 1,
                            duplicatedClients: 0,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });

        describe('clients duplication', () => {
            it('should return correct number of duplicated clients', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'clients'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        clients: {
                            data(dependencies) {
                                return [
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .phone({
                                            prefix: 1,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .phone({
                                            prefix: 2,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                    getDefaultClient()
                                        .restaurantId(newDbId())
                                        .duplicatedFromRestaurantId(dependencies.restaurants()[0]._id)
                                        .updatedAt(yesterday)
                                        .phone({
                                            prefix: 3,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(undefined)
                                        .updatedAt(yesterday)
                                        .phone({
                                            prefix: 4,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 2,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });

            it('should only return data within date filter for clients', async () => {
                class ExperimentationServiceMock {
                    isFeatureAvailable = jest.fn().mockResolvedValue(false);
                }
                container.register(ExperimentationService, {
                    useValue: new ExperimentationServiceMock() as unknown as ExperimentationService,
                });
                const getDuplicatedObjectsActions = container.resolve(GetDuplicatedObjectsActions);
                const yesterday = DateTime.now().minus({ days: 1 }).toJSDate();
                const twoDaysAgo = DateTime.now().minus({ days: 2 }).toJSDate();
                const today = DateTime.now().toJSDate();
                const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

                const testCase = new TestCaseBuilderV2<'restaurants' | 'clients'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                            },
                        },
                        clients: {
                            data(dependencies) {
                                return [
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .phone({
                                            prefix: 1,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(yesterday)
                                        .phone({
                                            prefix: 2,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                    getDefaultClient()
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .duplicatedFromRestaurantId(newDbId())
                                        .updatedAt(today)
                                        .phone({
                                            prefix: 3,
                                            digits: 1234567890,
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult() {
                        return {
                            duplicatedDescriptions: 0,
                            duplicatedAttributes: 0,
                            duplicatedKeywords: 0,
                            duplicatedSeoPosts: 0,
                            duplicatedSocialPosts: 0,
                            duplicatedStories: 0,
                            duplicatedMedia: 0,
                            duplicatedMessageTemplates: 0,
                            duplicatedReviewTemplates: 0,
                            duplicatedClients: 1,
                        };
                    },
                });

                await testCase.build();
                const seededObjects = testCase.getSeededObjects();
                const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
                const expectedResult = testCase.getExpectedResult();

                const result = await getDuplicatedObjectsActions.execute(restaurantId, dateFilter);

                expect(result).toEqual(expectedResult);
            });
        });
    });
});
