import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { DateFilter, ReportType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultReport } from ':modules/reports/tests/report.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { GetReportsActions } from ':modules/roi/use-cases/compute-saved-time-by-month/actions/get-reports-actions';

describe('GetReportsActions', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReportsRepository']);
    });

    describe('execute', () => {
        it('should return object with zeros if no reports', async () => {
            const getReportsActions = container.resolve(GetReportsActions);
            const now = DateTime.now();
            const yesterday = now.minus({ days: 1 }).toJSDate();
            const twoDaysAgo = now.minus({ days: 2 }).toJSDate();
            const dateFilter: DateFilter = { $gte: twoDaysAgo, $lte: yesterday };

            const testCase = new TestCaseBuilderV2<'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').build()];
                        },
                    },
                },
                expectedResult() {
                    return {
                        activatedReports: 0,
                        reviewsReports: 0,
                        aggregatedReviewsReports: 0,
                        performanceReports: 0,
                        aggregatedPerformanceReports: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getReportsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should return correct number of reports and emails for single restaurant in configurations', async () => {
            const getReportsActions = container.resolve(GetReportsActions);
            const aDay = DateTime.local(2024, 1, 1); // monday
            const startOfWeek = aDay.startOf('week').toJSDate();
            const endOfWeek = aDay.endOf('week').toJSDate();
            const dateFilter: DateFilter = { $gte: startOfWeek, $lte: endOfWeek };
            const daysBetweenInterval = 7;
            const mondaysBetweenInterval = 1;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reports'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('facebook_101185912234405').internalName('Restaurant 1').build()];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.DAILY_REVIEWS)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id],
                                        },
                                    ])
                                    .build(),
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id],
                                        },
                                    ])
                                    .build(),
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2', 'recipient3'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id],
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const dailyReviewsRecipients = dependencies.reports[0].configurations[0].recipients.length;
                    const weeklyReviewsRecipients = dependencies.reports[1].configurations[0].recipients.length;
                    const monthlyPerformanceRecipients = dependencies.reports[2].configurations[0].recipients.length;
                    return {
                        activatedReports: dependencies.reports.length,
                        reviewsReports: daysBetweenInterval * dailyReviewsRecipients + mondaysBetweenInterval * weeklyReviewsRecipients,
                        aggregatedReviewsReports: 0,
                        performanceReports: 1 * monthlyPerformanceRecipients,
                        aggregatedPerformanceReports: 0,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getReportsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });

        it('should return correct number of reports and emails for multiple restaurant in configurations', async () => {
            const getReportsActions = container.resolve(GetReportsActions);
            const aDay = DateTime.local(2024, 1, 1); // monday
            const startOfWeek = aDay.startOf('week').toJSDate();
            const endOfWeek = aDay.endOf('week').toJSDate();
            const dateFilter: DateFilter = { $gte: startOfWeek, $lte: endOfWeek };
            const daysBetweenInterval = 7;
            const mondaysBetweenInterval = 1;

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reports'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().uniqueKey('facebook_101185912234405').internalName('Restaurant 1').build(),
                                getDefaultRestaurant().uniqueKey('facebook_101185912234406').internalName('Restaurant 2').build(),
                            ];
                        },
                    },
                    reports: {
                        data(dependencies) {
                            return [
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.DAILY_REVIEWS)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id, dependencies.restaurants()[1]._id],
                                        },
                                    ])
                                    .build(),
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.WEEKLY_REVIEWS)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id, dependencies.restaurants()[1]._id],
                                        },
                                    ])
                                    .build(),
                                getDefaultReport()
                                    .active(true)
                                    .type(ReportType.MONTHLY_PERFORMANCE)
                                    .configurations([
                                        {
                                            _id: newDbId(),
                                            recipients: ['recipient1', 'recipient2', 'recipient3'],
                                            restaurantsIds: [dependencies.restaurants()[0]._id, dependencies.restaurants()[1]._id],
                                        },
                                    ])
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const dailyReviewsRecipients = dependencies.reports[0].configurations[0].recipients.length;
                    const weeklyReviewsRecipients = dependencies.reports[1].configurations[0].recipients.length;
                    const monthlyPerformanceRecipients = dependencies.reports[2].configurations[0].recipients.length;
                    return {
                        activatedReports: dependencies.reports.length,
                        reviewsReports: 0,
                        aggregatedReviewsReports:
                            daysBetweenInterval * dailyReviewsRecipients + mondaysBetweenInterval * weeklyReviewsRecipients,
                        performanceReports: 0,
                        aggregatedPerformanceReports: 1 * monthlyPerformanceRecipients,
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const restaurantId = (seededObjects.restaurants[0]._id as DbId).toString();
            const expectedResult = testCase.getExpectedResult();

            const result = await getReportsActions.execute(restaurantId, dateFilter);

            expect(result).toEqual(expectedResult);
        });
    });
});
