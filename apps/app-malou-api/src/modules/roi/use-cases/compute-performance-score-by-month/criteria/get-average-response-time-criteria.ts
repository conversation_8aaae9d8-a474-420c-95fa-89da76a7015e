import { autoInjectable } from 'tsyringe';

import { isNotNil } from '@malou-io/package-utils';

import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import {
    ComputePerformanceCriteria,
    ComputePerformanceCriteriaResponse,
    ComputePerformanceParams,
} from ':modules/roi/use-cases/compute-performance-score-by-month/compute-performance-score-by-month.interface';

@autoInjectable()
export class AverageResponseTimeCriteria implements ComputePerformanceCriteria {
    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute({
        restaurantId,
        startDate,
        endDate,
        scoresReference,
    }: ComputePerformanceParams): Promise<ComputePerformanceCriteriaResponse> {
        const reviewCount = await this._reviewsRepository.countGmbReviewsForRestaurantAndPeriod(restaurantId, startDate, endDate, {
            checkTextNull: false,
            getUpdatedReviews: false,
        });

        const averageAnswerTimeReference = scoresReference.averageAnswerTime;

        if (!reviewCount) {
            return {
                score: 0,
                value: 0,
                goal: averageAnswerTimeReference?.goal ?? 0,
            };
        }

        const averageResponseTimeInMilliseconds = await this._reviewsRepository.getAverageResponseTimeInMillisecondsByRestaurantId({
            restaurantId,
            startDate,
            endDate,
        });

        if (
            isNotNil(averageResponseTimeInMilliseconds) &&
            averageAnswerTimeReference?.goal &&
            averageResponseTimeInMilliseconds <= averageAnswerTimeReference.goal
        ) {
            return {
                score: averageAnswerTimeReference.weight,
                value: averageResponseTimeInMilliseconds,
                goal: averageAnswerTimeReference.goal,
            };
        }

        return {
            score: 0,
            value: averageResponseTimeInMilliseconds ?? 0,
            goal: averageAnswerTimeReference?.goal ?? 0,
        };
    }
}
