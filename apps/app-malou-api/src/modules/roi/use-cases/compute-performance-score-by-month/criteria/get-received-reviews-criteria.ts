import { isNil, isNumber, max, round } from 'lodash';
import { DateTime } from 'luxon';
import { autoInjectable } from 'tsyringe';

import { getLastXMonthsBeforeCurrentMonth } from '@malou-io/package-utils';

import { getDateIntervalForMonth } from ':helpers/date/date';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import {
    ComputePerformanceCriteria,
    ComputePerformanceCriteriaResponse,
    ComputePerformanceParams,
} from ':modules/roi/use-cases/compute-performance-score-by-month/compute-performance-score-by-month.interface';

@autoInjectable()
export class ReceivedReviewsCriteria implements ComputePerformanceCriteria {
    readonly REVIEWS_EVOLUTION_FACTOR = 1.05;

    constructor(private readonly _reviewsRepository: ReviewsRepository) {}

    async execute({
        restaurantId,
        scoresReference,
        startDate,
        endDate,
    }: ComputePerformanceParams): Promise<ComputePerformanceCriteriaResponse> {
        const reviewCount = await this._reviewsRepository.countGmbReviewsForRestaurantAndPeriod(restaurantId, startDate, endDate, {
            checkTextNull: false,
            getUpdatedReviews: true,
        });

        if (isNil(reviewCount)) {
            return {
                score: 0,
                value: 0,
                goal: 0,
            };
        }

        const reviewsGoal = await this._getMaxReviewCountPerMonth(restaurantId, startDate, endDate);

        const receivedReviewsReference = scoresReference.receivedReviewsCount;
        if (!this._isCorrectNumber(reviewsGoal)) {
            return {
                score: receivedReviewsReference?.weight ?? 0,
                value: reviewCount,
                goal: 0,
            };
        }

        if (reviewCount === 0) {
            return {
                score: 0,
                value: 0,
                goal: reviewsGoal,
            };
        }

        if (reviewCount >= reviewsGoal) {
            return {
                score: receivedReviewsReference?.weight ?? 0,
                value: reviewCount,
                goal: reviewsGoal,
            };
        }

        const scoreRatio = receivedReviewsReference
            ? reviewsGoal
                ? receivedReviewsReference.weight / reviewsGoal
                : receivedReviewsReference.weight / reviewCount
            : 0;

        return {
            score: reviewCount * scoreRatio,
            value: reviewCount,
            goal: reviewsGoal,
        };
    }

    private _isCorrectNumber(value: number): boolean {
        return isNumber(value) && isFinite(value);
    }

    private async _getMaxReviewCountPerMonth(restaurantId: string, startDate: Date, endDate: Date): Promise<number> {
        const months = getLastXMonthsBeforeCurrentMonth(12, {
            currentMonth: DateTime.fromJSDate(startDate).month,
            currentYear: DateTime.fromJSDate(endDate).year,
        });

        const reviewCountPerMonth = await Promise.all(
            months.map(async ({ month, year }) => {
                const dateFilter = getDateIntervalForMonth({ month, year });
                return this._reviewsRepository.countGmbReviewsForRestaurantAndPeriod(
                    restaurantId,
                    dateFilter.startDate,
                    dateFilter.endDate,
                    { checkTextNull: false, getUpdatedReviews: true }
                );
            })
        );

        const maxReviewCount = max(reviewCountPerMonth) ?? 0;

        return round(maxReviewCount * this.REVIEWS_EVOLUTION_FACTOR, 0);
    }
}
