import { container } from 'tsyringe';

import { PlatformKey, ReviewAnalysisChartDataTag, ReviewAnalysisSentiment, ReviewAnalysisTag } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReviewAnalysis } from ':modules/review-analyses/tests/review-analysis.builder';
import { GetReviewAnalysesChartDataUseCase } from ':modules/review-analyses/use-cases/get-review-analyses-chart-data/get-review-analyses-chart-data.use-case';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';

describe('', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'ReviewsRepository', 'ReviewAnalysesRepository']);
    });

    describe('execute', () => {
        it('should return an empty object if no analyses reviews are found', async () => {
            const useCase = container.resolve(GetReviewAnalysesChartDataUseCase);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                    reviewAnalysis: {
                        data() {
                            return [];
                        },
                    },
                },
                expectedResult(): any {
                    return {};
                },
            });
            await testCase.build();
            const filter = {
                restaurantIds: [],
                keys: [PlatformKey.GMB],
                startDate: new Date('2024-12-01'),
                endDate: new Date('2025-01-01'),
            };

            const result = await useCase.execute(filter);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });

        it('should return count by restaurant if at least one review analyses is matched', async () => {
            const useCase = container.resolve(GetReviewAnalysesChartDataUseCase);
            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'reviewAnalysis'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().name('Le beau restaurant').uniqueKey('restaurant_0_key').active(true).build(),
                                getDefaultRestaurant().name('La belle creperie').uniqueKey('restaurant_1_key').active(true).build(),
                            ];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).socialId('socialId1').build(),
                                getDefaultReview().restaurantId(dependencies.restaurants()[1]._id).socialId('socialId2').build(),
                            ];
                        },
                    },
                    reviewAnalysis: {
                        data(dependencies) {
                            return [
                                getDefaultReviewAnalysis()
                                    .reviewSocialId(dependencies.reviews()[0].socialId)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .platformKey(PlatformKey.GMB)
                                    .segmentAnalyses([
                                        {
                                            sentiment: ReviewAnalysisSentiment.NEGATIVE,
                                            originalSegment: 'Very bad',
                                            probability: 0.7,
                                            segment: 'Very bad',
                                            tag: ReviewAnalysisTag.ATMOSPHERE,
                                        },
                                    ])
                                    .createdAt(new Date('2024-12-25'))
                                    .build(),
                                getDefaultReviewAnalysis()
                                    .reviewSocialId(dependencies.reviews()[1].socialId)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .platformKey(PlatformKey.GMB)
                                    .segmentAnalyses([
                                        {
                                            sentiment: ReviewAnalysisSentiment.POSITIVE,
                                            originalSegment: 'Best crepes ever',
                                            probability: 0.9,
                                            segment: 'Best crepes ever',
                                            tag: ReviewAnalysisTag.FOOD,
                                        },
                                    ])
                                    .createdAt(new Date('2024-12-25'))
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies): any {
                    return {
                        [dependencies.restaurants[0]._id.toString()]: {
                            [ReviewAnalysisChartDataTag.ATMOSPHERE]: {
                                negative: 1,
                                positive: 0,
                                total: 1,
                            },
                            [ReviewAnalysisChartDataTag.EXPEDITIOUSNESS]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.FOOD]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.HYGIENE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.PRICE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.SERVICE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.TOTAL]: {
                                negative: 1,
                                positive: 0,
                                total: 1,
                            },
                        },
                        [dependencies.restaurants[1]._id.toString()]: {
                            [ReviewAnalysisChartDataTag.ATMOSPHERE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.EXPEDITIOUSNESS]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.FOOD]: {
                                negative: 0,
                                positive: 1,
                                total: 1,
                            },
                            [ReviewAnalysisChartDataTag.HYGIENE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.PRICE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.SERVICE]: {
                                negative: 0,
                                positive: 0,
                                total: 0,
                            },
                            [ReviewAnalysisChartDataTag.TOTAL]: {
                                negative: 0,
                                positive: 1,
                                total: 1,
                            },
                        },
                    };
                },
            });
            await testCase.build();
            const filter = {
                restaurantIds: testCase.getSeededObjects().restaurants.map((restaurant) => restaurant._id.toString()),
                keys: [PlatformKey.GMB],
                startDate: new Date('2024-12-01'),
                endDate: new Date('2025-01-01'),
            };

            const result = await useCase.execute(filter);

            const expectedResult = testCase.getExpectedResult();
            expect(result).toEqual(expectedResult);
        });
    });
});
