import AWS from 'aws-sdk';
import assert from 'node:assert';
import { singleton } from 'tsyringe';

import { KeywordSearchImpressionsType } from '@malou-io/package-utils';

import { Config } from ':config';

enum InvocationType {
    Event = 'Event',
    RequestResponse = 'RequestResponse',
    DryRun = 'DryRun',
}

export type FailedSearchKeywordsImpressionsResponse = {
    errorMessage: string;
    errorType: string;
    requestId: string;
    stackTrace: string[];
};

export enum SearchKeywordsImpressionsInvocationType {
    KEYWORD_SEARCH_PROCESSING = 'keywords_search_processing',
    BRAND_KEYWORDS_IDENTIFICATION = 'brand_keywords_identification',
}

export type SearchKeywordsImpressionsParams = {
    month: number;
    year: number;
    monthImpressionsData: {
        keywordSearch: string;
        value: number;
    }[];
    restaurantData: {
        formattedAddress: string;
        restaurantName: string;
        organizationName: string;
        selectedKeywords: {
            id: string;
            text: string;
        }[];
        brandNameKeywords: string[];
        brandGroupKeywords: string[];
    };
    type: SearchKeywordsImpressionsInvocationType.KEYWORD_SEARCH_PROCESSING;
};

export type SearchKeywordsImpressionsResponse = {
    month: number;
    year: number;

    monthImpressionsData: {
        keywordSearch: string;
        value: number;
        type: KeywordSearchImpressionsType;
        relatedSelectedKeywords: string[];
    }[];

    selectedKeywordsImpressions: {
        id: string;
        keywordSearch: string;
        value: number;
    }[];
};

@singleton()
export class SearchKeywordsImpressionsService {
    private readonly LAMBDA = new AWS.Lambda({
        region: Config.services.aws.region,
        accessKeyId: Config.services.aws.key,
        secretAccessKey: Config.services.aws.secret,
    });

    async processKeywordSearchImpressions(
        params: SearchKeywordsImpressionsParams
    ): Promise<{ data: SearchKeywordsImpressionsResponse } | { error: FailedSearchKeywordsImpressionsResponse }> {
        const { functionName } = Config.services.searchKeywordsImpressions;
        assert(functionName, 'Missing search keywords impressions function name');

        return this._callLambda(params, functionName, InvocationType.RequestResponse);
    }

    private async _callLambda(
        payload: SearchKeywordsImpressionsParams,
        functionName: string,
        invocationType: InvocationType
    ): Promise<any> {
        const params: AWS.Lambda.InvocationRequest = {
            FunctionName: functionName,
            Payload: JSON.stringify(payload),
            InvocationType: invocationType,
        };

        return new Promise((resolve, reject) => {
            this.LAMBDA.invoke(params, (err, data) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(data);
                }
            });
        }).then((data) => {
            if (invocationType === InvocationType.Event && !(data as AWS.Lambda.InvocationResponse).Payload) {
                return data;
            }
            const result = JSON.parse((data as AWS.Lambda.InvocationResponse).Payload as string);

            if (result.errorMessage) {
                return { error: result };
            }
            return { data: result };
        });
    }
}
