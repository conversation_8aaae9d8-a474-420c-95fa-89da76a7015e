import 'reflect-metadata';

import ':env';

import ':di';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { PlatformsGetter } from ':modules/platforms/platforms.getter';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import {
    PublishOnConnectedPlatformsUseCase,
    RestaurantPopulatedToPublish,
} from ':modules/platforms/use-cases/publish-on-connected-platforms/publish-on-connected-platforms.use-case';
import YextListingService from ':modules/publishers/yext/services/yext-listing.service';
import UpdateLocationUseCase from ':modules/publishers/yext/use-cases/update-location/update-location.use-case';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';

@singleton()
class ManualStartUpdateOnPlatformTask {
    constructor(
        private readonly _publishOnConnectedPlatformsUseCase: PublishOnConnectedPlatformsUseCase,
        private readonly _updateLocationUseCase: UpdateLocationUseCase,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _platformsGetter: PlatformsGetter,
        private readonly _gmbMapper: GmbMapper,
        private readonly _yextListingService: YextListingService
    ) {}

    async execute() {
        const restaurantId = '67ea78e9b347db007bc88d5d';

        const platformKey = PlatformKey.ABC;
        const keysToUpdate = [
            'reservationUrl',
            'orderUrl',
            'regularHours',
            'category',
            'website',
        ] as (keyof RestaurantPopulatedToPublish)[];

        if (!restaurantId || !platformKey) {
            return logger.info('Please provide a restaurantId and the platformKey to perform the update on');
        }

        // const listings = await this._yextListingService.getListingsForRestaurant(restaurantId);

        // const platformData = await this._platformsGetter.getPlatformUseCases(platformKey)?.getOverviewData({ restaurantId });
        // const malouData = await this._gmbMapper.toMalouMapper(platformData, ['reservationUrl', 'orderUrl']);

        const restaurant: RestaurantPopulatedToPublish = (await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            options: {
                lean: true,
                populate: [
                    { path: 'category' },
                    { path: 'logo' },
                    { path: 'cover' },
                    { path: 'categoryList' },
                    { path: 'attributeList', populate: [{ path: 'attribute' }] },
                ],
            },
        })) as RestaurantPopulatedToPublish;

        await this._updateLocationUseCase.execute(restaurant);

        const response = await this._publishOnConnectedPlatformsUseCase.execute({ platformKey, restaurant, keysToUpdate });
        logger.info('response :>>', response);
    }
}

const task = container.resolve(ManualStartUpdateOnPlatformTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
