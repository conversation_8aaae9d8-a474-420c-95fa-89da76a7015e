import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';

import { GenerateTailwindConfigurationService } from ':modules/store-locator/services/generate-tailwind-configuration/generate-tailwind-configuration.service';
import StoreLocatorOrganizationConfigRepository from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
class CreateOrganizationConfigurationTask {
    constructor(
        private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository,
        private readonly _generateTailwindConfigurationService: GenerateTailwindConfigurationService
    ) {}

    async execute(): Promise<void> {
        const config = await this._getTailwindConfiguration('67cf1ef531d778287af0d2ef');
        // await this._handleBioBurgerConfiguration();
        await this._handleBolkiriConfiguration();
    }

    private async _handleBioBurgerConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.updateOne({
            filter: {
                organizationId: toDbId('66695c8079a84d7da2a8e4cd'),
            },
            update: {
                plugins: {
                    googleAnalytics: {
                        trackingId: 'G-BB80WJF683',
                    },
                },
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/primary.woff',
                        },
                        {
                            class: 'primary-bold',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/66695c8079a84d7da2a8e4cd/fonts/primary-bold.woff',
                            weight: '900',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#19422c',
                        },
                        {
                            class: 'secondary',
                            value: '#eb7049',
                        },
                        {
                            class: 'tertiary',
                            value: '#fcf6ec',
                        },
                        {
                            class: 'fourth',
                            value: '#c4a696',
                        },
                    ],
                },
            },
        });
    }

    private async _handleBolkiriConfiguration(): Promise<void> {
        await this._storeLocatorOrganizationConfigRepository.create({
            data: {
                organizationId: toDbId('67cf1ef531d778287af0d2ef'),
                cloudfrontDistributionId: 'ED691UB5YSI29',
                baseUrl: 'https://restaurants.bolkiri.fr',
                styles: {
                    fonts: [
                        {
                            class: 'primary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/Satoshi-Medium.woff',
                        },
                        {
                            class: 'primary-bold',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/Satoshi-Medium.woff',
                            weight: '900',
                        },
                        {
                            class: 'secondary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/dosis.woff2',
                        },
                        {
                            class: 'tertiary',
                            src: 'https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/67cf1ef531d778287af0d2ef/fonts/titanone.woff2',
                        },
                    ],
                    colors: [
                        {
                            class: 'primary',
                            value: '#656161',
                        },
                        {
                            class: 'secondary',
                            value: '#FFFFFF',
                        },
                        {
                            class: 'tertiary',
                            value: '#FBC513',
                        },
                        {
                            class: 'fourth',
                            value: '#D3D3D3',
                        },
                    ],
                },
            },
        });
    }

    private async _getTailwindConfiguration(organizationId: string): Promise<string> {
        const storeLocatorOrganizationConfig = await this._storeLocatorOrganizationConfigRepository.findOne({
            filter: { organizationId },
            options: { lean: true },
        });

        if (!storeLocatorOrganizationConfig) {
            throw new Error(`No configuration found for organization ${organizationId}`);
        }

        // Assuming GenerateTailwindConfigurationService is implemented and available
        return this._generateTailwindConfigurationService.execute({ storeLocatorOrganizationConfig });
    }
}

const task = container.resolve(CreateOrganizationConfigurationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
