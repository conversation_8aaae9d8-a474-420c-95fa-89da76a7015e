import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { MalouErrorCode, waitFor } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { SlackChannel, SlackService, SlackUsers } from ':services/slack.service';

@singleton()
class TestSlackIntegrationTask {
    constructor(private readonly _slackService: SlackService) {}

    async execute(): Promise<void> {
        this._slackService.sendMessage({
            channel: SlackChannel.STORE_LOCATOR_ALERTS,
            text: 'This is a test message from the TestSlackIntegrationTask.',
            shouldPing: true,
        });

        this._slackService.sendAlert({
            data: {
                err: new MalouError(MalouErrorCode.AGENDA_DATABASE_CONNECTION_FAILED, {
                    message: 'This is a test error for Slack integration.',
                }),
            },
            channel: SlackChannel.STORE_LOCATOR_ALERTS,
            shouldPing: [SlackUsers.CYRIL, SlackUsers.MAXIME],
        });

        await waitFor(20000); // Wait for 20 seconds to ensure messages are sent before exiting
    }
}

const task = container.resolve(TestSlackIntegrationTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
