import axios from 'axios';
import { DateTime } from 'luxon';
import path from 'path';
import { singleton } from 'tsyringe';
import v8 from 'v8';
import { isMainThread, Worker } from 'worker_threads';

import { SizeInBytes, TimeInMilliseconds } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { AwsS3 } from ':plugins/cloud-storage/s3';
import { ExperimentationService } from ':services/experimentations-service/experimentation.service';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class MemoryManager {
    private _lastSnapshotDate: Date | null = null;
    private _runTimeAppName = process.env.START_SQS_CONSUMER === 'true' ? 'WORKER' : 'API';
    private _isEnabled = true;
    private _isStarted = false;
    private _taskMetadata: {
        id: string;
        arn: string;
    } | null = null;

    constructor(
        private _cloudStorageService: AwsS3,
        private _slackService: SlackService,
        private readonly _experimentationService: ExperimentationService
    ) {}

    startMonitoring(): void {
        logger.info('[MEMORY_MANAGER] available size for node: ', this._bytesToMB(v8.getHeapStatistics().total_available_size));

        if (this._isStarted) {
            logger.error('[MEMORY_MANAGER] Memory manager is already started');
            return;
        }
        this._isStarted = true;
        this._initTaskMetadata().catch((error) => {
            logger.error(`Error initializing task metadata: ${error.message}`);
        });

        // check if the feature is enabled every minute
        setInterval(async () => {
            this._isEnabled = await this._experimentationService.isFeatureAvailable('kill-switch-memory-manager');
        }, TimeInMilliseconds.MINUTE);

        // Log memory usage every 5 seconds
        setInterval(() => this._logMemoryUsage(), TimeInMilliseconds.SECOND * 5);
    }

    private async _initTaskMetadata() {
        const TASK_METADATA_ENDPOINT = 'http://169.254.170.2/v2/metadata';

        try {
            // Fetch metadata from ECS Task Metadata endpoint
            // this endpoint is standard for all ECS tasks
            const response = await axios.get(TASK_METADATA_ENDPOINT);

            const metadata = response.data;

            // Extract the task ID from the Task ARN
            const taskArn = metadata.TaskARN;
            const taskId = taskArn.split('/').pop();
            this._taskMetadata = {
                id: taskId,
                arn: taskArn,
            };
        } catch (error: any) {
            console.error(`Error fetching metadata: ${error.message}`);
        }
    }

    private _logMemoryUsage(): void {
        if (!this._isEnabled) {
            return;
        }
        const memoryUsage = process.memoryUsage();

        const highCeil = process.env.NODE_ENV === 'production' ? 6 : 2 * SizeInBytes.GIGA_BYTES;

        if (memoryUsage.heapUsed >= highCeil && this._canStartSnapshot()) {
            logger.warn(
                `[MEMORY_MANAGER] Memory usage is high - RSS: ${this._bytesToMB(memoryUsage.rss)} MB, Heap Total: ${this._bytesToMB(
                    memoryUsage.heapTotal
                )} MB, Heap Used: ${this._bytesToMB(memoryUsage.heapUsed)} MB`
            );
            this._startHeapSnapshot();
        } else {
            logger.info(
                `[MEMORY_MANAGER] Memory usage - RSS: ${this._bytesToMB(memoryUsage.rss)} MB, Heap Total: ${this._bytesToMB(
                    memoryUsage.heapTotal
                )} MB, Heap Used: ${this._bytesToMB(memoryUsage.heapUsed)} MB`
            );
        }
    }

    private _canStartSnapshot(): boolean {
        if (!this._lastSnapshotDate) {
            return true;
        }
        const diff = DateTime.now().diff(DateTime.fromJSDate(this._lastSnapshotDate)).as('hour');
        return diff >= 1;
    }

    private _bytesToMB(bytes: number): number {
        return Math.round(bytes / 1024 / 1024);
    }

    private _startHeapSnapshot(): void {
        if (process.env.I_AM_A === 'developer') {
            return;
        }
        this._lastSnapshotDate = new Date();
        logger.info('[MEMORY_MANAGER] Starting heap snapshot');
        const fileName = `${this._runTimeAppName}-heap-snapshot-${DateTime.now().toISO()}-task-${
            this._taskMetadata?.id ?? 'no-id'
        }.heapsnapshot`;
        const filePath = `./downloadedMedias/${fileName}`;

        // would never happen to be false but just in case
        if (isMainThread) {
            const workerFilePath = path.resolve(__dirname, 'heap-snapshot-worker.js');
            const worker = new Worker(workerFilePath, {
                workerData: { filePath },
            });

            worker.once('message', async () => {
                try {
                    logger.info(`[MEMORY_MANAGER] Heap snapshot written to ${filePath}`);
                    const url = await this._saveSnapshotToBucket({ filePath, fileName });
                    await this._sendAlert(url);
                    logger.info(`[MEMORY_MANAGER] Heap snapshot deleted from ${filePath}`);
                } catch (error: any) {
                    logger.error(`[MEMORY_MANAGER] Error saving heap snapshot: ${error.message}`);
                }
            });

            // we don't care about the message content here
            worker.postMessage('heapdump');
        }
    }

    private async _saveSnapshotToBucket({ filePath, fileName }: { filePath: string; fileName: string }): Promise<string> {
        const url = await this._cloudStorageService.uploadMedia({
            localImagePath: filePath,
            remotePath: 'heap-snapshots',
            mediaName: fileName,
            extension: 'heapsnapshot',
        });
        return url;
    }

    private async _sendAlert(snapshotUrl: string) {
        const description = `Memory usage Ceil reached (2GB) in ${this._runTimeAppName}. Heap snapshot saved to: \n
        ${snapshotUrl} \n  in task ARN: ${this._taskMetadata?.arn}`;
        this._slackService.sendAlert({
            channel: SlackChannel.INFRA_ALERTS,
            data: { err: new Error(description) },
        });
    }
}
