import { Application, NextFunction, Request, Response } from 'express';
import { container } from 'tsyringe';

import { IMaintenance } from '@malou-io/package-models';
import { TimeInMilliseconds } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import MaintenanceRepository from ':modules/maintenance/maintenance.repository';

const maintenanceRepository = container.resolve(MaintenanceRepository);
let cachedMaintenanceMode: IMaintenance | null = null;
let cacheExpiry = 0;
const CACHE_DURATION_MS = 30 * TimeInMilliseconds.SECOND; // 30 seconds

export function addMaintenanceHeaderMiddleware(app: Application) {
    app.use(async (_req: Request, res: Response, next: NextFunction) => {
        try {
            const now = Date.now();

            if (!cachedMaintenanceMode || now > cacheExpiry) {
                const maintenanceMode = await maintenanceRepository.findOneOrFail({ filter: {} });
                cachedMaintenanceMode = maintenanceMode;
                cacheExpiry = now + CACHE_DURATION_MS;
            }

            res.setHeader(
                'X-Maintenance-Status',
                JSON.stringify({
                    status: cachedMaintenanceMode.status,
                    until: cachedMaintenanceMode.until,
                    trespass: cachedMaintenanceMode.trespass,
                    redirect: cachedMaintenanceMode.redirect,
                    webhookRedirectActive: cachedMaintenanceMode.webhookRedirectActive,
                    localWebhookUri: cachedMaintenanceMode.localWebhookUri,
                })
            );

            next();
        } catch (error) {
            logger.warn('Error adding maintenance header:', error);
            next();
        }
    });
}

export function clearMaintenanceCache(): void {
    cachedMaintenanceMode = null;
    cacheExpiry = 0;
}
