{"name": "@malou-io/store-locator", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "deploy": "pnpm run type-check && astro build", "preview": "pnpm run type-check && astro preview", "astro": "astro", "type-check": "astro check", "format": "prettier  . --write", "format:check": "prettier . --check"}, "dependencies": {"@astrojs/partytown": "^2.1.4", "@astrojs/sitemap": "^3.4.0", "@malou-io/package-dto": "workspace:*", "@tailwindcss/vite": "^4.1.6", "astro": "^5.7.13", "i18next": "^24.2.3", "sharp": "=0.33.1", "tailwindcss": "^4.0.11"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "prettier": "^3.3.3", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.2", "vite": "^6.3.5"}}