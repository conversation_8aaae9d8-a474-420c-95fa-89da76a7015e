// Define valid header keys as a union type
const validComponentKeys = ['bioburger', 'bolkiri'] as const;
type ComponentKey = (typeof validComponentKeys)[number];

const componentMap = {
    header: {
        bioburger: () => import(':components/custom/bioburger/Header.astro'),
        bolkiri: () => import(':components/custom/bolkiri/Header.astro'),
    },
    footer: {
        bioburger: () => import(':components/custom/bioburger/Footer.astro'),
        bolkiri: () => import(':components/custom/bolkiri/Footer.astro'),
    },
} as const;

// Function to dynamically load a component (header, footer, css, etc.)
export async function loadComponent({
    component,
    organization,
}: {
    component: 'header' | 'footer';
    organization: string | undefined;
}): Promise<any> {
    const componentKey = organization?.toLowerCase() as ComponentKey;
    if (!validComponentKeys.includes(componentKey)) {
        console.warn(
            `Unknown store name: ${organization}, falling back to default ${component} component.`,
        );
    }

    return (await componentMap[component][componentKey]()).default;
}
