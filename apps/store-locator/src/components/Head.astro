---
import type { StorePage } from ':interfaces/pages.interfaces';
import { generateShareImages } from ':utils/images/shareImages';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    headBlock: StorePage['headBlock'];
}

const { headBlock } = Astro.props as Props;

// Generate shareImageUrls (transform images and hide original URLs)
const { facebookImageUrl, twitterImageUrl, snippetImageUrl } =
    await generateShareImages({
        facebookImageUrl: headBlock.facebookImageUrl,
        twitterImageUrl: headBlock.twitterImageUrl,
        snippetImageUrl: headBlock.snippetImageUrl,
    });

// Add snippetImageUrl to microdata
const microdata = JSON.parse(headBlock.microdata);
microdata.image = snippetImageUrl;
headBlock.microdata = JSON.stringify(microdata, null, 2);
---

<meta charset="UTF-8" />

<meta name="viewport" content="width=device-width, initial-scale=1.0" />

<link rel="canonical" href={headBlock.url} />
<link rel="sitemap" href="/sitemap-index.xml" />

{/* Favicon */}
{
    headBlock.favIcons &&
        headBlock.favIcons.map((favIcon) => (
            <link
                rel={favIcon.rel}
                type={favIcon.type}
                href={favIcon.url}
                sizes={favIcon.sizes}
            />
        ))
}

{/* Basic Meta Tags */}
<title>{headBlock.title}</title>
<meta name="description" content={headBlock.description} />
<meta name="keywords" content={headBlock.keywords} />
<meta name="author" content={headBlock.organizationName} />
<meta name="robots" content="index, follow" />

{/* OpenGraph Tags (for social media sharing) */}
<meta property="og:title" content={headBlock.title} />
<meta property="og:description" content={headBlock.description} />
<meta property="og:type" content="restaurant" />
<meta property="og:url" content={headBlock.url} />
<meta property="og:image" content={facebookImageUrl} />
<meta property="og:site_name" content={headBlock.organizationName} />
<meta property="og:locale" content={headBlock.locale} />

{/* Twitter Card Tags */}
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={headBlock.title} />
<meta name="twitter:description" content={headBlock.twitterDescription} />
<meta name="twitter:image" content={twitterImageUrl} />
{
    headBlock.xUserName && (
        <meta name="twitter:site" content={headBlock.xUserName} />
    )
}

{
    headBlock.googleAnalyticsId && (
        <>
            <script
                type="text/partytown"
                src={`https://www.googletagmanager.com/gtag/js?id=${headBlock.googleAnalyticsId}`}
            />
            <script
                type="text/partytown"
                set:html={`
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', '${headBlock.googleAnalyticsId}');
            `}
            />
        </>
    )
}

{/* Microdata for Local SEO (Best for search engines: Google, Bing, ...) */}
<script is:inline type="application/ld+json" set:html={headBlock.microdata} />
