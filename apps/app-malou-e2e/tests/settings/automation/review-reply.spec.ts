import { Page } from '@playwright/test';
import { expect, it } from 'baseTest';
import setupAutomation from 'tests/settings/automation/automation.setup';
import { openEditReviewReplyModal, ReviewReplyAutomationType } from 'tests/settings/automation/utils';

import { DbId, newDbId, PlatformModel, toDbId } from '@malou-io/package-models';
import { PlatformKey } from '@malou-io/package-utils';

const CHECKED_TOGGLE_CLASS = /mat-mdc-slide-toggle-checked/i;

async function waitForAutomationToBeReady(page: Page) {
    await page.locator('app-automation-card').first().waitFor({ state: 'visible' });
    await page.waitForSelector('app-automation-card:nth-child(2)');
}

it.describe.serial('should enable then disable automatic reply for review with comments', () => {
    let deliverooPlatformId: DbId;
    const credentialId = newDbId();

    it.beforeAll(async () => {
        try {
            // Create a properly connected Deliveroo platform
            const platform = await PlatformModel.findOneAndUpdate(
                {
                    restaurantId: toDbId(process.env.RESTAURANT_ID_LE_NINA),
                    key: PlatformKey.DELIVEROO,
                    socialId: 'test-social-id',
                },
                {
                    restaurantId: toDbId(process.env.RESTAURANT_ID_LE_NINA),
                    key: PlatformKey.DELIVEROO,
                    socialId: 'test-social-id',
                    credentials: [credentialId],
                },
                { upsert: true, new: true, lean: true }
            );
            deliverooPlatformId = platform._id;
        } catch (error: any) {
            // Duplicate key error
            if (error.code !== 11000) {
                throw error;
            }
            // If the platform already exists, we can safely ignore this error
            // and continue with the test
        }
    });

    it.afterAll(async () => {
        if (deliverooPlatformId) {
            // Cleanup the created platform
            await PlatformModel.findByIdAndDelete(deliverooPlatformId);
        }
    });
    it('should check that automatic reply for review with comment or without comment are both disabled', async ({ page }) => {
        await setupAutomation(); // needed to reset the automation state because creating a new restaurant sets up the automation as active by default
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);
        await waitForAutomationToBeReady(page);
        const disabledAutomationCardCount = await page.locator('.malou-chip--red').count();

        expect(disabledAutomationCardCount).toBe(2);
    });

    it('should enable automatic review reply for reviews with comments', async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);

        await waitForAutomationToBeReady(page);

        await openEditReviewReplyModal(page, ReviewReplyAutomationType.WITH_COMMENT);

        const modal = page.locator('app-edit-review-reply-automations-modal');

        const allCheckedSlidToggle = await modal.locator('.mat-mdc-slide-toggle-checked').count();

        expect(allCheckedSlidToggle).toBe(0);

        const oneAndTwoStarToggle = modal.locator('mat-slide-toggle').first();

        await oneAndTwoStarToggle?.click();

        const replyWithRadioButton = modal.locator('mat-radio-button');

        expect(await replyWithRadioButton.count()).toBe(2);

        const checkReplyWithRadioButton = modal.locator('.mat-mdc-radio-checked');

        const input = await checkReplyWithRadioButton.locator('input').first();
        expect(await input.getAttribute('value')).toBe('AI');

        await modal.getByTestId('review-reply-automation-save-btn').click();
    });

    it('should check that automatic reply for review with comment is enabled and the one without comment is disabled', async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);

        await waitForAutomationToBeReady(page);

        const disabledAutomationCardCount = await page.locator('.malou-chip--red').count();
        const enabledAutomationCardCount = await page.locator('.malou-chip--success-light').count();

        expect(disabledAutomationCardCount).toBe(1);
        expect(enabledAutomationCardCount).toBe(1);
    });

    it('should disable automatic review reply for reviews with comments', async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);

        await waitForAutomationToBeReady(page);

        await openEditReviewReplyModal(page, ReviewReplyAutomationType.WITH_COMMENT);

        const modal = page.locator('app-edit-review-reply-automations-modal');

        const allSlideToggles = await modal.locator('mat-slide-toggle').all();

        const [_firstSlideToggle, ...all] = allSlideToggles;

        await expect(modal.locator('mat-slide-toggle').first()).toHaveClass(CHECKED_TOGGLE_CLASS);

        Promise.all(
            all.map(async (slideToggle) => {
                expect(slideToggle).not.toHaveClass(CHECKED_TOGGLE_CLASS);
            })
        );

        const oneAndTwoStarToggle = allSlideToggles[0];

        await oneAndTwoStarToggle?.click();

        Promise.all(
            allSlideToggles.map(async (slideToggle) => {
                expect(slideToggle).not.toHaveClass(CHECKED_TOGGLE_CLASS);
            })
        );

        await modal.getByTestId('review-reply-automation-save-btn').click();

        await page.waitForTimeout(2000);

        const disabledAutomationCardCount = await page.locator('.malou-chip--red').count();

        expect(disabledAutomationCardCount).toBe(2);
    });

    it('should not show Deliveroo in platforms selection for WITHOUT_COMMENT automations event if the platform is connected', async ({
        page,
    }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);

        await waitForAutomationToBeReady(page);

        await openEditReviewReplyModal(page, ReviewReplyAutomationType.WITHOUT_COMMENT);

        const modal = page.locator('app-edit-review-reply-automations-modal');

        // Click the toggle to enable the automation
        const allSlideToggles = await modal.locator('mat-slide-toggle').all();

        const lastSlideToggle = allSlideToggles.at(-1);

        await lastSlideToggle?.click();

        // Select AI method to show platforms selector
        await page.locator('app-select-platforms').click();

        // Get all platform options in the select-platforms component
        const platformOptions = await page.locator('mat-option').all();
        const platformTexts = await Promise.all(platformOptions.map((option) => option.textContent()));

        // Verify Deliveroo is not in the list
        expect(platformTexts).not.toContain('Deliveroo');
    });

    it('should show Deliveroo in platforms selection for WITH_COMMENT automations', async ({ page }) => {
        await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/settings/automations`);

        await waitForAutomationToBeReady(page);

        await openEditReviewReplyModal(page, ReviewReplyAutomationType.WITH_COMMENT);

        const modal = page.locator('app-edit-review-reply-automations-modal');

        // Click the toggle to enable the automation
        const allSlideToggles = await modal.locator('mat-slide-toggle').all();

        const lastSlideToggle = allSlideToggles.at(-1);

        await lastSlideToggle?.click();

        // Select AI method to show platforms selector
        await page.locator('app-select-platforms').click();

        // Get all platform options in the select-platforms component
        const platformOptions = await page.locator('mat-option').all();
        const platformTexts = await Promise.all(platformOptions.map((option) => option.textContent()));

        // Verify Deliveroo is in the list
        expect(platformTexts).toContain('Deliveroo');
    });
});
