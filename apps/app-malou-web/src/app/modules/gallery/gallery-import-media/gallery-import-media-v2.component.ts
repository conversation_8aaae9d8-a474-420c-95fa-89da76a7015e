import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    ElementRef,
    OnD<PERSON>roy,
    OnInit,
    signal,
    viewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { isNotNil, ProcessingMediaStatus } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { ProcessingMediasService } from ':core/services/processing-medias.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { MediaService, UploadV2Result } from ':modules/media/media.service';
import { CircleProgressComponent } from ':shared/components-v3/circle-progress/circle-progress.component';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';

import * as GalleryImportMediaActions from './gallery-import-media.actions';
import { selectCurrentFolderId, selectIsGalleryOpen } from './gallery-import-media.reducer';
import { GalleryImportMediaService } from './gallery-import-media.service';

enum ImportStatus {
    SCHEDULED = 'SCHEDULED',
    UPLOADING = 'UPLOADING',
    PROCESSING = 'PROCESSING',
}

type ImportJob = { file: File } & ( // the file object is used as an identifier (the referenced object stays the same)
    | { status: ImportStatus.SCHEDULED }
    | {
          status: ImportStatus.UPLOADING;
          /** a number between 0 and 1 */
          progress: number | null;
      }
    | { status: ImportStatus.PROCESSING; processingMediaId: string | null; progress: number | null }
);

const getImportProgress = (job: ImportJob): number => {
    if (job.status === ImportStatus.SCHEDULED) {
        return 0;
    }
    if (job.status === ImportStatus.UPLOADING) {
        return (job.progress ?? 0) / 2;
    }
    if (job.status === ImportStatus.PROCESSING) {
        return 0.6;
    }
    return 1;
};

/**
 * This component is supposed to be instanciated once for the whole application.
 *
 * See https://airtable.com/appIqBldyX7wZlWnp/tblbOxMTpexQyxSTV/viwVSdtBlz857nQiA/recdHmIJwdJGtRTaf
 */
@Component({
    selector: 'app-gallery-import-media-v2',
    templateUrl: './gallery-import-media-v2.component.html',
    imports: [CircleProgressComponent, MalouSpinnerComponent, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GalleryImportMediaV2Component implements OnDestroy, OnInit {
    readonly jobs = signal<ImportJob[]>([]);

    readonly totalFiles = computed(() => this.jobs().length);

    private readonly _currentFolderId = toSignal(this._store.select(selectCurrentFolderId), { initialValue: null });

    /** between 0 and 100 */
    readonly importProgress = computed<number>(() => {
        const jobs = this.jobs();
        if (!jobs.length) {
            return 0;
        }
        const sum = jobs.reduce((sum_, j) => sum_ + (getImportProgress(j) ?? 0), 0);
        return (sum / jobs.length) * 100;
    });

    readonly importProgress$ = toObservable(this.importProgress);

    readonly ImportStatus = ImportStatus;

    private _fileInput = viewChild.required<ElementRef<HTMLInputElement>>('fileInput');

    private readonly _errors: WritableSignal<string[]> = signal([]);

    private readonly _dragAndDropEnabled = this._store.selectSignal(selectIsGalleryOpen);

    constructor(
        private readonly _bodyDragAndDropEventsService: BodyDragAndDropEventsService,
        private readonly _galleryImportMediaService: GalleryImportMediaService,
        private readonly _mediaService: MediaService,
        private readonly _restaurantsService: RestaurantsService,
        private readonly _store: Store,
        private readonly _translateService: TranslateService,
        private readonly _processingMediasService: ProcessingMediasService
    ) {
        effect(() =>
            this._store.dispatch({
                type: GalleryImportMediaActions.setFilesErrors.type,
                filesErrors: this._errors(),
            })
        );

        this._bodyDragAndDropEventsService.drop.pipe(takeUntilDestroyed()).subscribe(this._onDrop);
        this._bodyDragAndDropEventsService.dragOver.pipe(takeUntilDestroyed()).subscribe(this._onDragOver);
    }

    ngOnInit(): void {
        this._galleryImportMediaService.element.set(this);
    }

    ngOnDestroy(): void {
        this._galleryImportMediaService.element.set(null);
    }

    /** Can be called from other components */
    public openFilePicker(): void {
        this._fileInput().nativeElement.value = '';
        this._fileInput().nativeElement.click();
    }

    onFileInputChange(_event: Event): void {
        const files: File[] = Array.from(this._fileInput().nativeElement.files ?? []);
        for (const file of files) {
            this._scheduleImport(file);
        }
    }

    private _onDragOver = (event: DragEvent): void => {
        if (this._dragAndDropEnabled()) {
            event.preventDefault();
        }
    };

    private _onDrop = (event: DragEvent): void => {
        if (!this._dragAndDropEnabled()) {
            return;
        }

        event.preventDefault();
        const droppedFiles: FileList | null = event.dataTransfer?.files as FileList | null;
        if (droppedFiles) {
            for (const file of Array.from(droppedFiles)) {
                this._scheduleImport(file);
            }
        }
    };

    /**
     * Appends a file to the list of file to import. This does not mean that the file
     * will be uploaded immediately.
     */
    private _scheduleImport(file: File): void {
        const importJob: ImportJob = { file, status: ImportStatus.SCHEDULED };
        this.jobs.update((jobs) => [...jobs, importJob]);
        this._maybeStartUpload();
    }

    /** Starts a scheduled import job if possible. Does nothing otherwise. */
    private _maybeStartUpload(): void {
        setTimeout(() => this._maybeStartUploadImpl());
    }

    /** Should not be called directly. Call it via _maybeStartUpload(). */
    private _maybeStartUploadImpl(): void {
        this.jobs.update((jobs: ImportJob[]): ImportJob[] => {
            const maxParallelUploads = 2;
            if (jobs.filter((u) => u.status === ImportStatus.UPLOADING).length >= maxParallelUploads) {
                // already enough uploads in progress
                return jobs;
            }

            const job = jobs.find((u) => u.status === ImportStatus.SCHEDULED);
            if (!job) {
                // nothing to start
                return jobs;
            }

            this._mediaService
                .uploadV2({
                    file: job.file,
                    onProgress: (progress) => this._updateUploadProgress(job.file, progress),
                    queryParams: { restaurantId: this._restaurantsService.currentRestaurant._id, folderId: this._currentFolderId() },
                })
                .then((result) => this._onUploadEnd(job.file, result));

            return [...jobs.filter((j) => j !== job), { ...job, status: ImportStatus.UPLOADING, progress: 0 }];
        });
    }

    private _updateUploadProgress(file: File, progress: number) {
        this._updateJob(file, (job: ImportJob): ImportJob | null => {
            if (job.status !== ImportStatus.UPLOADING) {
                return null;
            }

            return {
                file: job.file,
                status: ImportStatus.UPLOADING,
                progress,
            };
        });
    }

    private async _onProcessingEnd(file: File, mediaId: string | null): Promise<void> {
        this._updateJob(file, (job: ImportJob): ImportJob | null => {
            if (mediaId) {
                this._mediaService.getMediumById(mediaId).subscribe(({ data: media }) => {
                    this._store.dispatch({
                        type: GalleryImportMediaActions.setCreatedMedia.type,
                        createdMedia: [media],
                    });
                });
            } else {
                this._showProcessingError(job.file);
            }
            return null;
        });
    }

    private async _onUploadEnd(file: File, result: UploadV2Result): Promise<void> {
        this._maybeStartUpload();

        this._updateJob(file, (job: ImportJob): ImportJob | null => {
            if (job.status !== ImportStatus.UPLOADING) {
                throw new Error();
            }

            if (!result.success) {
                this._showUploadError(file);
                return null;
            }

            this._processingMediasService.waitUntilEnded(result.result.processingMediaId).then((processingMedia) => {
                if (processingMedia.status === ProcessingMediaStatus.SUCCESS && processingMedia.mediaId) {
                    this._onProcessingEnd(job.file, processingMedia.mediaId);
                } else {
                    this._onProcessingEnd(job.file, null);
                }
            });

            return { file, status: ImportStatus.PROCESSING, processingMediaId: result.result.processingMediaId, progress: 0 };
        });
    }

    private _showUploadError(file: File): void {
        this._showImportError(file, this._translateService.instant('gallery.upload_errors.network_error'));
    }

    private _showProcessingError(file: File): void {
        this._showImportError(file, this._translateService.instant('gallery.upload_errors.invalid_file'));
    }

    private _showImportError(file: File, reason: string): void {
        this._errors.update((errors) => [
            ...errors,
            this._translateService.instant('gallery.upload_error', { fileName: file.name, reason }),
        ]);
    }

    /** The job is removed if the updateFn fuction returns null */
    private _updateJob(file: File, updateFn: (job: ImportJob) => ImportJob | null): void {
        this.jobs.update((jobs) =>
            jobs
                .map((job) => {
                    if (job.file === file) {
                        const newJob = updateFn(job);
                        if (newJob && newJob.file !== file) {
                            throw new Error('File mismatch');
                        }
                        return newJob;
                    }
                    return job;
                })
                .filter(isNotNil)
        );
    }
}
