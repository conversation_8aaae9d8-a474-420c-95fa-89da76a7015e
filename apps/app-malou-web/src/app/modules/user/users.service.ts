import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import {
    AdminSearchUsersUserDto,
    GetFrontChatUserEmailHashResponseDto,
    OrganizationUserDto,
    UpdateUserProfileInputBodyDto,
} from '@malou-io/package-dto';
import { ApiResultV2, ApplicationLanguage } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { MessagesNotificationsSettings, User, UserRestaurant } from ':modules/user/user';
import { ApiResult } from ':shared/models';

@Injectable({ providedIn: 'root' })
export class UsersService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/users/`;

    constructor(private readonly _http: HttpClient) {}

    getUser(userId: string): Observable<User> {
        return this._http.get(this.API_BASE_URL + userId).pipe(map((res: any) => new User(res.data)));
    }

    updateUser$(userId: string, params: UpdateUserProfileInputBodyDto): Observable<ApiResult<User>> {
        return this._http.put<ApiResult<User>>(this.API_BASE_URL + userId, params);
    }

    updateUserOrganizations(userId: string, params: any): Observable<ApiResult<User>> {
        return this._http.put<ApiResult<User>>(`${this.API_BASE_URL}organizations/${userId}`, params, { withCredentials: true }).pipe(
            map((res) => {
                res.data = new User(res.data);
                return res;
            })
        );
    }

    createUser(params: Partial<User>): Observable<ApiResult<User>> {
        return this._http.post<ApiResult<User>>(this.API_BASE_URL, params, {
            withCredentials: true,
        });
    }

    adminSearchUsers(params: {
        text?: string;
        limit?: number;
        offset?: number;
    }): Observable<ApiResultV2<AdminSearchUsersUserDto[], { pagination: { total: number } }>> {
        const queryParams = new URLSearchParams();
        if (params.text) {
            queryParams.set('text', params.text);
        }
        if (params.limit) {
            queryParams.set('limit', params.limit.toString());
        }
        if (params.offset) {
            queryParams.set('offset', params.offset.toString());
        }

        return this._http
            .get<
                ApiResultV2<User[], { pagination: { total: number } }>
            >(`${this.API_BASE_URL}admin/search?${queryParams.toString()}`, { withCredentials: true })
            .pipe(
                map((res) => ({
                    ...res,
                    data: res.data.map((user) => new User(user)),
                }))
            );
    }

    createGuest(params: Object): Observable<ApiResult> {
        return this._http.post<ApiResult>(this.API_BASE_URL + 'guest', params);
    }

    index(): Observable<User[]> {
        return this._http.get<ApiResult<User[]>>(this.API_BASE_URL).pipe(map((res: ApiResult<User[]>) => res.data.map((u) => new User(u))));
    }

    getOrganizationUsers(organizationId: string): Observable<User[]> {
        return this._http
            .get<ApiResult<OrganizationUserDto[]>>(`${this.API_BASE_URL}organizations/${organizationId}`, { withCredentials: true })
            .pipe(map((res: ApiResult<OrganizationUserDto[]>) => res.data.map((u) => new User(u))));
    }

    usersRestaurants(): Observable<ApiResult<UserRestaurant[]>> {
        return this._http.get<ApiResult<UserRestaurant[]>>(this.API_BASE_URL + 'users_restaurants');
    }

    getUsersForRestaurant(restaurantId: string): Observable<ApiResult<UserRestaurant[]>> {
        return this._http.get<ApiResult<UserRestaurant[]>>(this.API_BASE_URL + 'restaurants/' + restaurantId, { withCredentials: true });
    }

    updateUserRestaurantById(userRestaurantId: string, update: Partial<UserRestaurant>): Observable<ApiResult> {
        return this._http.put<ApiResult>(`${this.API_BASE_URL}restaurants/${userRestaurantId}`, update, { withCredentials: true });
    }

    updateUserLastVisitedRestaurant(restaurantId: string): Observable<ApiResult> {
        return this._http.put<ApiResult>(`${this.API_BASE_URL}restaurants/${restaurantId}/last_visited`, { withCredentials: true });
    }

    deleteUserRestaurant(urId: string): Observable<ApiResult> {
        return this._http.delete<ApiResult>(`${this.API_BASE_URL}restaurants/` + urId, { withCredentials: true });
    }

    deleteUserFromAllOwnedRestaurants(userId: string): Observable<ApiResult> {
        return this._http.delete<ApiResult>(`${this.API_BASE_URL}restaurants/all/${userId}`, { withCredentials: true });
    }

    getUserByEmail$(email: string): Observable<User> {
        return this._http
            .get<ApiResult<User>>(`${this.API_BASE_URL}email/${email}`, { withCredentials: true })
            .pipe(map((res: any) => new User(res.data)));
    }

    getUserNotificationsSettings(userId: string): Observable<ApiResult<MessagesNotificationsSettings>> {
        return this._http.get<ApiResult<MessagesNotificationsSettings>>(`${this.API_BASE_URL}${userId}/settings`, {
            withCredentials: true,
        });
    }

    getFrontChatUserEmailHash(
        userEmail: string,
        language: ApplicationLanguage
    ): Observable<ApiResultV2<GetFrontChatUserEmailHashResponseDto>> {
        return this._http.post<ApiResultV2<GetFrontChatUserEmailHashResponseDto>>(`${this.API_BASE_URL}front-chat/email-hash/`, {
            email: userEmail,
            language,
        });
    }

    searchUsers(
        searchText?: string,
        fields: string[] = [],
        limit?: number,
        offset?: number
    ): Observable<ApiResultV2<User[], { pagination: { total: number } }>> {
        const params: any = {};

        if (searchText?.trim()) {
            params.text = searchText.trim();
        }

        if (fields.length > 0) {
            params.fields = fields;
        }

        if (limit !== undefined) {
            params.limit = limit;
        }

        if (offset !== undefined) {
            params.offset = offset;
        }

        return this._http
            .get<ApiResultV2<OrganizationUserDto[], { pagination: { total: number } }>>(`${this.API_BASE_URL}search`, { params })
            .pipe(
                map((result) => ({
                    ...result,
                    data: result.data.map((user) => new User(user)),
                }))
            );
    }
}
