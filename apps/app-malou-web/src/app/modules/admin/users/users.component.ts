import { NgTemplateOutlet, SlicePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { RouterLink } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { omit } from 'lodash';
import { BehaviorSubject, combineLatest, distinctUntilChanged, forkJoin, map, Observable, of, Subject } from 'rxjs';
import { filter, switchMap, take, tap } from 'rxjs/operators';

import { AdminSearchUsersUserDto } from '@malou-io/package-dto';
import { isNotNil, Role } from '@malou-io/package-utils';

import { SpinnerService } from ':core/services/malou-spinner.service';
import { OrganizationsService } from ':core/services/organizations.service';
import { ToastService } from ':core/services/toast.service';
import { LocalStorage } from ':core/storage/local-storage';
import { AdminService } from ':modules/admin/admin.service';
import { RestaurantsListModalComponent } from ':modules/admin/restaurants-list-modal/restaurants-list-modal.component';
import { OrganizationsListModalComponent } from ':modules/admin/users/organizations-list-modal/organizations-list-modal.component';
import { ChangePasswordComponent } from ':modules/admin/users/reset-password/change-password.component';
import { UpsertUserModalComponent, UserUpsertForm } from ':modules/admin/users/upsert-user-modal/upsert-user-modal.component';
import * as UserActions from ':modules/user/store/user.actions';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import { User } from ':modules/user/user';
import { UsersService } from ':modules/user/users.service';
import { SlideToggleComponent } from ':shared/components-v3/slide-toggle/slide-toggle.component';
import { PaginatorComponent } from ':shared/components/paginator/paginator.component';
import { SearchComponent } from ':shared/components/search/search.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { LocalStorageKey } from ':shared/enums/local-storage-key';
import { UpsertKind } from ':shared/enums/upsert-kind.enum';
import { TrackByFunctionFactory } from ':shared/helpers/track-by-functions';
import { Organization } from ':shared/models/organization';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-users',
    templateUrl: './users.component.html',
    styleUrls: ['./users.component.scss'],
    imports: [
        NgTemplateOutlet,
        PaginatorComponent,
        SearchComponent,
        SkeletonComponent,
        SlideToggleComponent,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        FormsModule,
        MatButtonModule,
        MatFormFieldModule,
        MatIconModule,
        MatInputModule,
        MatMenuModule,
        MatTableModule,
        ReactiveFormsModule,
        RouterLink,
        TranslateModule,
        SlicePipe,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class UsersComponent implements OnInit {
    readonly SvgIcon = SvgIcon;
    readonly UpsertKind = UpsertKind;
    readonly Role = Role;

    readonly DISPLAYED_COLUMNS: string[] = ['name', 'email', 'role', 'organizations', 'restaurants', 'caslRole', 'active', 'id', 'actions'];
    readonly killSubscriptions$: Subject<void> = new Subject<void>();
    readonly MAX_ITEMS_PER_COLUMN = 5;

    dataSource: MatTableDataSource<AdminSearchUsersUserDto> = new MatTableDataSource<AdminSearchUsersUserDto>([]);
    refresh$: Subject<void> = new Subject<void>();
    readonly searchQuery$ = new BehaviorSubject<string>('');
    _paginator: MatPaginator | null = null;

    readonly pageSize = 20;
    readonly currentOffset = signal<number>(0);
    readonly isLoading = signal<boolean>(false);
    readonly totalCount = signal<number>(0);
    readonly hasNextPage = signal<boolean>(false);
    readonly hasPreviousPage = signal<boolean>(false);

    private readonly _destroyRef = inject(DestroyRef);

    readonly trackByIdFn = TrackByFunctionFactory.get('_id');

    constructor(
        private readonly _usersService: UsersService,
        private readonly _adminService: AdminService,
        private readonly _spinnerService: SpinnerService,
        private readonly _organizationsService: OrganizationsService,
        private readonly _toastService: ToastService,
        private readonly _httpErrorPipe: HttpErrorPipe,
        private readonly _customDialogService: CustomDialogService,
        private readonly _store: Store,
        private readonly _translate: TranslateService
    ) {}

    @ViewChild(MatPaginator) set paginator(matPaginator: MatPaginator) {
        if (matPaginator) {
            this._paginator = matPaginator;
        }
    }

    ngOnInit(): void {
        combineLatest([
            this.searchQuery$.pipe(
                distinctUntilChanged(),
                tap(() => {
                    this.currentOffset.set(0);
                    if (this._paginator) {
                        this._paginator.firstPage();
                    }
                })
            ),
            this.refresh$,
        ])
            .pipe(
                tap(() => this.isLoading.set(true)),
                switchMap(() => this._loadUsers$(this.currentOffset())),
                tap(({ users, organizations: allOrganizations, total }) => {
                    this.totalCount.set(total);
                    this._updateDataSource(users, allOrganizations);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe();

        this.refresh$.next();
    }

    onSearchChange(searchValue: string): void {
        this.searchQuery$.next(searchValue);
    }

    onPageEvent({ pageIndex, pageSize }: { pageIndex: number; pageSize: number }): void {
        const offset = pageIndex * pageSize;
        this.currentOffset.set(offset);
        this.refresh$.next();
    }

    private _loadUsers$(offset: number): Observable<{
        users: AdminSearchUsersUserDto[];
        organizations: Organization[];
        total: number;
    }> {
        const searchText = this.searchQuery$.getValue();

        return combineLatest([
            this._usersService.adminSearchUsers({
                text: searchText || undefined,
                limit: this.pageSize,
                offset: offset,
            }),
            this._organizationsService.index().pipe(map((res) => res.data)),
        ]).pipe(
            map(([searchResult, organizations]) => ({
                users: searchResult.data,
                organizations,
                total: searchResult.metadata?.pagination?.total || 0,
            })),
            takeUntilDestroyed(this._destroyRef)
        );
    }

    private _updateDataSource(users: AdminSearchUsersUserDto[], _allOrganizations: Organization[]): void {
        this.dataSource.data = users;
        this.isLoading.set(false);
        this._spinnerService.hide();
    }

    displayOrganizationListModal(organizations: Organization[]): void {
        this._customDialogService.open(OrganizationsListModalComponent, {
            disableClose: false,
            data: {
                organizations,
            },
        });
    }

    displayRestaurantsListModal(user: AdminSearchUsersUserDto): void {
        this._customDialogService.open(RestaurantsListModalComponent, {
            disableClose: false,
            data: {
                restaurants: user.restaurants.map(({ restaurantId, restaurantDetails }) => ({
                    _id: restaurantId,
                    name: restaurantDetails.name,
                })),
            },
        });
    }

    updateActive(userId: string, active: boolean): void {
        this._spinnerService.show();
        this._adminService.updateAccount(userId, { verified: active, hasBeenDeactivatedByAdmin: !active }).subscribe({
            next: () => {
                this.refresh$.next();
            },
            error: (err) => {
                console.error('err :>> ', err);
                this._spinnerService.hide();
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }

    changePassword(userId: string): void {
        this._customDialogService
            .open(ChangePasswordComponent, {
                height: 'unset',
            })
            .afterClosed()
            .subscribe((newPassword: string | undefined) => {
                if (newPassword) {
                    this._spinnerService.show();
                    this._adminService.updateAccount(userId, { password: newPassword }).subscribe({
                        next: () => {
                            this.refresh$.next();
                        },
                        error: (err) => {
                            console.error('err :>> ', err);
                            this._spinnerService.hide();
                            this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                        },
                    });
                }
            });
    }

    upsert(upsertKind: UpsertKind, data?: AdminSearchUsersUserDto): void {
        this._customDialogService
            .open(UpsertUserModalComponent, {
                data: {
                    upsertKind,
                    user: data,
                    organizations: data?.organizations ?? [],
                },
            })
            .afterClosed()
            .subscribe((result: UserUpsertForm) => {
                if (!result) {
                    return;
                }
                this._spinnerService.show();

                const partialUser: Partial<User> = {
                    ...omit(result, ['organizations']),
                    organizationIds: result.organizations.map((organization) => organization._id),
                };

                const upsert$ =
                    upsertKind === UpsertKind.INSERT ? this._getCreateUser$(partialUser) : this._getUpdateUser$(data?._id, partialUser);
                upsert$.subscribe({
                    complete: () => {
                        this.refresh$.next();
                    },
                    error: (err) => {
                        console.error('err :>> ', err);
                        this._spinnerService.hide();
                        this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
                    },
                });
            });
    }

    private _getCreateUser$(partial: Partial<User>): Observable<User> {
        return this._adminService.createAccount(partial);
    }

    private _getUpdateUser$(userId: string | undefined, partial: Partial<User>): Observable<User | null> {
        if (!userId) {
            return of(null);
        }
        return forkJoin([
            this._adminService.updateAccount(userId, partial),
            this._store.select(selectUserInfos).pipe(take(1), filter(isNotNil)),
        ]).pipe(
            switchMap(([_, currentUser]) => {
                if (currentUser._id === userId) {
                    return this._usersService.getUser(userId);
                }
                return of(null);
            }),
            tap((user) => {
                if (user) {
                    this._store.dispatch(UserActions.editUserInfos({ infos: user }));
                }
            })
        );
    }

    impersonateUser(user: { _id: string; role: Role }): void {
        if (user.role === Role.ADMIN) {
            this._toastService.openErrorToast(this._translate.instant('roles.existing_user.cannot_impersonate_admin'));
            return;
        }

        this._spinnerService.show();
        this._adminService.impersonateUser(user._id).subscribe({
            next: (response) => {
                LocalStorage.setItem(LocalStorageKey.JWT_TOKEN, response.data.token);
                window.location.href = '/'; // Force reload to reset all states
            },
            error: (err) => {
                this._spinnerService.hide();
                this._toastService.openErrorToast(this._httpErrorPipe.transform(err));
            },
        });
    }
}
