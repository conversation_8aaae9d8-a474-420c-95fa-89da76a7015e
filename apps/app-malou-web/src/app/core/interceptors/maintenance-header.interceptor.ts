import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { MaintenanceMode } from ':core/maintenance/maintenance.service';
import { environment } from ':environments/environment';
import { editMaintenanceMode } from ':modules/admin/store/admin.actions';

@Injectable()
export class MaintenanceHeaderInterceptor implements HttpInterceptor {
    private _store: Store = inject(Store);
    constructor() {}

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        return next.handle(req).pipe(
            tap((event) => {
                if (event instanceof HttpResponse && req.url.startsWith(environment.APP_MALOU_API_URL)) {
                    const maintenanceHeader = event.headers.get('X-Maintenance-Status');
                    if (maintenanceHeader) {
                        try {
                            const maintenanceMode: MaintenanceMode = JSON.parse(maintenanceHeader);
                            this._store.dispatch(editMaintenanceMode({ maintenance: maintenanceMode }));
                        } catch (error) {
                            console.warn('Error parsing maintenance header:', error);
                        }
                    }
                }
            })
        );
    }
}
