import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { firstValueFrom, Observable } from 'rxjs';

import { GetProcessingMediaResponseDto, ProcessingMediaDto } from '@malou-io/package-dto';
import { ApiResultV2, ProcessingMediaStatus, retry, TimeInMilliseconds } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

@Injectable({
    providedIn: 'root',
})
export class ProcessingMediasService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/processing-medias`;

    private readonly _http = inject(HttpClient);

    private _getProcessingMedia(processingMediaId: string): Observable<ApiResultV2<GetProcessingMediaResponseDto>> {
        return this._http.get<ApiResultV2<GetProcessingMediaResponseDto>>(`${this.API_BASE_URL}/${processingMediaId}`);
    }

    async waitUntilEnded(processingMediaId: string): Promise<ProcessingMediaDto> {
        const result = await retry(() => firstValueFrom(this._getProcessingMedia(processingMediaId)), {
            attempts: 120,
            isSuccess: (res) => res.data.status !== ProcessingMediaStatus.IN_PROGRESS,
            backoffStrategy: (attempt) => 2 ** attempt * 50 * TimeInMilliseconds.MILLISECOND,
            minDelayInMs: 500 * TimeInMilliseconds.MILLISECOND,
            maxDelayInMs: TimeInMilliseconds.SECOND,
        });

        if (result.isErr()) {
            throw new Error(`ProcessingMediasService.waitUntilReady: network error (${result.error.error})`);
        }

        return result.value.data;
    }
}
