import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { MaintenanceStatus } from '@malou-io/package-utils';

import { environment } from ':environments/environment';
import { ApiResult } from ':shared/models';

export interface MaintenanceMode {
    status: MaintenanceStatus;
    until: Date;
    trespass: string[];
    redirect: boolean;
    webhookRedirectActive: boolean;
    localWebhookUri: string;
}

@Injectable({ providedIn: 'root' })
export class MaintenanceService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/`;

    constructor(private readonly _http: HttpClient) {}

    public setMaintenanceStatus(status: MaintenanceStatus): Observable<ApiResult<MaintenanceMode>> {
        return this._http.put<ApiResult<any>>(`${this.API_BASE_URL}maintenance/`, { status });
    }

    public updateMaintenance(data: Partial<MaintenanceMode>): Observable<ApiResult> {
        return this._http.put<ApiResult<any>>(`${this.API_BASE_URL}maintenance/`, { ...data });
    }
}
