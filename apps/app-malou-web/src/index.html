<!doctype html>
<html lang="en" translate="no">
    <head>
        <meta name="google" content="notranslate" />
        <!-- Google Tag Manager -->
        <script>
            if (window.location.href.includes('app.malou')) {
                (function (w, d, s, l, i) {
                    w[l] = w[l] || [];
                    w[l].push({ 'gtm.start': new Date().getTime(), event: 'gtm.js' });
                    var f = d.getElementsByTagName(s)[0],
                        j = d.createElement(s),
                        dl = l != 'dataLayer' ? '&l=' + l : '';
                    j.async = true;
                    j.src = 'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                    f.parentNode.insertBefore(j, f);
                })(window, document, 'script', 'dataLayer', 'GTM-WPCS4D3');
            }
        </script>
        <!-- End Google Tag Manager -->
        <meta charset="utf-8" />
        <title>MalouApp</title>
        <base href="/" />

        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />

        <!--favicons-->
        <link href="/assets/favicons/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
        <link type="image/png" href="/assets/favicons/favicon-32x32.png" rel="icon" sizes="32x32" />
        <link type="image/png" href="/assets/favicons/favicon-16x16.png" rel="icon" sizes="16x16" />
        <link href="/assets/favicons/site.webmanifest" rel="manifest" />
        <link href="/assets/favicons/safari-pinned-tab.svg" rel="mask-icon" color="#5bbad5" />
        <link href="/assets/favicons/favicon.ico" rel="shortcut icon" />
        <meta name="apple-mobile-web-app-title" content="Malou" />
        <meta name="application-name" content="Malou" />
        <meta name="msapplication-TileColor" content="#da532c" />
        <meta name="msapplication-config" content="/assets/favicons/browserconfig.xml" />
        <meta name="theme-color" content="#ffffff" />

        <!-- angular material icons -->
        <link
            href="https://fonts.googleapis.com/icon?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Round"
            rel="stylesheet" />
        <!-- SOME SCRIPT WITH API KEYS ARE INJECTED FROM environment.ts file -->
        <!-- https://github.com/skynet2/ngx-google-places-autocomplete -->

        <!-- Start Heap -->
        <script type="text/javascript">
            if (window.location.href.includes('app.malou')) {
                (window.heap = window.heap || []),
                    (heap.load = function (e, t) {
                        (window.heap.appid = e), (window.heap.config = t = t || {});
                        var r = document.createElement('script');
                        (r.type = 'text/javascript'), (r.async = !0), (r.src = 'https://cdn.heapanalytics.com/js/heap-' + e + '.js');
                        var a = document.getElementsByTagName('script')[0];
                        a.parentNode.insertBefore(r, a);
                        for (
                            var n = function (e) {
                                    return function () {
                                        heap.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                                    };
                                },
                                p = [
                                    'addEventProperties',
                                    'addUserProperties',
                                    'clearEventProperties',
                                    'identify',
                                    'resetIdentity',
                                    'removeEventProperty',
                                    'setEventProperties',
                                    'track',
                                    'unsetEventProperty',
                                ],
                                o = 0;
                            o < p.length;
                            o++
                        )
                            heap[p[o]] = n(p[o]);
                    });
                heap.load('2391935765');
            } else {
                window.heap = {};
            }
        </script>
        <!-- End Heap -->
        <link href="manifest.webmanifest" rel="manifest" />
        <meta name="theme-color" content="#1976d2" />
    </head>

    <body>
        <!-- Google Tag Manager (noscript) -->
        <noscript
            ><iframe
                src="https://www.googletagmanager.com/ns.html?id=GTM-WPCS4D3"
                height="0"
                width="0"
                style="display: none; visibility: hidden"></iframe
        ></noscript>
        <!-- End Google Tag Manager (noscript) -->
        <app-root></app-root>
        <noscript>Please enable JavaScript to continue using this application.</noscript>
    </body>

    <script type="text/javascript">
        if (!window.location.href.includes('localhost')) {
            var script = document.createElement('script');
            script.setAttribute('src', 'https://www.googletagmanager.com/gtag/js?id=UA-128193815-1');
            document.getElementsByTagName('body')[0].appendChild(script);
        }
    </script>

    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());
        gtag('config', 'UA-128193815-1');
    </script>
    <script src="https://chat-assets.frontapp.com/v1/chat.bundle.js"></script>
</html>
