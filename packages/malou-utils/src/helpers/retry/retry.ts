import { err, ok, Result } from 'neverthrow';

import { TimeInMilliseconds } from '../../constants';
import { waitFor } from '../../functions';

export enum RetryError {
    STILL_ERROR_AFTER_RETRIES = 'STILL_ERROR_AFTER_RETRIES',

    /** Returned if the predicate shouldRetryError has returned false */
    SHOULD_NOT_RETRY_AFTER_ERROR = 'SHOULD_NOT_RETRY_AFTER_ERROR',

    /** Returned if the predicate shouldRetrySuccess has returned false */
    SHOULD_NOT_RETRY_AFTER_SUCCESS = 'SHOULD_NOT_RETRY_AFTER_SUCCESS',
}

export type RetryErrorObject<R, E> =
    | { error: RetryError.STILL_ERROR_AFTER_RETRIES; lastResult: Result<R, E> }
    | { error: RetryError.SHOULD_NOT_RETRY_AFTER_ERROR; originalError: E }
    | { error: RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS; originalValue: R };

const BackoffStrategy = {
    CONSTANT: 'constant',
    LINEAR: 'linear',
    EXPONENTIAL: 'exponential',
} as const;
type IBackoffStrategy = (typeof BackoffStrategy)[keyof typeof BackoffStrategy];

/** Returns an amount of millisecond. */
type BackoffStrategyFn = (attempt: number) => number;

export interface Options<R, E> {
    attempts: number;
    backoffStrategy: IBackoffStrategy | BackoffStrategyFn;
    isSuccess: (_res: R, _attempt: number) => boolean;

    /** If this predicate returns false, `retryResult` fails with RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS */
    shouldRetrySuccess: (_res: R, _attempt: number) => boolean;

    /** If this predicate returns false, `retryResult` fails with RetryError.SHOULD_NOT_RETRY_AFTER_ERROR */
    shouldRetryError: (_error: E, _attempt: number) => boolean;

    minDelayInMs: TimeInMilliseconds;
    maxDelayInMs: TimeInMilliseconds;
}

const defaultOptions: Options<unknown, unknown> = {
    attempts: 3,
    backoffStrategy: BackoffStrategy.LINEAR,
    isSuccess: () => true,
    shouldRetrySuccess: () => true,
    shouldRetryError: () => true,
    minDelayInMs: 1 * TimeInMilliseconds.SECOND,
    maxDelayInMs: 1 * TimeInMilliseconds.MINUTE,
};

const defaultBackoffStrategyFns: Record<IBackoffStrategy, BackoffStrategyFn> = {
    [BackoffStrategy.CONSTANT]: () => 5 * TimeInMilliseconds.SECOND,
    [BackoffStrategy.LINEAR]: (attempt) => attempt * 5 * TimeInMilliseconds.SECOND,
    // eslint-disable-next-line no-mixed-operators
    [BackoffStrategy.EXPONENTIAL]: (attempt) => 2 ** attempt * TimeInMilliseconds.SECOND,
};

function computeDelay(
    attempt: number,
    backoffStrategy: IBackoffStrategy | BackoffStrategyFn,
    minDelayInMs: number,
    maxDelayInMs: number
): TimeInMilliseconds {
    const isCustomFn = typeof backoffStrategy === 'function';
    const res = isCustomFn ? backoffStrategy(attempt) : defaultBackoffStrategyFns[backoffStrategy](attempt);
    return Math.max(Math.min(res, minDelayInMs), maxDelayInMs);
}

/**
 * Try (and retry) the given Fn with extensive configuration.
 *
 * This version expects a Fn that return a Result type from 'neverthow' (and obviously that do not throw).
 * Thus, the Error becomes typed.
 *
 * Default options: check defaultOptions object
 */
export async function retryResult<R, E>(
    fn: () => Promise<Result<R, E>>,
    optionsParam?: Partial<Options<NoInfer<R>, NoInfer<E>>>
): Promise<Result<R, RetryErrorObject<R, E>>> {
    const options: Options<R, E> = { ...defaultOptions, ...(optionsParam ?? {}) };
    if (options.attempts < 1) {
        throw new Error('attempts must be greater than 0');
    }
    let res: Result<R, E> | undefined;
    for (let attempt = 1; attempt <= options.attempts; attempt++) {
        res = await fn();
        if (res.isErr()) {
            const shouldRetryError = options.shouldRetryError(res.error, attempt);
            if (!shouldRetryError) {
                return err({ error: RetryError.SHOULD_NOT_RETRY_AFTER_ERROR, originalError: res.error });
            }
        } else {
            const isSuccess = options.isSuccess(res.value, attempt);
            if (isSuccess) {
                return ok(res.value);
            }
            const shouldRetrySuccess = options.shouldRetrySuccess(res.value, attempt);
            if (!shouldRetrySuccess) {
                return err({ error: RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS, originalValue: res.value });
            }
        }
        const delay = computeDelay(attempt, options.backoffStrategy, options.minDelayInMs, options.maxDelayInMs);
        if (attempt + 1 <= options.attempts) {
            await waitFor(delay);
        }
    }
    if (!res) {
        throw new Error('res is set because the loop iterates at least once because options.attempts >= 1');
    }
    return err({ error: RetryError.STILL_ERROR_AFTER_RETRIES, lastResult: res });
}

/**
 * Try (and retry) the given Fn with extensive configuration.
 *
 * This version expects a Fn that can throw.
 * The Error type is 'unknown'.
 *
 * Default options: check defaultOptions object
 */
export async function retry<R>(
    fn: () => Promise<R>,
    optionsParam?: Partial<Options<NoInfer<R>, unknown>>
): Promise<Result<R, RetryErrorObject<R, unknown>>> {
    const options: Options<R, unknown> = { ...defaultOptions, ...(optionsParam ?? {}) };
    let lastError: unknown;
    for (let attempt = 1; attempt <= options.attempts; attempt++) {
        try {
            const res = await fn();
            const isSuccess = options.isSuccess(res, attempt);
            if (isSuccess) {
                return ok(res);
            }
            const shouldRetrySuccess = options.shouldRetrySuccess(res, attempt);
            if (!shouldRetrySuccess) {
                return err({ error: RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS, originalValue: res });
            }
        } catch (error: unknown) {
            lastError = error;
            const shouldRetryError = options.shouldRetryError(error, attempt);
            if (!shouldRetryError) {
                return err({ error: RetryError.SHOULD_NOT_RETRY_AFTER_ERROR, originalError: error });
            }
        }
        const delay = computeDelay(attempt, options.backoffStrategy, options.minDelayInMs, options.maxDelayInMs);
        if (attempt + 1 <= options.attempts) {
            await waitFor(delay);
        }
    }
    return err({ error: RetryError.STILL_ERROR_AFTER_RETRIES, lastResult: err(lastError) });
}
