import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { mentionJSONSchema } from './mention-schema';

const mentionSchema = createMongooseSchemaFromJSONSchema(mentionJSONSchema);

mentionSchema.virtual('restaurant', {
    ref: 'Restaurant',
    localField: 'restaurantId',
    foreignField: '_id',
    justOne: true,
});

mentionSchema.index({ restaurantId: 1, platformKey: 1 });
mentionSchema.index({ restaurantId: 1, mentionType: 1 });
mentionSchema.index({
    text: 'text',
    'replies.reviewer.displayName': 'text',
    'replies.text': 'text',
    'reviewer.displayName': 'text',
});
mentionSchema.index({ restaurantId: 1, socialId: 1 }, { unique: true, partialFilterExpression: { __t: 'CommentMention' } });
mentionSchema.index({ restaurantId: 1, socialId: 1, replies: 1 });

mentionSchema.index({
    restaurantId: 1,
    'post.socialId': 1,
    platformKey: 1,
    mentionType: 1,
    'post.replies': 1,
});

export type IMention = FromSchema<
    typeof mentionJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const MentionModel = mongoose.model<IMention>(mentionJSONSchema.title, mentionSchema);
