import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { processingMediasJSONSchema } from ':modules/processing-medias/processing-medias-schema';

const processingMediaSchema = createMongooseSchemaFromJSONSchema(processingMediasJSONSchema);

processingMediaSchema.index({
    mediaId: 1,
});

export type IProcessingMedia = FromSchema<
    typeof processingMediasJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const ProcessingMediaModel = mongoose.model<IProcessingMedia>(processingMediasJSONSchema.title, processingMediaSchema);
