import { ProcessingMediaErrorCode, ProcessingMediaStatus } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const processingMediasJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'ProcessingMedia',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        status: {
            enum: Object.values(ProcessingMediaStatus),
        },
        mediaId: {
            type: 'string',
            description: 'Only present if status === SUCCESS',
        },
        errorCode: {
            enum: Object.values(ProcessingMediaErrorCode),
            description: 'Only present if status === ERROR',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'status', 'createdAt'],
} as const satisfies JSONSchemaExtraProps;

export default processingMediasJSONSchema;
