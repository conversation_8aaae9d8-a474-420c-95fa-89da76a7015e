import { IsExact } from ':core/mongoose-json-schema/type-utils';
import { ISticker } from ':modules/nfcs/discriminators/stickers/sticker-model';
import { VirtualSticker } from ':modules/nfcs/discriminators/stickers/virtual-sticker';
import { ITotem } from ':modules/nfcs/discriminators/totems/totem-model';
import { VirtualTotem } from ':modules/nfcs/discriminators/totems/virtual-totem';
import { IOrganization } from ':modules/organizations/organization-model';
import { VirtualOrganization } from ':modules/organizations/virtual-organization';
import { IStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/store-locator-organization-config-model';
import { VirtualStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/virtual-store-locator-organization-config';

import { IApiKey } from './api-keys/api-key-model';
import { VirtualApiKey } from './api-keys/virtual-api-key';
import { IAttribute } from './attributes/attribute-model';
import { VirtualAttribute } from './attributes/attribute-virtual';
import { IAutomation } from './automations/automation-model';
import { IReviewReplyAutomation } from './automations/discriminators/review-reply-automation/review-reply-automation-model';
import { VirtualReviewReplyAutomation } from './automations/discriminators/review-reply-automation/virtual-review-reply-automation';
import { VirtualAutomation } from './automations/virtual-automation';
import { ICampaign } from './campaigns/campaign-model';
import { VirtualCampaign, VirtualContactInteractions } from './campaigns/virtual-campaign';
import { ICategory } from './categories/category-model';
import { VirtualCategory } from './categories/virtual-category';
import { IClient } from './clients/client-model';
import { VirtualClient } from './clients/virtual-client';
import { IComment } from './comments/comment-model';
import { VirtualComment } from './comments/virtual-comment';
import { IConversation } from './conversations/conversation-model';
import { VirtualConversation } from './conversations/virtual-conversation';
import { ICredential } from './credentials/credential-model';
import { VirtualCredential } from './credentials/virtual-credential';
import { ICustomer } from './customers/customer-model';
import { VirtualCustomer } from './customers/virtual-customer';
import { IDiagnostic } from './diagnostics';
import { VirtualDiagnostic, VirtualDiagnosticRestaurant } from './diagnostics/virtual-diagnostic';
import { IGiftDraw } from './gift-draws/gift-draw-model';
import { VirtualGiftDraw } from './gift-draws/virtual-gift-draw';
import { IGiftStock } from './gift-stocks/gift-stock-model';
import { VirtualGiftStock } from './gift-stocks/virtual-gift-stock';
import { IKeywordTemp } from './keywords-temp/keyword-model';
import { VirtualBricks, VirtualKeywordTemp } from './keywords-temp/virtual-keyword';
import { IKeyword } from './keywords/keyword-model';
import { VirtualKeyword } from './keywords/virtual-keyword';
import { ILabel } from './labels/label-model';
import { VirtualLabel } from './labels/virtual-label';
import { IMedia } from './media/media-model';
import { VirtualMedia } from './media/virtual-media';
import { IMention } from './mentions/mention-model';
import { VirtualMention } from './mentions/virtual-mention';
import { IMessage } from './messages/message-model';
import { VirtualMessage } from './messages/virtual-message';
import { INfc } from './nfcs/nfc-model';
import { VirtualNfc } from './nfcs/virtual-nfc';
import { INotification } from './notifications/notification-model';
import { VirtualNotification } from './notifications/virtual-notification';
import { IPlatformInsight } from './platform-insights/platform-insight-model';
import { VirtualPlatformInsight } from './platform-insights/virtual-platform-insight';
import { IPlatform } from './platforms/platform-model';
import { VirtualPlatform } from './platforms/virtual-platform';
import { IPost } from './posts/post-model';
import { VirtualPost } from './posts/virtual-post';
import { IPrivateReview } from './private-reviews/private-review-model';
import { VirtualPrivateReview } from './private-reviews/virtual-private-review';
import { IPushNotification } from './push-notifications/push-notification-model';
import { VirtualPushNotification } from './push-notifications/virtual-push-notification';
import { IReport } from './reports/report-model';
import { VirtualConfigurations, VirtualReport } from './reports/virtual-report';
import { IRestaurantAiSettings } from './restaurant-ai-settings/restaurant-ai-settings-model';
import { VirtualRestaurantAiSettings, VirtualTranslations } from './restaurant-ai-settings/virtual-restaurant-ai-settings';
import { IRestaurantAttribute } from './restaurant-attributes/restaurant-attribute-model';
import { VirtualRestaurantAttribute } from './restaurant-attributes/virtual-restaurant-attribute';
import { IRestaurantKeyword } from './restaurant-keywords/restaurant-keyword-model';
import { VirtualRestaurantKeyword } from './restaurant-keywords/virtual-restaurant-keyword';
import { IRestaurant } from './restaurants/restaurant-model';
import { VirtualRestaurant } from './restaurants/virtual-restaurant';
import { IReview } from './reviews/review-model';
import { VirtualReview } from './reviews/virtual-review';
import { IRoiInsights } from './roi-insights/roi-insights-model';
import { VirtualRoiInsights } from './roi-insights/virtual-roi-insights';
import { IRoiSettings } from './roi-settings/roi-settings-model';
import { VirtualRoiSettings } from './roi-settings/virtual-roi-settings';
import { IScan } from './scans/scan-model';
import { VirtualScan } from './scans/virtual-scan';
import { ISegmentAnalysis } from './segment-analyses/segment-analyses-model';
import { VirtualSegmentAnalysis } from './segment-analyses/virtual-segment-analyses';
import { ISegmentAnalysisParentTopics } from './segment-analysis-parent-topics/segment-analysis-parent-topics-model';
import { VirtualSegmentAnalysisParentTopics } from './segment-analysis-parent-topics/virtual-segment-analysis-parent-topics';
import { ITemplate } from './templates/template-model';
import { VirtualTemplate } from './templates/virtual-template';
import { IUserRestaurant } from './user-restaurants/user-restaurant-model';
import { VirtualUserRestaurant } from './user-restaurants/virtual-user-restaurant';
import { IUser } from './users/user-model';
import { VirtualReceiveMessagesNotifications, VirtualSettings, VirtualUser } from './users/virtual-user';
import { IAggregatedWheelOfFortune } from './wheels-of-fortune/discriminators/aggregated-wheels-of-fortune/aggregated-wheel-of-fortune-model';
import { IRestaurantWheelOfFortune } from './wheels-of-fortune/discriminators/restaurant-wheels-of-fortune/restaurant-wheel-of-fortune-model';
import { VirtualRestaurantWheelOfFortune } from './wheels-of-fortune/discriminators/restaurant-wheels-of-fortune/virtual-restaurant-wheel-of-fortune';
import { VirtualWheelOfFortune, VirtualWheelOfFortuneParameters } from './wheels-of-fortune/virtual-wheel-of-fortune';
import { IWheelOfFortune } from './wheels-of-fortune/wheel-of-fortune-model';

// it's literally a switch case
export type VirtualsGetter<TYPE_OF_THE_MODEL> =
    IsExact<TYPE_OF_THE_MODEL, IRestaurant> extends true
        ? VirtualRestaurant
        : IsExact<TYPE_OF_THE_MODEL, IUser> extends true
          ? VirtualUser
          : IsExact<TYPE_OF_THE_MODEL, IPlatform> extends true
            ? VirtualPlatform
            : IsExact<TYPE_OF_THE_MODEL, ICredential> extends true
              ? VirtualCredential
              : IsExact<TYPE_OF_THE_MODEL, IReview> extends true
                ? VirtualReview
                : IsExact<TYPE_OF_THE_MODEL, IOrganization> extends true
                  ? VirtualOrganization
                  : IsExact<TYPE_OF_THE_MODEL, IApiKey> extends true
                    ? VirtualApiKey
                    : IsExact<TYPE_OF_THE_MODEL, IAutomation> extends true
                      ? VirtualAutomation
                      : IsExact<TYPE_OF_THE_MODEL, IAttribute> extends true
                        ? VirtualAttribute
                        : IsExact<TYPE_OF_THE_MODEL, IReviewReplyAutomation> extends true
                          ? VirtualReviewReplyAutomation
                          : IsExact<TYPE_OF_THE_MODEL, ICampaign> extends true
                            ? VirtualCampaign
                            : IsExact<TYPE_OF_THE_MODEL, ICategory> extends true
                              ? VirtualCategory
                              : IsExact<TYPE_OF_THE_MODEL, IClient> extends true
                                ? VirtualClient
                                : IsExact<TYPE_OF_THE_MODEL, IComment> extends true
                                  ? VirtualComment
                                  : IsExact<TYPE_OF_THE_MODEL, IConversation> extends true
                                    ? VirtualConversation
                                    : IsExact<TYPE_OF_THE_MODEL, ICredential> extends true
                                      ? VirtualCredential
                                      : IsExact<TYPE_OF_THE_MODEL, ICustomer> extends true
                                        ? VirtualCustomer
                                        : IsExact<TYPE_OF_THE_MODEL, IKeyword> extends true
                                          ? VirtualKeyword
                                          : IsExact<TYPE_OF_THE_MODEL, IKeywordTemp> extends true
                                            ? VirtualKeywordTemp
                                            : IsExact<TYPE_OF_THE_MODEL, ILabel> extends true
                                              ? VirtualLabel
                                              : IsExact<TYPE_OF_THE_MODEL, IMedia> extends true
                                                ? VirtualMedia
                                                : IsExact<TYPE_OF_THE_MODEL, IMention> extends true
                                                  ? VirtualMention
                                                  : IsExact<TYPE_OF_THE_MODEL, IMessage> extends true
                                                    ? VirtualMessage
                                                    : IsExact<TYPE_OF_THE_MODEL, INfc> extends true
                                                      ? VirtualNfc
                                                      : IsExact<TYPE_OF_THE_MODEL, INotification> extends true
                                                        ? VirtualNotification
                                                        : IsExact<TYPE_OF_THE_MODEL, IPlatformInsight> extends true
                                                          ? VirtualPlatformInsight
                                                          : IsExact<TYPE_OF_THE_MODEL, IPost> extends true
                                                            ? VirtualPost
                                                            : IsExact<TYPE_OF_THE_MODEL, IPrivateReview> extends true
                                                              ? VirtualPrivateReview
                                                              : IsExact<TYPE_OF_THE_MODEL, IPushNotification> extends true
                                                                ? VirtualPushNotification
                                                                : IsExact<TYPE_OF_THE_MODEL, IReport> extends true
                                                                  ? VirtualReport
                                                                  : IsExact<TYPE_OF_THE_MODEL, IRestaurantAttribute> extends true
                                                                    ? VirtualRestaurantAttribute
                                                                    : IsExact<TYPE_OF_THE_MODEL, IScan> extends true
                                                                      ? VirtualScan
                                                                      : IsExact<TYPE_OF_THE_MODEL, ITemplate> extends true
                                                                        ? VirtualTemplate
                                                                        : IsExact<TYPE_OF_THE_MODEL, IUserRestaurant> extends true
                                                                          ? VirtualUserRestaurant
                                                                          : IsExact<TYPE_OF_THE_MODEL, IRoiSettings> extends true
                                                                            ? VirtualRoiSettings
                                                                            : IsExact<TYPE_OF_THE_MODEL, IUser['settings']> extends true
                                                                              ? VirtualSettings
                                                                              : IsExact<
                                                                                      TYPE_OF_THE_MODEL,
                                                                                      IUser['settings']['receiveMessagesNotifications']
                                                                                  > extends true
                                                                                ? VirtualReceiveMessagesNotifications
                                                                                : IsExact<
                                                                                        TYPE_OF_THE_MODEL,
                                                                                        ICampaign['contactInteractions'][0]
                                                                                    > extends true
                                                                                  ? VirtualContactInteractions
                                                                                  : IsExact<TYPE_OF_THE_MODEL, IWheelOfFortune> extends true
                                                                                    ? VirtualWheelOfFortune
                                                                                    : IsExact<
                                                                                            TYPE_OF_THE_MODEL,
                                                                                            IRestaurantWheelOfFortune
                                                                                        > extends true
                                                                                      ? VirtualRestaurantWheelOfFortune
                                                                                      : IsExact<
                                                                                              TYPE_OF_THE_MODEL,
                                                                                              IAggregatedWheelOfFortune
                                                                                          > extends true
                                                                                        ? // same as WheelOfFortune because they are the same
                                                                                          VirtualWheelOfFortune
                                                                                        : IsExact<
                                                                                                TYPE_OF_THE_MODEL,
                                                                                                IWheelOfFortune['parameters']
                                                                                            > extends true
                                                                                          ? VirtualWheelOfFortuneParameters
                                                                                          : IsExact<
                                                                                                  TYPE_OF_THE_MODEL,
                                                                                                  IGiftDraw
                                                                                              > extends true
                                                                                            ? VirtualGiftDraw
                                                                                            : IsExact<
                                                                                                    TYPE_OF_THE_MODEL,
                                                                                                    IReport['configurations'][0]
                                                                                                > extends true
                                                                                              ? VirtualConfigurations
                                                                                              : IsExact<
                                                                                                      TYPE_OF_THE_MODEL,
                                                                                                      IGiftStock
                                                                                                  > extends true
                                                                                                ? VirtualGiftStock
                                                                                                : IsExact<
                                                                                                        TYPE_OF_THE_MODEL,
                                                                                                        ITotem
                                                                                                    > extends true
                                                                                                  ? VirtualTotem
                                                                                                  : IsExact<
                                                                                                          TYPE_OF_THE_MODEL,
                                                                                                          ISticker
                                                                                                      > extends true
                                                                                                    ? VirtualSticker
                                                                                                    : IsExact<
                                                                                                            TYPE_OF_THE_MODEL,
                                                                                                            IRestaurantKeyword
                                                                                                        > extends true
                                                                                                      ? VirtualRestaurantKeyword
                                                                                                      : IsExact<
                                                                                                              TYPE_OF_THE_MODEL,
                                                                                                              IKeywordTemp['bricks'][0]
                                                                                                          > extends true
                                                                                                        ? VirtualBricks
                                                                                                        : IsExact<
                                                                                                                TYPE_OF_THE_MODEL,
                                                                                                                IRoiInsights
                                                                                                            > extends true
                                                                                                          ? VirtualRoiInsights
                                                                                                          : IsExact<
                                                                                                                  TYPE_OF_THE_MODEL,
                                                                                                                  ISegmentAnalysis
                                                                                                              > extends true
                                                                                                            ? VirtualSegmentAnalysis
                                                                                                            : IsExact<
                                                                                                                    TYPE_OF_THE_MODEL,
                                                                                                                    ISegmentAnalysisParentTopics
                                                                                                                > extends true
                                                                                                              ? VirtualSegmentAnalysisParentTopics
                                                                                                              : IsExact<
                                                                                                                      TYPE_OF_THE_MODEL,
                                                                                                                      IDiagnostic
                                                                                                                  > extends true
                                                                                                                ? VirtualDiagnostic
                                                                                                                : IsExact<
                                                                                                                        TYPE_OF_THE_MODEL,
                                                                                                                        IDiagnostic['restaurant']
                                                                                                                    > extends true
                                                                                                                  ? VirtualDiagnosticRestaurant
                                                                                                                  : IsExact<
                                                                                                                          TYPE_OF_THE_MODEL,
                                                                                                                          IRestaurantAiSettings
                                                                                                                      > extends true
                                                                                                                    ? VirtualRestaurantAiSettings
                                                                                                                    : IsExact<
                                                                                                                            TYPE_OF_THE_MODEL,
                                                                                                                            IRestaurantAiSettings['reviewSettings']
                                                                                                                        > extends true
                                                                                                                      ? VirtualTranslations
                                                                                                                      : IsExact<
                                                                                                                              TYPE_OF_THE_MODEL,
                                                                                                                              IStoreLocatorOrganizationConfig
                                                                                                                          > extends true
                                                                                                                        ? VirtualStoreLocatorOrganizationConfig
                                                                                                                        : unknown;
