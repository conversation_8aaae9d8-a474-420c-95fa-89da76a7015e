import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';

import { restaurantAttributeJSONSchema } from './restaurant-attribute-schema';

const restaurantAttributeSchema = createMongooseSchemaFromJSONSchema(restaurantAttributeJSONSchema);

restaurantAttributeSchema.virtual('attribute', {
    ref: 'Attribute',
    localField: 'attributeId',
    foreignField: '_id',
    justOne: true,
});

restaurantAttributeSchema.index({ restaurantId: 1 });
restaurantAttributeSchema.index({ attributeId: 1, restaurantId: 1 }, { unique: true });

restaurantAttributeSchema.post('save', (error: Error, doc: unknown, next: (error?: Error) => void): void => {
    if ('code' in error && error.code === 11000) {
        return next({ duplicateRecordError: true } as any); // FIXME
    }
    return next();
});

export type IRestaurantAttribute = FromSchema<
    typeof restaurantAttributeJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const RestaurantAttributeModel = mongoose.model<IRestaurantAttribute>(
    restaurantAttributeJSONSchema.title,
    restaurantAttributeSchema
);
