import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { storeLocatorOrganizationConfigJSONSchema } from ':modules/store-locator-organization-config/store-locator-organization-config-schema';

const storeLocatorOrganizationConfigSchema = createMongooseSchemaFromJSONSchema(storeLocatorOrganizationConfigJSONSchema);

storeLocatorOrganizationConfigSchema.index({ organizationId: 1 }, { unique: true });

storeLocatorOrganizationConfigSchema.virtual('organization', {
    ref: 'Organization',
    localField: 'organizationId',
    foreignField: '_id',
    justOne: true,
});

export type IStoreLocatorOrganizationConfig = FromSchema<
    typeof storeLocatorOrganizationConfigJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export const StoreLocatorOrganizationConfigModel = mongoose.model<IStoreLocatorOrganizationConfig>(
    storeLocatorOrganizationConfigJSONSchema.title,
    storeLocatorOrganizationConfigSchema
);
