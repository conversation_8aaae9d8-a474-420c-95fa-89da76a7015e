import { OverwriteOrAssign } from ':core/index';
import { IOrganization } from ':modules/organizations/organization-model';
import { IStoreLocatorOrganizationConfig } from ':modules/store-locator-organization-config/store-locator-organization-config-model';

export type IStoreLocatorOrganizationConfigWithOrganization = OverwriteOrAssign<
    IStoreLocatorOrganizationConfig,
    { organization: IOrganization }
>;
