name: Deploy Store Locator

on:
    workflow_dispatch:
        inputs:
            organizationId:
                description: 'Organization ID'
                required: true
                type: string
            environment:
                type: choice
                description: 'Environment'
                required: true
                default: 'test'
                options:
                    - 'test'
                    - 'production'

run-name: Deploy Store Locator (Org ${{ inputs.organizationId }})

env:
    GIT_PKG_ACCESS_TOKEN: ${{ secrets.PAT }}
    PNPM_STORE_PATH: ~/.pnpm-store

jobs:
    fetch-api:
        name: Fetch configuration from API
        runs-on: ubuntu-latest
        outputs:
            cloudfront_distribution_id: ${{ steps.get_api_data.outputs.cloudfront_distribution_id }}
            organization_name: ${{ steps.get_api_data.outputs.organization_name }}
            base_url: ${{ steps.get_api_data.outputs.base_url }}
            tailwind_config: ${{ steps.get_api_data.outputs.tailwind_config }}
        env:
            STORE_LOCATOR_CONFIGURATION_URL: https://app.api.malou.io/api/v1/store-locator/${{ inputs.organizationId }}/configuration
        steps:
            - name: Call API and parse response
              id: get_api_data
              run: |
                  # Call API
                  RESPONSE=$(curl -s "${{ env.STORE_LOCATOR_CONFIGURATION_URL }}?api_key=${{ secrets.MALOU_API_KEY }}")

                  # Parse response
                  CLOUDFRONT_DISTRIBUTION_ID=$(echo "$RESPONSE" | jq -r '.data.cloudfrontDistributionId')
                  ORGANIZATION_NAME=$(echo "$RESPONSE" | jq -r '.data.organizationName')
                  BASE_URL=$(echo "$RESPONSE" | jq -r '.data.baseUrl')

                  # Escape the tailwindConfig content to avoid issues with newlines and special characters
                  TAILWIND_CONFIG=$(echo "$RESPONSE" | jq -r '.data.tailwindConfig' | sed ':a;N;$!ba;s/\n/\\n/g')

                  # Export them as outputs
                  echo "cloudfront_distribution_id=$CLOUDFRONT_DISTRIBUTION_ID" >> $GITHUB_OUTPUT
                  echo "organization_name=$ORGANIZATION_NAME" >> $GITHUB_OUTPUT
                  echo "base_url=$BASE_URL" >> $GITHUB_OUTPUT
                  echo "tailwind_config=$TAILWIND_CONFIG" >> $GITHUB_OUTPUT

    deploy:
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache
        name: Deploy Store Locator
        needs: [fetch-api]
        outputs:
            status: ${{ job.status }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.ref_name }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install --filter @malou-io/store-locator...

            - name: Build dependencies
              run: pnpm run build --filter=@malou-io/store-locator... --cache-dir=.

            - name: Define deployment variables
              id: deployment-variables
              run: |
                  if [ "${{ inputs.environment }}" = "test" ]; then
                    S3_BUCKET_NAME="s3://store-locator-qa"
                    CLOUDFRONT_DISTRIBUTION_ID="E2H0RYJ95M33U7"
                  else
                    S3_BUCKET_NAME="s3://store-locator-${{ inputs.environment }}-org-${{ inputs.organizationId }}"
                    CLOUDFRONT_DISTRIBUTION_ID="${{ needs.fetch-api.outputs.cloudfront_distribution_id }}"
                  fi

                  echo "s3_bucket_name=$S3_BUCKET_NAME" >> $GITHUB_OUTPUT
                  echo "cloudfront_distribution_id=$CLOUDFRONT_DISTRIBUTION_ID" >> $GITHUB_OUTPUT

            - name: Set CSS configuration
              working-directory: apps/store-locator
              run: |
                  mkdir -p ./src/styles
                  echo "${{ needs.fetch-api.outputs.tailwind_config }}" | sed 's/\\n/\n/g' > ./src/styles/global.css
                  # Verify file was created
                  echo "Generated global.css with content:"
                  cat ./src/styles/global.css

            - name: Build Astro pages
              run: pnpm run deploy
              working-directory: apps/store-locator
              env:
                  ORGANIZATION_ID: ${{ inputs.organizationId }}
                  ORGANIZATION_BASE_URL: ${{ needs.fetch-api.outputs.base_url }}
                  PUBLIC_API_BASE_URL: https://app.api.malou.io/api/v1
                  API_KEY: ${{ secrets.MALOU_API_KEY }}

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: eu-west-3

            - name: Deploy to S3
              working-directory: apps/store-locator
              run: |
                  aws s3 sync ./dist ${{ steps.deployment-variables.outputs.s3_bucket_name }} --acl public-read --follow-symlinks --delete

            - name: Create AWS Cloudfront invalidation
              run: |
                  aws cloudfront create-invalidation --distribution-id ${{ steps.deployment-variables.outputs.cloudfront_distribution_id }} --paths "/*"

    slack-notification:
        needs: [fetch-api, deploy]
        if: always()
        name: Send Slack notification
        runs-on: ubuntu-latest
        steps:
            - name: Set Slack channel ID
              id: get-slack-channel-id
              run: |
                  if [ "${{ inputs.environment }}" = "production" ]; then
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID_PROD }}" >> $GITHUB_OUTPUT
                  else
                    echo "SLACK_CHANNEL_ID=${{ secrets.STORE_LOCATOR_SLACK_CHANNEL_ID }}" >> $GITHUB_OUTPUT
                  fi

            - name: Send notification
              uses: slackapi/slack-github-action@v2.0.0
              with:
                  method: chat.postMessage
                  token: ${{ secrets.SLACK_BOT_TOKEN }}
                  payload: |
                      channel: ${{ steps.get-slack-channel-id.outputs.SLACK_CHANNEL_ID }}
                      attachments:
                        - color: ${{ needs.deploy.result == 'success' && '"#2eb886"' || (needs.deploy.result == 'failure' && '"#e01e5a"' || '"#cccccc"') }}
                          blocks:
                            - type: section
                              text:
                                type: mrkdwn
                                text: "*🚀 Deploy status: ${{ needs.deploy.result == 'success' && '✅ Success' || needs.deploy.result == 'failure' && '❌ Failure' || needs.deploy.result == 'cancelled' && '⚪ Cancelled' || 'Unknown' }}*"

                            - type: context
                              elements:
                                - type: mrkdwn
                                  text: "Triggered for: Organization `${{ needs.fetch-api.outputs.organization_name }}` | ID `${{ inputs.organizationId }}` | Environment: `${{ inputs.environment }}` | Branch: `${{ github.ref_name }}`"


                                - type: mrkdwn
                                  text: "<${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow Run>"
