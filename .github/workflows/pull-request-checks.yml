name: Pull request checks
on:
    pull_request:
        types: [ready_for_review]

run-name: Pull request checks - ${{ github.run_id }}

env:
    GIT_PKG_ACCESS_TOKEN: ${{ secrets.PAT }}
    PNPM_STORE_PATH: ~/.pnpm-store
    TOTAL_SHARDS_E2E: 5
    TOTAL_SHARDS_UNIT: 3
    ELASTICMQ_PORT: 9324
    REDIS_PORT: 6379
    MONGO_PORT: 27017

concurrency:
    group: ${{ github.workflow }}-${{ github.ref }}
    cancel-in-progress: true

jobs:
    check-format:
        name: Check format
        runs-on: runs-on=${{ github.run_id }}/runner=2cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # We run the format check only on the files that have changed in the PR
            # We don't care about the dependencies, we only need to run it in every changed folder
            # No dependsOn in turbo.json so scripts will run in //
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Check format
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run format:check --cache-dir=.turbo --filter=\[origin/${{ github.event.pull_request.base.ref }}\]

    check-lint:
        name: Check lint
        runs-on: ubuntu-latest

        steps:
            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # We run the lint check only on the files that have changed in the PR
            # We don't care about the dependencies, we only need to run it in every changed folder
            # No dependsOn in turbo.json so scripts will run in //
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Check lint
              env:
                  NODE_OPTIONS: '--max_old_space_size=4096'
              run: pnpm run lint --cache-dir=.turbo --filter=\[origin/${{ github.event.pull_request.base.ref }}\]

    build:
        name: Check build
        runs-on: runs-on=${{ github.run_id }}/runner=8cpu-custom/extras=s3-cache

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            # Restore cache only
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install

            # If package/utils changed, then we have to run build in package-utils and all its dependent packages / apps
            # If several dependent packages / apps changed, turborepo is smart enough to only run the tasks once for each package / app
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Run build
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: pnpm run build --cache-dir=.turbo

            # actions/cache@v4 won't save the cache afterwards if there's a cache hit
            # But we want to save it no matter what, so we have to do it manually, by using restore then save
            - name: Turbo cache save
              uses: actions/cache/save@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}

    setup-unit:
        name: Setup unit
        runs-on: ubuntu-latest
        outputs:
            shard_matrix: ${{ steps.generate-matrix.outputs.matrix }}
            shard_total: ${{ steps.generate-matrix.outputs.total }}
        steps:
            - name: Generate Shard Matrix
              id: generate-matrix
              run: |
                  # Get the total number of shards from the environment
                  TOTAL_SHARDS_UNIT=${{ env.TOTAL_SHARDS_UNIT }}

                  # Generate an array of shard indices dynamically
                  # MATRIX = [1, 2, 3, 4, 5] for example
                  # SHARD_TOTAL = [5]
                  MATRIX=$(seq 1 $TOTAL_SHARDS_UNIT | jq -R . | jq -s . | tr -d '[:space:]')
                  SHARD_TOTAL="[${TOTAL_SHARDS_UNIT}]"
                  echo "matrix=$MATRIX"
                  echo "total=$SHARD_TOTAL"

                  # Output the matrix as a string
                  echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
                  echo "total=$SHARD_TOTAL" >> $GITHUB_OUTPUT

    unit-test:
        name: Check unit tests
        # We could run this job in parallel with the build job, but by waiting for it, we will take advantage of the cache for the build step
        needs: [setup-unit, build]
        runs-on: runs-on=${{ github.run_id }}/runner=8cpu-custom/extras=s3-cache
        strategy:
            fail-fast: false
            matrix:
                shardIndex: ${{ fromJson(needs.setup-unit.outputs.shard_matrix) }}
                shardTotal: ${{ fromJson(needs.setup-unit.outputs.shard_total) }}

        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - name: Checkout repository
              uses: actions/checkout@v4
              with:
                  fetch-depth: 0

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            # https://github.com/vercel/turborepo/pull/2761/files
            # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            - name: Turbo caching
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Run build
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: pnpm run build --cache-dir=.turbo

            - name: Set env variables
              run: |
                  touch apps/app-malou-api/.env.jest.tests
                  printf ${{ secrets.TESTS_ENV_FILE }} | base64 --decode > apps/app-malou-api/.env.jest.tests

            # If package/utils changed, then we have to run all unit tests in package-utils and all its dependent packages / apps
            # If several dependent packages / apps changed, turborepo is smart enough to only run the tasks once for each package / app
            # https://turbo.build/repo/docs/reference/run#advanced-filtering-examples
            - name: Run unit tests
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: pnpm run test:unit --filter=...\[origin/${{ github.event.pull_request.base.ref }}\] -- --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --passWithNoTests

    setup:
        name: Setup playwright
        runs-on: ubuntu-latest
        outputs:
            shard_matrix: ${{ steps.generate-matrix.outputs.matrix }}
            shard_total: ${{ steps.generate-matrix.outputs.total }}
        steps:
            - name: Generate Shard Matrix
              id: generate-matrix
              run: |
                  # Get the total number of shards from the environment
                  TOTAL_SHARDS_E2E=${{ env.TOTAL_SHARDS_E2E}}

                  # Generate an array of shard indices dynamically
                  # MATRIX = [1, 2, 3, 5]
                  # SHARD_TOTAL = [5]
                  MATRIX=$(seq 1 $TOTAL_SHARDS_E2E | jq -R . | jq -s . | tr -d '[:space:]')
                  SHARD_TOTAL="[${TOTAL_SHARDS_E2E}]"
                  echo "matrix=$MATRIX"
                  echo "total=$SHARD_TOTAL"

                  # Output the matrix as a string
                  echo "matrix=$MATRIX" >> $GITHUB_OUTPUT
                  echo "total=$SHARD_TOTAL" >> $GITHUB_OUTPUT

    playwright-tests:
        name: Playwright tests
        needs: [setup, build]
        timeout-minutes: 10
        runs-on: runs-on=${{ github.run_id }}/runner=16cpu-custom/extras=s3-cache
        strategy:
            fail-fast: false
            matrix:
                shardIndex: ${{ fromJson(needs.setup.outputs.shard_matrix) }}
                shardTotal: ${{ fromJson(needs.setup.outputs.shard_total) }}
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses:
                  ./.github/actions/setup-node-and-pnpm

                  #         # https://github.com/vercel/turborepo/pull/2761/files
            #         # https://github.com/austinwoon/turbo-repo-cache-with-github-actions/tree/master
            - name: Restore cache
              uses: actions/cache/restore@v4
              with:
                  path: .turbo
                  key: ${{ runner.os }}-turbo-${{ github.event.pull_request.head.ref }}
                  restore-keys: |
                      ${{ runner.os }}-turbo-${{ github.event.pull_request.base.ref }}
                      ${{ runner.os }}-turbo-main
                      ${{ runner.os }}-turbo-

            - name: Install dependencies
              run: pnpm install --frozen-lockfile

            - name: Run build
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: pnpm run build --cache-dir=.turbo

            - name: Set env variables
              id: set-env
              run: |
                  touch apps/app-malou-e2e/.env
                  printf ${{ secrets.E2E_TESTS_ENV_FILE }} | base64 --decode > apps/app-malou-e2e/.env

            - name: Set env variables backend
              id: set-env-backend
              run: |
                  touch apps/app-malou-api/dist/.env.local
                  printf ${{ secrets.E2E_TESTS_BACKEND_ENV_FILE }} | base64 --decode > apps/app-malou-api/dist/.env.local

            - name: Start backend services
              working-directory: apps/app-malou-api
              run: docker compose up -d --remove-orphans

            - name: Start backend api
              working-directory: apps/app-malou-api
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: NODE_ENV=local pnpm run start &

            - name: Start backend worker
              working-directory: apps/app-malou-api
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: NODE_ENV=local pnpm run start-workers &

            - name: Start frontend
              working-directory: apps/app-malou-web/dist
              env:
                  NODE_OPTIONS: '--max_old_space_size=8192'
              run: npx --yes serve -l 4200 -s . & # Faster than ng serve

            - name: Install Playwright Browsers
              working-directory: apps/app-malou-e2e
              run: pnpm exec playwright install chromium

            - name: Wait for backend to be ready
              run: |
                  while ! curl http://localhost:3000 > /dev/null; do
                  echo "Waiting for backend..."
                  sleep 5
                  done

            - name: Wait for frontend to be ready
              run: |
                  while ! curl -s http://localhost:4200 > /dev/null; do
                  echo "Waiting for frontend..."
                  sleep 5
                  done

            - name: Run Playwright tests
              working-directory: apps/app-malou-e2e
              env:
                  TESTING_TARGET: local_dev # This flow will use local environment. API is connected to target database (development). Playwright sets up the development database.
                  MONGODB_URI_DEVELOPMENT: ${{ secrets.E2E_TESTS_MONGODB_URI }} # This is the target database for the tests
                  DEBUG: pw:api
                  PLAYWRIGHT_WORKER_COUNT: 2
              run: |
                  pnpm exec playwright test --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }} --trace on > test_output.txt || true

                  if [ ! -s test_output.txt ]; then
                  echo "0 passed" > test_output.txt
                  echo "0 failed" >> test_output.txt
                  echo "0 flaky" >> test_output.txt
                  fi

                  passed=$(grep -Eo '([0-9]+) passed' test_output.txt | awk '{print $1}')
                  failed=$(grep -Eo '([0-9]+) failed' test_output.txt | awk '{print $1}')
                  flaky=$(grep -Eo '([0-9]+) flaky' test_output.txt | awk '{print $1}')

                  passed=${passed:-0}
                  failed=${failed:-0}
                  flaky=${flaky:-0}

                  echo "Passed: $passed, Failed: $failed, Flaky: $flaky"

                  # Store results in a file
                  echo "$failed" > results-failed-${{ matrix.shardIndex }}.txt

            - name: Upload blob report to GitHub Actions Artifacts
              if: ${{ !cancelled() }}
              uses: actions/upload-artifact@v4
              with:
                  name: blob-report-${{ matrix.shardIndex }}
                  path: apps/app-malou-e2e/blob-report
                  retention-days: 1

            - name: Upload failed test results
              if: ${{ !cancelled() }}
              uses: actions/upload-artifact@v4
              with:
                  name: test-results-${{ matrix.shardIndex }}
                  path: |
                      apps/app-malou-e2e/results-failed-${{ matrix.shardIndex }}.txt
                  retention-days: 1

    merge-reports:
        name: Merge reports
        if: ${{ !cancelled() }}
        needs: [playwright-tests]
        runs-on: runs-on=${{ github.run_id }}/runner=2cpu-custom/extras=s3-cache
        steps:
            # Necessary to fetch runsOn S3 cache
            - uses: runs-on/action@v1

            - uses: actions/checkout@v4

            - name: Setup Node and PNPM
              uses: ./.github/actions/setup-node-and-pnpm

            - name: Install dependencies
              run: pnpm install --filter @malou-io/app-e2e... --frozen-lockfile

            - name: Download blob reports from GitHub Actions Artifacts
              uses: actions/download-artifact@v4
              with:
                  path: apps/app-malou-e2e/all-blob-reports
                  pattern: blob-report-*
                  merge-multiple: true

            # Use cache instead of artifacts to speed up the process
            - name: Download test results
              uses: actions/download-artifact@v4
              with:
                  pattern: test-results-*

            - name: Aggregate test results
              run: |
                  total_failed=0

                  for shard in {1..${{ env.TOTAL_SHARDS_E2E }}}; do
                      failed=$(cat test-results-$shard/results-failed-$shard.txt)

                      total_failed=$((total_failed + failed))
                  done

                  echo "Total Failed: $total_failed"

                  # Store totals in environment variables
                  echo "TOTAL_FAILED=$total_failed" >> $GITHUB_ENV

            - name: Merge into HTML Report
              working-directory: apps/app-malou-e2e
              run: pnpm exec playwright merge-reports --reporter html ./all-blob-reports

            - name: Configure AWS Credentials
              uses: aws-actions/configure-aws-credentials@v4
              if: always()
              with:
                  aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
                  aws-region: 'eu-west-3'

            - name: Upload to S3 bucket
              id: S3
              if: always()
              env:
                  REPORT_DIR: 'playwright-report-${{ github.run_id }}'
              run: |
                  echo "REPORT_DIR=$REPORT_DIR" >> $GITHUB_ENV
                  aws s3 cp apps/app-malou-e2e/playwright-report/. s3://malou-playwright-traces/$REPORT_DIR --recursive

            - name: Generate Report URL
              id: generate_report_url
              run: echo "::set-output name=report_url::https://djj1pfvigd2g9.cloudfront.net/${{ env.REPORT_DIR }}/index.html"

            - name: Setup Job Summary
              if: always()
              run: |
                  echo " 🔗 [View Playwright Report](${{ steps.generate_report_url.outputs.report_url }})" >> $GITHUB_STEP_SUMMARY

            - name: Slack notification
              if: always()
              id: slack
              uses: slackapi/slack-github-action@v1.27.0
              with:
                  payload: |
                      {
                          "reportUrl": "${{ steps.generate_report_url.outputs.report_url }}",
                          "env": "${{ github.event.schedule && 'production' || inputs.apps }}",
                          "totalFailed": "${{ env.TOTAL_FAILED }}",
                          "branch": "${{ github.event.pull_request.head.ref || github.ref_name }}"
                      }
              env:
                  SLACK_WEBHOOK_URL: 'https://hooks.slack.com/triggers/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'
